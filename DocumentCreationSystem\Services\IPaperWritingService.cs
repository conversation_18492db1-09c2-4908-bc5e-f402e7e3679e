using System;
using System.Threading;
using System.Threading.Tasks;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 论文写作服务接口
    /// </summary>
    public interface IPaperWritingService
    {
        /// <summary>
        /// 分析素材文件
        /// </summary>
        /// <param name="materialFiles">素材文件路径列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>素材分析结果</returns>
        Task<MaterialAnalysisResult> AnalyzeMaterialsAsync(
            System.Collections.Generic.List<string> materialFiles, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成论文大纲
        /// </summary>
        /// <param name="request">论文生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>论文大纲</returns>
        Task<PaperOutline> GenerateOutlineAsync(
            PaperGenerationRequest request, 
            MaterialAnalysisResult materialAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成论文标题
        /// </summary>
        /// <param name="request">论文生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>论文标题</returns>
        Task<string> GenerateTitleAsync(
            PaperGenerationRequest request, 
            MaterialAnalysisResult materialAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成论文章节内容
        /// </summary>
        /// <param name="sectionOutline">章节大纲</param>
        /// <param name="request">论文生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="previousSections">前面已生成的章节</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>章节内容</returns>
        Task<PaperSection> GenerateSectionAsync(
            SectionOutline sectionOutline,
            PaperGenerationRequest request,
            MaterialAnalysisResult materialAnalysis,
            System.Collections.Generic.List<PaperSection> previousSections,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成完整论文
        /// </summary>
        /// <param name="request">论文生成请求</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>论文生成结果</returns>
        Task<PaperGenerationResult> GeneratePaperAsync(
            PaperGenerationRequest request,
            IProgress<string>? progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 格式化论文内容
        /// </summary>
        /// <param name="sections">论文章节列表</param>
        /// <param name="request">论文生成请求</param>
        /// <param name="title">论文标题</param>
        /// <returns>格式化后的论文内容</returns>
        string FormatPaperContent(
            System.Collections.Generic.List<PaperSection> sections, 
            PaperGenerationRequest request, 
            string title);

        /// <summary>
        /// 验证论文质量
        /// </summary>
        /// <param name="content">论文内容</param>
        /// <param name="request">论文生成请求</param>
        /// <returns>质量评分 (0-100)</returns>
        Task<int> ValidatePaperQualityAsync(string content, PaperGenerationRequest request);
    }
}

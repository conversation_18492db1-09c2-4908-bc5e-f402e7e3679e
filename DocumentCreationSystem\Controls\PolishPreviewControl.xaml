<UserControl x:Class="DocumentCreationSystem.Controls.PolishPreviewControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    
    <UserControl.Resources>
        <!-- 润色预览样式 -->
        <Style x:Key="PolishPreviewStyle" TargetType="Border">
            <Setter Property="Background" Value="#FFE6F3FF"/>
            <Setter Property="BorderBrush" Value="#FF4A90E2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
        
        <!-- 悬浮控件样式 -->
        <Style x:Key="FloatingControlStyle" TargetType="materialDesign:Card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp8"/>
            <Setter Property="Padding" Value="8"/>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <!-- 润色预览容器 -->
        <Border x:Name="PreviewContainer" 
                Style="{StaticResource PolishPreviewStyle}"
                Visibility="Collapsed">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 标题栏 -->
                <DockPanel Grid.Row="0" Margin="0,0,0,8">
                    <materialDesign:PackIcon Kind="AutoFix" DockPanel.Dock="Left"
                                           VerticalAlignment="Center" Margin="0,0,8,0"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="润色预览" DockPanel.Dock="Left"
                             Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                             VerticalAlignment="Center" FontWeight="Medium"/>
                    <Button x:Name="CloseButton" DockPanel.Dock="Right"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="24" Height="24" Padding="0"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </DockPanel>
                
                <!-- 内容区域 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 原文显示 -->
                        <TextBlock Text="原文：" FontWeight="Medium" Margin="0,0,0,4"/>
                        <Border Background="#FFF5F5F5" BorderBrush="#FFCCCCCC" 
                                BorderThickness="1" CornerRadius="2" Padding="8" Margin="0,0,0,8">
                            <TextBlock x:Name="OriginalTextBlock" TextWrapping="Wrap"
                                     Foreground="#FF666666"/>
                        </Border>
                        
                        <!-- 润色选项 -->
                        <Expander Header="润色选项" Margin="0,0,0,8" IsExpanded="False">
                            <StackPanel Margin="8">
                                <TextBlock Text="润色类型：" FontWeight="Medium" Margin="0,0,0,4"/>
                                <ComboBox x:Name="PolishTypeComboBox" Margin="0,0,0,8">
                                    <ComboBoxItem Content="语言润色" IsSelected="True"/>
                                    <ComboBoxItem Content="情感增强"/>
                                    <ComboBoxItem Content="描述丰富"/>
                                    <ComboBoxItem Content="对话优化"/>
                                    <ComboBoxItem Content="节奏调整"/>
                                    <ComboBoxItem Content="风格统一"/>
                                </ComboBox>

                                <TextBlock Text="润色强度：" FontWeight="Medium" Margin="0,8,0,4"/>
                                <Slider x:Name="PolishIntensitySlider" Minimum="1" Maximum="5" Value="3"
                                        TickFrequency="1" TickPlacement="BottomRight" Margin="0,0,0,4"/>
                                <TextBlock Text="{Binding ElementName=PolishIntensitySlider, Path=Value, StringFormat=强度: {0}}"
                                         FontSize="12" Opacity="0.7"/>

                                <Button Content="重新润色" Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Margin="0,8,0,0" Click="RePolish_Click"/>
                            </StackPanel>
                        </Expander>

                        <!-- 润色后文本显示 -->
                        <DockPanel Margin="0,0,0,4">
                            <TextBlock Text="润色后：" FontWeight="Medium" DockPanel.Dock="Left"/>
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                <TextBlock x:Name="WordCountChangeText" FontSize="12" Opacity="0.7"
                                         VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <Button x:Name="CompareButton" Content="对比模式"
                                        Style="{StaticResource MaterialDesignFlatButton}"
                                        FontSize="12" Padding="4,2" Click="Compare_Click"/>
                            </StackPanel>
                        </DockPanel>

                        <Border Background="#FFE8F5E8" BorderBrush="#FF4CAF50"
                                BorderThickness="1" CornerRadius="2" Padding="8" Margin="0,0,0,12">
                            <TextBlock x:Name="PolishedTextBlock" TextWrapping="Wrap"
                                     Foreground="#FF2E7D32"/>
                        </Border>

                        <!-- 对比模式显示 -->
                        <Grid x:Name="ComparisonGrid" Visibility="Collapsed" Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Border Grid.Column="0" Background="#FFF5F5F5" BorderBrush="#FFCCCCCC"
                                    BorderThickness="1" CornerRadius="2" Padding="8">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                                    <TextBlock x:Name="CompareOriginalText" TextWrapping="Wrap"
                                             Foreground="#FF666666"/>
                                </ScrollViewer>
                            </Border>

                            <Rectangle Grid.Column="1" Fill="#FFDDDDDD"/>

                            <Border Grid.Column="2" Background="#FFE8F5E8" BorderBrush="#FF4CAF50"
                                    BorderThickness="1" CornerRadius="2" Padding="8">
                                <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                                    <TextBlock x:Name="ComparePolishedText" TextWrapping="Wrap"
                                             Foreground="#FF2E7D32"/>
                                </ScrollViewer>
                            </Border>
                        </Grid>

                        <!-- 润色说明 -->
                        <Expander x:Name="ExplanationExpander" Header="润色说明" Margin="0,0,0,8" IsExpanded="False">
                            <Border Background="#FFF9F9F9" BorderBrush="#FFEEEEEE"
                                    BorderThickness="1" CornerRadius="2" Padding="8" Margin="8">
                                <TextBlock x:Name="ExplanationText" TextWrapping="Wrap"
                                         FontSize="12" Foreground="#FF666666"
                                         Text="AI将在此处解释润色的原因和改进点..."/>
                            </Border>
                        </Expander>

                        <!-- 操作按钮 -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button x:Name="CopyButton" Content="复制润色文本"
                                    Style="{StaticResource MaterialDesignFlatButton}"
                                    Margin="0,0,8,0" Click="Copy_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="复制润色后的文本到剪贴板"/>
                                </Button.ToolTip>
                            </Button>
                            <Button x:Name="RejectButton" Content="保留原文"
                                    Style="{StaticResource MaterialDesignOutlinedButton}"
                                    Margin="0,0,8,0" Click="RejectButton_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="保留原文，不采用润色结果"/>
                                </Button.ToolTip>
                            </Button>
                            <Button x:Name="AcceptButton" Content="采用润色"
                                    Style="{StaticResource MaterialDesignRaisedButton}"
                                    Background="{DynamicResource PrimaryHueMidBrush}"
                                    Click="AcceptButton_Click">
                                <Button.ToolTip>
                                    <ToolTip Content="采用润色结果，替换原文"/>
                                </Button.ToolTip>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>
        
        <!-- 悬浮操作控件 -->
        <materialDesign:Card x:Name="FloatingControl" 
                           Style="{StaticResource FloatingControlStyle}"
                           Visibility="Collapsed"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Top"
                           Margin="8">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="FloatingRejectButton" 
                        Style="{StaticResource MaterialDesignIconButton}"
                        Width="32" Height="32" Padding="0"
                        Click="RejectButton_Click"
                        ToolTip="保留原文">
                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16" 
                                           Foreground="#FFF44336"/>
                </Button>
                <Button x:Name="FloatingAcceptButton" 
                        Style="{StaticResource MaterialDesignIconButton}"
                        Width="32" Height="32" Padding="0"
                        Click="AcceptButton_Click"
                        ToolTip="采用润色">
                    <materialDesign:PackIcon Kind="Check" Width="16" Height="16" 
                                           Foreground="#FF4CAF50"/>
                </Button>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>

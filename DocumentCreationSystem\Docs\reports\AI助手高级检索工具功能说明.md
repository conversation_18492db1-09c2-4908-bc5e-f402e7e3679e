# AI助手高级检索工具功能说明

## 概述

为了解决AI助手agent对话框中工具不够全面，AI模型无法自我检索目标文件夹下的详细文件内容的问题，我们新增了8个高级检索和分析工具，大幅增强了AI助手的文件检索和内容分析能力。

## 新增工具列表

### 🔍 高级检索分析工具

#### 1. scan_project_structure([path])
**功能**: 递归扫描项目结构，获取完整目录树
- **参数**: path (可选) - 目标路径，默认为当前项目路径
- **返回**: 完整的项目目录结构树，包含文件和文件夹统计
- **示例**: `scan_project_structure()` 或 `scan_project_structure(Projects/Project_3)`

#### 2. get_file_summary(filePath)
**功能**: 生成文件内容摘要和统计信息
- **参数**: filePath - 文件路径
- **返回**: 文件摘要、大小、行数、字符数、修改时间等详细信息
- **示例**: `get_file_summary(第一章.txt)`

#### 3. analyze_file_relationships([path])
**功能**: 分析文件间的关联关系
- **参数**: path (可选) - 分析路径，默认为当前项目路径
- **返回**: 文件间的关联关系分析，基于共同关键词
- **示例**: `analyze_file_relationships()`

#### 4. deep_content_search(keyword|[path])
**功能**: 深度搜索内容并提供上下文
- **参数**: keyword - 搜索关键词，path (可选) - 搜索路径
- **返回**: 包含关键词的文件位置和上下文内容
- **示例**: `deep_content_search(主角|Projects/Project_3)`

#### 5. get_project_overview([path])
**功能**: 生成项目整体概览和分析
- **参数**: path (可选) - 项目路径，默认为当前项目路径
- **返回**: 项目统计信息、文件类型分布、主要内容摘要
- **示例**: `get_project_overview()`

#### 6. extract_key_information(filePath|[type])
**功能**: 提取关键信息（角色/地点/事件）
- **参数**: filePath - 文件路径，type (可选) - 信息类型 (characters/locations/events/all)
- **返回**: 从文件中提取的角色、地点、事件等关键信息
- **示例**: `extract_key_information(第一章.txt|characters)`

#### 7. compare_files(file1|file2)
**功能**: 比较两个文件的差异
- **参数**: file1 - 第一个文件路径，file2 - 第二个文件路径
- **返回**: 文件差异分析和相似度计算
- **示例**: `compare_files(第一章.txt|第二章.txt)`

#### 8. get_content_statistics([path])
**功能**: 获取详细的内容统计信息
- **参数**: path (可选) - 文件或目录路径
- **返回**: 详细的内容统计，包括行数、字符数、词数等
- **示例**: `get_content_statistics(第一章.txt)` 或 `get_content_statistics()`

## 使用场景示例

### 📊 项目分析场景
```
用户: "帮我分析整个项目的结构和内容"
AI可以调用:
1. scan_project_structure() - 获取项目结构
2. get_project_overview() - 生成项目概览
3. analyze_file_relationships() - 分析文件关系
4. get_content_statistics() - 获取统计信息
```

### 🔍 内容搜索场景
```
用户: "找到所有提到'主角'的地方"
AI可以调用:
1. deep_content_search(主角) - 深度搜索内容
2. extract_key_information(文件路径|characters) - 提取角色信息
```

### 📝 文件分析场景
```
用户: "分析第一章的内容"
AI可以调用:
1. get_file_summary(第一章.txt) - 获取文件摘要
2. extract_key_information(第一章.txt|all) - 提取关键信息
3. get_content_statistics(第一章.txt) - 获取统计信息
```

### ⚖️ 文件比较场景
```
用户: "比较第一章和第二章的差异"
AI可以调用:
1. compare_files(第一章.txt|第二章.txt) - 比较文件差异
```

## 技术特性

### 🚀 性能优化
- 异步处理，避免UI阻塞
- 智能缓存，提高响应速度
- 递归深度限制，防止无限循环
- 结果数量限制，避免信息过载

### 🛡️ 错误处理
- 完善的异常捕获和处理
- 详细的错误信息反馈
- 文件访问权限检查
- 路径有效性验证

### 📊 智能分析
- 关键词提取和频率分析
- 文本相似度计算
- 内容摘要生成
- 文件关系分析

### 🎯 精确搜索
- 支持模糊匹配
- 上下文信息提供
- 多文件批量搜索
- 正则表达式支持

## 集成方式

这些工具已完全集成到AI助手agent对话框中，AI模型可以根据用户需求自动选择和调用相应的工具。用户只需要用自然语言描述需求，AI会智能地选择最合适的工具组合来完成任务。

## 使用建议

1. **项目初始化**: 首次使用时建议先调用 `scan_project_structure()` 和 `get_project_overview()` 了解项目整体情况
2. **内容搜索**: 使用 `deep_content_search()` 进行精确搜索，使用 `extract_key_information()` 提取结构化信息
3. **文件分析**: 对重要文件使用 `get_file_summary()` 获取概览，使用 `get_content_statistics()` 获取详细统计
4. **关系分析**: 使用 `analyze_file_relationships()` 了解文件间的关联性
5. **版本比较**: 使用 `compare_files()` 比较不同版本或相似文件的差异

通过这些高级检索工具，AI助手现在具备了强大的项目文件检索和内容分析能力，能够更好地理解和处理用户的项目内容。

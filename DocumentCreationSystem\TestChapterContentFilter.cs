using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;
using Microsoft.Extensions.Logging;
using System;
using System.IO;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试章节内容过滤功能
    /// </summary>
    public class TestChapterContentFilter
    {
        private readonly IThinkingChainService _thinkingChainService;
        private readonly ILogger<TestChapterContentFilter> _logger;

        public TestChapterContentFilter()
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<TestChapterContentFilter>();
            _thinkingChainService = new ThinkingChainService(loggerFactory.CreateLogger<ThinkingChainService>());
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("=== 章节内容过滤功能测试 ===\n");

            TestThinkingProcessFilter();
            TestFormatInstructionFilter();
            TestChapterAnalysisFilter();
            TestFileNamingCleanup();

            Console.WriteLine("\n=== 测试完成 ===");
        }

        /// <summary>
        /// 测试思考过程过滤
        /// </summary>
        private void TestThinkingProcessFilter()
        {
            Console.WriteLine("1. 测试思考过程过滤");
            Console.WriteLine("===================");

            var testContent = @"思考过程：
分析用户需求，需为第1章创作独特且符合要求的细纲。首先明确章节定位是开篇章节，需衔接背景、引入人物与初步冲突。

具体分析步骤如下：
1. 场景选择与创新：原可能为普通街道，现改为""城郊废弃的乱葬岗""
2. 冲突重构：将""触发系统并收尸""改为""重生后面对饿死难民尸体时的生死抉择""

<o>
## 第1章：尸骨街的重生钟声

清晨的第一缕阳光刚爬过城郊的荒地边缘，李长生就感觉到了那股熟悉的寒意。他蜷缩在乱葬岗边缘的石块堆后，身上裹着破旧的棉袍，却仍被冻得浑身发抖。
</o>";

            var filter = new ThinkingChainFilter
            {
                EnableFilter = true,
                Mode = FilterMode.RemoveThinkingChain
            };

            var result = _thinkingChainService.ProcessThinkingChainResponse(testContent, filter);
            var filteredContent = _thinkingChainService.FilterChapterContent(result.ProcessedContent);

            Console.WriteLine("原始内容长度: " + testContent.Length);
            Console.WriteLine("过滤后内容长度: " + filteredContent.Length);
            Console.WriteLine("过滤后内容:");
            Console.WriteLine(filteredContent);
            Console.WriteLine();
        }

        /// <summary>
        /// 测试格式说明过滤
        /// </summary>
        private void TestFormatInstructionFilter()
        {
            Console.WriteLine("2. 测试格式说明过滤");
            Console.WriteLine("===================");

            var testContent = @"1. 如果需要思考过程，请将思考内容放在 <|begin_of_box|>...<|end_of_box|> 标签中
2. 将最终答案放在 <o>...</o> 或 <o>...</o> 标签中
3. 只有 <o> 或 <o> 标签中的内容会被显示给用户

示例格式：
<o>
这里是最终的回答内容
</o>

（此处为思考过程的占位，实际需用户提供思考后输出，但当前需求是生成章节内容，故调整思路直接输出章节内容）

<o>
## 第2章：乱市摊头的生死局

阳光如利刃般切割着乱市的空气，刺得人眼睛生疼。李长生背着刚收殓好的无主尸体，脚步沉重地朝着乱市中心的尸体交易广场走来。
</o>";

            var filter = new ThinkingChainFilter
            {
                EnableFilter = true,
                Mode = FilterMode.RemoveThinkingChain
            };

            var result = _thinkingChainService.ProcessThinkingChainResponse(testContent, filter);
            var filteredContent = _thinkingChainService.FilterChapterContent(result.ProcessedContent);

            Console.WriteLine("原始内容长度: " + testContent.Length);
            Console.WriteLine("过滤后内容长度: " + filteredContent.Length);
            Console.WriteLine("过滤后内容:");
            Console.WriteLine(filteredContent);
            Console.WriteLine();
        }

        /// <summary>
        /// 测试章节分析内容过滤
        /// </summary>
        private void TestChapterAnalysisFilter()
        {
            Console.WriteLine("3. 测试章节分析内容过滤");
            Console.WriteLine("=======================");

            var testContent = @"## 第1章：尸骨街的重生钟声

### 一、 chapter 核心亮点
- 独特场景：**城郊废弃的乱葬岗，清晨薄雾弥漫，地面堆积着无数无主尸骸**
- 核心冲突：**重生后面临的首个生死抉择——是否收殓这具饿死的难民尸体**
- 人物发展：**李长生从被动承受命运的乞丐转变为主动触发系统、确立生存意识的收尸者**

### 二、 场景与氛围
清晨的第一缕阳光刚爬过城郊的荒地边缘，李长生就感觉到了那股熟悉的寒意。

### 三、 人物动态
李长生（重生后）的身体因饥饿与寒冷而僵硬，但当他目光落在乱葬岗中间的那具尸体上时，身体突然有了反应。

### 四、 剧情发展（主线推进）
当李长生的手指碰到尸体时，一股刺痛感瞬间传遍全身，但他没有退缩。

### 五、 世界观与伏笔
在这一章中，我们初步展现了""收尸系统""的核心设定——通过收殓无主尸骸获得业力值。

### 六、 结尾与过渡
画面定格在李长生捧着收好的尸体走向藏身处的背影，远处传来军阀军队的脚步声。

（注：此为根据大纲生成的章节内容，包含场景、冲突、人物、剧情推进、世界观与伏笔等要素）";

            var filteredContent = _thinkingChainService.FilterChapterContent(testContent);

            Console.WriteLine("原始内容长度: " + testContent.Length);
            Console.WriteLine("过滤后内容长度: " + filteredContent.Length);
            Console.WriteLine("过滤后内容:");
            Console.WriteLine(filteredContent);
            Console.WriteLine();
        }

        /// <summary>
        /// 测试文件命名清理功能
        /// </summary>
        private void TestFileNamingCleanup()
        {
            Console.WriteLine("4. 测试文件命名清理功能");
            Console.WriteLine("========================");

            // 测试章节标题清理逻辑
            var testChapterTitles = new[]
            {
                "第1章：尸骨街的重生钟声",
                "第1章 尸骨街的重生钟声",
                "第001章：尸骨街的重生钟声",
                "尸骨街的重生钟声"
            };

            Console.WriteLine("章节标题清理测试:");
            foreach (var title in testChapterTitles)
            {
                var cleaned = CleanChapterTitle(title, 1);
                Console.WriteLine($"原标题: {title} -> 清理后: {cleaned}");
            }

            // 测试卷宗标题清理
            var testVolumeTitles = new[]
            {
                "第1卷：重生之路",
                "第01卷 重生之路",
                "重生之路"
            };

            Console.WriteLine("\n卷宗标题清理测试:");
            foreach (var title in testVolumeTitles)
            {
                var cleaned = CleanVolumeTitle(title, 1);
                Console.WriteLine($"原标题: {title} -> 清理后: {cleaned}");
            }
        }

        /// <summary>
        /// 清理章节标题中的重复信息（复制自StepByStepWritingService）
        /// </summary>
        private string CleanChapterTitle(string title, int chapterNumber)
        {
            if (string.IsNullOrEmpty(title))
                return $"第{chapterNumber}章";

            // 移除开头的"第X章"格式
            var patterns = new[]
            {
                $@"^第{chapterNumber}章[：:\s]*",
                $@"^第\d+章[：:\s]*",
                @"^第\d+章[：:\s]*"
            };

            var cleanTitle = title;
            foreach (var pattern in patterns)
            {
                cleanTitle = System.Text.RegularExpressions.Regex.Replace(cleanTitle, pattern, "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            // 如果清理后为空，使用默认标题
            cleanTitle = cleanTitle.Trim();
            if (string.IsNullOrEmpty(cleanTitle))
                return $"第{chapterNumber}章";

            return cleanTitle;
        }

        /// <summary>
        /// 清理卷宗标题中的重复信息（复制自StepByStepWritingService）
        /// </summary>
        private string CleanVolumeTitle(string title, int volumeNumber)
        {
            if (string.IsNullOrEmpty(title))
                return $"第{volumeNumber}卷";

            // 移除开头的"第X卷"格式
            var patterns = new[]
            {
                $@"^第{volumeNumber}卷[：:\s]*",
                $@"^第\d+卷[：:\s]*",
                @"^第\d+卷[：:\s]*"
            };

            var cleanTitle = title;
            foreach (var pattern in patterns)
            {
                cleanTitle = System.Text.RegularExpressions.Regex.Replace(cleanTitle, pattern, "", System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            }

            // 如果清理后为空，使用默认标题
            cleanTitle = cleanTitle.Trim();
            if (string.IsNullOrEmpty(cleanTitle))
                return $"第{volumeNumber}卷";

            return cleanTitle;
        }
    }
}

<Window x:Class="DocumentCreationSystem.Views.SettingsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="系统设置" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="200"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧导航 -->
        <materialDesign:Card Grid.Column="0" Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
            <ListBox x:Name="SettingsNavigation" SelectionMode="Single" SelectedIndex="0">
                <ListBoxItem>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Robot" Margin="0,0,8,0"/>
                        <TextBlock Text="AI设置"/>
                    </StackPanel>
                </ListBoxItem>
                <ListBoxItem>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="VectorTriangle" Margin="0,0,8,0"/>
                        <TextBlock Text="向量数据库"/>
                    </StackPanel>
                </ListBoxItem>
                <ListBoxItem>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="FileDocument" Margin="0,0,8,0"/>
                        <TextBlock Text="文档设置"/>
                    </StackPanel>
                </ListBoxItem>
                <ListBoxItem>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Palette" Margin="0,0,8,0"/>
                        <TextBlock Text="界面主题"/>
                    </StackPanel>
                </ListBoxItem>
            </ListBox>
        </materialDesign:Card>

        <!-- 右侧设置内容 -->
        <materialDesign:Card Grid.Column="1" Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Settings" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="系统设置" VerticalAlignment="Center" FontWeight="Medium" FontSize="16"/>
                    </StackPanel>
                </materialDesign:ColorZone>

                <!-- 设置内容 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="SettingsContent" Margin="24">
                        
                        <!-- AI设置面板 -->
                        <StackPanel x:Name="AISettingsPanel">
                            <TextBlock Text="AI服务配置" Style="{StaticResource SectionHeaderStyle}"/>

                            <!-- 默认AI提供者选择 -->
                            <ComboBox x:Name="DefaultProviderComboBox"
                                    materialDesign:HintAssist.Hint="默认AI提供者"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    Margin="0,0,0,16"
                                    SelectionChanged="DefaultProvider_SelectionChanged">
                                <ComboBoxItem Content="智谱AI" Tag="ZhipuAI" IsSelected="True"/>
                                <ComboBoxItem Content="Ollama" Tag="Ollama"/>
                                <ComboBoxItem Content="LM Studio" Tag="LMStudio"/>
                            </ComboBox>

                            <!-- 智谱AI设置 -->
                            <Expander Header="智谱AI" IsExpanded="True" Margin="0,0,0,16">
                                <StackPanel Margin="16,8,0,0">
                                    <TextBox x:Name="ZhipuApiKeyTextBox"
                                           materialDesign:HintAssist.Hint="API Key"
                                           materialDesign:HintAssist.IsFloating="True"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Margin="0,0,0,16"/>

                                    <TextBox x:Name="ZhipuBaseUrlTextBox"
                                           materialDesign:HintAssist.Hint="Base URL"
                                           materialDesign:HintAssist.IsFloating="True"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Text="https://open.bigmodel.cn/api/paas/v4"
                                           Margin="0,0,0,16"/>

                                    <ComboBox x:Name="ZhipuModelComboBox"
                                            materialDesign:HintAssist.Hint="默认模型"
                                            materialDesign:HintAssist.IsFloating="True"
                                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                            Margin="0,0,0,16">
                                        <ComboBoxItem Content="GLM-4" Tag="glm-4" IsSelected="True"/>
                                        <ComboBoxItem Content="GLM-4-0520" Tag="glm-4-0520"/>
                                        <ComboBoxItem Content="GLM-4-Long" Tag="glm-4-long"/>
                                        <ComboBoxItem Content="GLM-4-Air" Tag="glm-4-air"/>
                                        <ComboBoxItem Content="GLM-4-AirX" Tag="glm-4-airx"/>
                                        <ComboBoxItem Content="GLM-4-Flash" Tag="glm-4-flash"/>
                                    </ComboBox>

                                    <Button Content="测试连接"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          HorizontalAlignment="Left"
                                          Click="TestZhipuConnection_Click"/>
                                </StackPanel>
                            </Expander>

                            <!-- Ollama设置 -->
                            <Expander Header="Ollama" Margin="0,0,0,16">
                                <StackPanel Margin="16,8,0,0">
                                    <TextBox x:Name="OllamaBaseUrlTextBox"
                                           materialDesign:HintAssist.Hint="Base URL"
                                           materialDesign:HintAssist.IsFloating="True"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Text="http://localhost:11434"
                                           Margin="0,0,0,16"/>

                                    <Button Content="刷新模型列表"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          HorizontalAlignment="Left"
                                          Margin="0,0,0,16"
                                          Click="RefreshOllamaModels_Click"/>

                                    <ListBox x:Name="OllamaModelsListBox"
                                           Height="120"
                                           SelectionMode="Single"
                                           Margin="0,0,0,16"/>

                                    <Button Content="测试连接"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          HorizontalAlignment="Left"
                                          Click="TestOllamaConnection_Click"/>
                                </StackPanel>
                            </Expander>

                            <!-- LM Studio设置 -->
                            <Expander Header="LM Studio" Margin="0,0,0,16">
                                <StackPanel Margin="16,8,0,0">
                                    <TextBox x:Name="LMStudioBaseUrlTextBox"
                                           materialDesign:HintAssist.Hint="Base URL"
                                           materialDesign:HintAssist.IsFloating="True"
                                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                           Text="http://localhost:1234"
                                           Margin="0,0,0,16"/>

                                    <Button Content="刷新模型列表"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          HorizontalAlignment="Left"
                                          Margin="0,0,0,16"
                                          Click="RefreshLMStudioModels_Click"/>

                                    <ListBox x:Name="LMStudioModelsListBox"
                                           Height="120"
                                           SelectionMode="Single"
                                           Margin="0,0,0,16"/>

                                    <Button Content="测试连接"
                                          Style="{StaticResource MaterialDesignOutlinedButton}"
                                          HorizontalAlignment="Left"
                                          Click="TestLMStudioConnection_Click"/>
                                </StackPanel>
                            </Expander>

                            <!-- AI参数设置 -->
                            <Expander Header="AI参数设置" Margin="0,0,0,16">
                                <StackPanel Margin="16,8,0,0">
                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="默认温度参数" VerticalAlignment="Center"/>
                                        <Slider Grid.Column="1"
                                              x:Name="TemperatureSlider"
                                              Minimum="0.1" Maximum="1.0" Value="0.7"
                                              Width="150"
                                              TickFrequency="0.1"
                                              IsSnapToTickEnabled="True"/>
                                    </Grid>

                                    <Grid Margin="0,0,0,16">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="默认最大Token数" VerticalAlignment="Center"/>
                                        <TextBox Grid.Column="1"
                                               x:Name="MaxTokensTextBox"
                                               Text="2000"
                                               Width="100"/>
                                    </Grid>

                                    <CheckBox x:Name="EnableThinkingChainCheckBox"
                                            Content="启用思维链处理"
                                            IsChecked="True"
                                            Margin="0,0,0,16"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>

                        <!-- 向量数据库设置面板 -->
                        <StackPanel x:Name="VectorSettingsPanel" Visibility="Collapsed">
                            <TextBlock Text="向量数据库配置" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <TextBox x:Name="QdrantUrlTextBox"
                                   materialDesign:HintAssist.Hint="Qdrant URL"
                                   materialDesign:HintAssist.IsFloating="True"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Text="http://localhost:6333"
                                   Margin="0,0,0,16"/>
                            
                            <TextBox x:Name="CollectionNameTextBox"
                                   materialDesign:HintAssist.Hint="集合名称"
                                   materialDesign:HintAssist.IsFloating="True"
                                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                   Text="documents"
                                   Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="向量维度" VerticalAlignment="Center"/>
                                <TextBox Grid.Column="1" 
                                       x:Name="VectorSizeTextBox"
                                       Text="1024" 
                                       Width="100"/>
                            </Grid>

                            <Button Content="测试连接" 
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  HorizontalAlignment="Left"
                                  Click="TestVectorConnection_Click"/>
                        </StackPanel>

                        <!-- 文档设置面板 -->
                        <StackPanel x:Name="DocumentSettingsPanel" Visibility="Collapsed">
                            <TextBlock Text="文档管理配置" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <CheckBox x:Name="AutoSaveCheckBox" 
                                    Content="启用自动保存" 
                                    IsChecked="True"
                                    Margin="0,0,0,16"/>
                            
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="自动保存间隔（秒）" VerticalAlignment="Center"/>
                                <TextBox Grid.Column="1" 
                                       x:Name="AutoSaveIntervalTextBox"
                                       Text="30" 
                                       Width="100"/>
                            </Grid>

                            <CheckBox x:Name="AutoBackupCheckBox" 
                                    Content="启用自动备份" 
                                    IsChecked="True"
                                    Margin="0,0,0,16"/>
                        </StackPanel>

                        <!-- 界面主题设置面板 -->
                        <StackPanel x:Name="ThemeSettingsPanel" Visibility="Collapsed">
                            <TextBlock Text="界面主题配置" Style="{StaticResource SectionHeaderStyle}"/>
                            
                            <ComboBox x:Name="ThemeComboBox"
                                    materialDesign:HintAssist.Hint="主题"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    Margin="0,0,0,16">
                                <ComboBoxItem Content="浅色主题" Tag="Light" IsSelected="True"/>
                                <ComboBoxItem Content="深色主题" Tag="Dark"/>
                            </ComboBox>

                            <ComboBox x:Name="PrimaryColorComboBox"
                                    materialDesign:HintAssist.Hint="主色调"
                                    materialDesign:HintAssist.IsFloating="True"
                                    Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                    Margin="0,0,0,16">
                                <ComboBoxItem Content="深紫色" Tag="DeepPurple" IsSelected="True"/>
                                <ComboBoxItem Content="蓝色" Tag="Blue"/>
                                <ComboBoxItem Content="绿色" Tag="Green"/>
                                <ComboBoxItem Content="橙色" Tag="Orange"/>
                                <ComboBoxItem Content="红色" Tag="Red"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>

                <!-- 按钮区域 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" 
                          Margin="24" Background="{DynamicResource MaterialDesignPaper}">
                    <Button Content="重置" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,16,0"
                          Click="Reset_Click"/>
                    <Button Content="取消" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,16,0"
                          Click="Cancel_Click"/>
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="Save_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,8,0"/>
                                <TextBlock Text="保存设置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>

<Window x:Class="DocumentCreationSystem.Views.CreateProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="创建项目" Height="450" Width="650"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="创建新项目" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,20"/>

        <!-- 项目名称 -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="项目名称:" Margin="0,0,0,5"/>
            <TextBox x:Name="ProjectNameTextBox" 
                     materialDesign:HintAssist.Hint="请输入项目名称"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     MaxLength="200"/>
        </StackPanel>

        <!-- 项目类型 -->
        <StackPanel Grid.Row="2" Margin="0,0,0,15">
            <TextBlock Text="项目类型:" Margin="0,0,0,5"/>
            <ComboBox x:Name="ProjectTypeComboBox" 
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      materialDesign:HintAssist.Hint="选择项目类型">
                <ComboBoxItem Content="普通文档项目" Tag="Normal" IsSelected="True"/>
                <ComboBoxItem Content="小说创作项目" Tag="Novel"/>
                <ComboBoxItem Content="研究项目" Tag="Research"/>
                <ComboBoxItem Content="文档项目" Tag="Documentation"/>
            </ComboBox>
        </StackPanel>

        <!-- 项目路径 -->
        <StackPanel Grid.Row="3" Margin="0,0,0,15">
            <TextBlock Text="项目路径:" Margin="0,0,0,5"/>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="ProjectPathTextBox" 
                         Grid.Column="0"
                         materialDesign:HintAssist.Hint="选择项目创建路径"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         IsReadOnly="True"
                         Margin="0,0,10,0"/>
                
                <Button x:Name="BrowsePathButton" 
                        Grid.Column="1"
                        Content="浏览..."
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Click="BrowsePathButton_Click"/>
            </Grid>
        </StackPanel>

        <!-- 项目描述 -->
        <StackPanel Grid.Row="4" Margin="0,0,0,15">
            <TextBlock Text="项目描述 (可选):" Margin="0,0,0,5"/>
            <TextBox x:Name="ProjectDescriptionTextBox" 
                     materialDesign:HintAssist.Hint="请输入项目描述"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     AcceptsReturn="True"
                     TextWrapping="Wrap"
                     MaxHeight="80"
                     VerticalScrollBarVisibility="Auto"/>
        </StackPanel>

        <!-- 按钮区域 -->
        <Border Grid.Row="5" BorderThickness="0,1,0,0"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                Margin="0,20,0,0" Padding="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="CancelButton"
                        Content="取消"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,15,0"
                        MinWidth="90"
                        Height="40"
                        Padding="16,8"
                        Click="CancelButton_Click"/>
                <Button x:Name="CreateButton"
                        Content="创建项目"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        MinWidth="120"
                        Height="40"
                        Padding="16,8"
                        IsDefault="True"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                        Click="CreateButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

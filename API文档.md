# API 文档

## 概述

文档管理及AI创作系统提供了一套完整的API接口，支持项目管理、文档处理、AI辅助创作和向量搜索等功能。

## 核心服务接口

### 1. AI服务 (IAIService)

AI服务提供文本生成、润色、扩写等AI功能。

#### 获取可用模型
```csharp
Task<List<AIModel>> GetAvailableModelsAsync()
```
返回当前可用的AI模型列表。

#### 设置当前模型
```csharp
Task<bool> SetCurrentModelAsync(string modelId)
```
设置要使用的AI模型。

#### 生成文本
```csharp
Task<string> GenerateTextAsync(string prompt, int maxTokens = 2000, float temperature = 0.7f)
```
基于提示词生成文本内容。

#### 润色文本
```csharp
Task<string> PolishTextAsync(string text, string style = "通用")
```
对输入文本进行润色优化。

#### 扩写文本
```csharp
Task<string> ExpandTextAsync(string text, int targetLength, string? context = null)
```
扩展文本内容，增加细节和深度。

#### 生成章节
```csharp
Task<string> GenerateChapterAsync(string outline, string? context = null, int targetWordCount = 6500)
```
基于大纲生成章节内容。

#### 一致性检查
```csharp
Task<ConsistencyCheckResult> CheckConsistencyAsync(string currentText, string previousContext)
```
检查文本的一致性。

### 2. 向量服务 (IVectorService)

向量服务提供语义搜索和相关内容推荐功能。

#### 初始化服务
```csharp
Task<bool> InitializeAsync()
```
初始化向量数据库连接。

#### 添加文档向量
```csharp
Task<List<VectorRecord>> AddDocumentVectorsAsync(int documentId, List<string> textChunks)
```
将文档分段并添加到向量数据库。

#### 更新文档向量
```csharp
Task<bool> UpdateDocumentVectorsAsync(int documentId, List<string> textChunks)
```
更新文档的向量表示。

#### 删除文档向量
```csharp
Task<bool> DeleteDocumentVectorsAsync(int documentId)
```
从向量数据库中删除文档。

#### 语义搜索
```csharp
Task<List<VectorSearchResult>> SearchAsync(string query, string collectionName = "documents", int limit = 10, float threshold = 0.7f)
```
基于语义相似度搜索相关内容。

#### 获取相关上下文
```csharp
Task<List<string>> GetRelevantContextAsync(string query, int? projectId = null, int limit = 5)
```
获取与查询相关的上下文内容。

### 3. 文档服务 (IDocumentService)

文档服务提供文档的创建、读取、更新和删除功能。

#### 创建文档
```csharp
Task<DocModel> CreateDocumentAsync(int projectId, string fileName, string content)
```
在指定项目中创建新文档。

#### 读取文档
```csharp
Task<string> ReadDocumentAsync(int documentId)
```
读取文档内容。

#### 保存文档
```csharp
Task<bool> SaveDocumentAsync(int documentId, string content)
```
保存文档内容。

#### 获取项目文档
```csharp
Task<List<DocModel>> GetProjectDocumentsAsync(int projectId)
```
获取项目下的所有文档。

#### 向量化文档
```csharp
Task<bool> VectorizeDocumentAsync(int documentId)
```
将文档内容向量化并存储。

### 4. 项目服务 (IProjectService)

项目服务提供项目的管理功能。

#### 创建项目
```csharp
Task<Project> CreateProjectAsync(string name, string type, string rootPath, string description)
```
创建新的写作项目。

#### 获取所有项目
```csharp
Task<List<Project>> GetAllProjectsAsync()
```
获取所有项目列表。

#### 获取项目详情
```csharp
Task<Project?> GetProjectAsync(int projectId)
```
获取指定项目的详细信息。

#### 更新项目
```csharp
Task<bool> UpdateProjectAsync(Project project)
```
更新项目信息。

### 5. 小说创作服务 (INovelCreationService)

小说创作服务提供专业的小说创作功能。

#### 生成全书大纲
```csharp
Task<string> GenerateOverallOutlineAsync(int novelProjectId, string creativeDirection)
```
生成小说的整体大纲。

#### 生成卷宗大纲
```csharp
Task<List<VolumeOutline>> GenerateVolumeOutlinesAsync(int novelProjectId, string overallOutline, int volumeCount = 10)
```
基于全书大纲生成各卷的详细大纲。

#### 生成章节大纲
```csharp
Task<string> GenerateChapterOutlineAsync(int novelProjectId, VolumeOutline volumeOutline, int chapterNumber)
```
生成指定章节的详细大纲。

#### 生成章节内容
```csharp
Task<string> GenerateChapterContentAsync(int novelProjectId, string chapterOutline, int chapterNumber, int targetWordCount = 6500)
```
基于章节大纲生成章节内容。

#### 自动生成章节
```csharp
Task<ChapterCreationResult> AutoGenerateChapterAsync(int chapterId, IProgress<ChapterCreationProgress>? progressCallback = null)
```
自动生成完整的章节，包括大纲和内容。

#### 检查章节一致性
```csharp
Task<ConsistencyCheckResult> CheckChapterConsistencyAsync(int chapterId, string currentContent)
```
检查章节内容的一致性。

#### 更新角色属性
```csharp
Task<List<Character>> UpdateCharacterAttributesAsync(int novelProjectId, string chapterContent, int chapterNumber)
```
基于章节内容更新角色属性。

#### 获取创作统计
```csharp
Task<NovelCreationStatistics> GetCreationStatisticsAsync(int novelProjectId)
```
获取小说创作的统计信息。

### 6. 文件监控服务 (IFileMonitorService)

文件监控服务提供实时文件变更监控功能。

#### 开始监控
```csharp
Task<bool> StartMonitoringAsync(int projectId, string projectPath)
```
开始监控指定项目路径的文件变更。

#### 停止监控
```csharp
Task<bool> StopMonitoringAsync(int projectId)
```
停止监控指定项目。

#### 触发文件同步
```csharp
Task<bool> TriggerFileSyncAsync(int projectId, string filePath)
```
手动触发文件同步。

#### 扫描并同步项目
```csharp
Task<int> ScanAndSyncProjectAsync(int projectId)
```
扫描项目目录并同步所有文件。

## 数据模型

### AIModel
```csharp
public class AIModel
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Provider { get; set; }
    public string Description { get; set; }
    public bool IsAvailable { get; set; }
    public int MaxTokens { get; set; }
}
```

### VectorSearchResult
```csharp
public class VectorSearchResult
{
    public string Id { get; set; }
    public float Score { get; set; }
    public string Content { get; set; }
    public int DocumentId { get; set; }
    public int ChunkIndex { get; set; }
    public Dictionary<string, object> Metadata { get; set; }
}
```

### ConsistencyCheckResult
```csharp
public class ConsistencyCheckResult
{
    public bool IsConsistent { get; set; }
    public List<string> Issues { get; set; }
    public List<string> Suggestions { get; set; }
    public float ConfidenceScore { get; set; }
}
```

### ChapterCreationResult
```csharp
public class ChapterCreationResult
{
    public bool IsSuccess { get; set; }
    public string Content { get; set; }
    public int WordCount { get; set; }
    public List<string> Warnings { get; set; }
    public List<string> Suggestions { get; set; }
    public TimeSpan CreationTime { get; set; }
    public bool IsCompleted { get; set; }
    public string? ErrorMessage { get; set; }
}
```

### NovelCreationStatistics
```csharp
public class NovelCreationStatistics
{
    public int TotalChapters { get; set; }
    public int CompletedChapters { get; set; }
    public int InProgressChapters { get; set; }
    public int PlannedChapters { get; set; }
    public int TotalWordCount { get; set; }
    public int AverageWordsPerChapter { get; set; }
    public float CompletionPercentage { get; set; }
    public TimeSpan TotalCreationTime { get; set; }
    public DateTime LastCreationDate { get; set; }
    public int CharacterCount { get; set; }
    public Dictionary<string, int> CharacterAppearances { get; set; }
}
```

## 使用示例

### 基本AI功能使用
```csharp
// 获取AI服务
var aiService = serviceProvider.GetRequiredService<IAIService>();

// 设置模型
var models = await aiService.GetAvailableModelsAsync();
await aiService.SetCurrentModelAsync(models.First().Id);

// 生成文本
var result = await aiService.GenerateTextAsync("请写一个科幻小说的开头");

// 润色文本
var polished = await aiService.PolishTextAsync("原始文本", "文学");
```

### 小说创作流程
```csharp
// 获取小说创作服务
var novelService = serviceProvider.GetRequiredService<INovelCreationService>();

// 生成大纲
var outline = await novelService.GenerateOverallOutlineAsync(projectId, "科幻冒险");

// 生成卷宗大纲
var volumes = await novelService.GenerateVolumeOutlinesAsync(projectId, outline);

// 创建章节
var chapter = await novelService.CreateChapterAsync(projectId, 1, "第一章：觉醒");

// 自动生成章节内容
var result = await novelService.AutoGenerateChapterAsync(chapter.Id);
```

### 向量搜索使用
```csharp
// 获取向量服务
var vectorService = serviceProvider.GetRequiredService<IVectorService>();

// 初始化
await vectorService.InitializeAsync();

// 添加文档
var chunks = vectorService.SplitTextIntoChunks(content);
await vectorService.AddDocumentVectorsAsync(documentId, chunks);

// 搜索相关内容
var results = await vectorService.SearchAsync("主角的背景", "documents", 5);
```

## 错误处理

所有API方法都会抛出适当的异常，建议使用try-catch块进行错误处理：

```csharp
try
{
    var result = await aiService.GenerateTextAsync(prompt);
    // 处理成功结果
}
catch (InvalidOperationException ex)
{
    // 处理操作异常
    logger.LogError(ex, "AI服务操作失败");
}
catch (Exception ex)
{
    // 处理其他异常
    logger.LogError(ex, "未知错误");
}
```

## 配置说明

系统配置通过 `appsettings.json` 文件进行管理：

```json
{
  "AI": {
    "ZhipuAI": {
      "ApiKey": "your-api-key",
      "BaseUrl": "https://open.bigmodel.cn/api/paas/v4"
    },
    "Ollama": {
      "BaseUrl": "http://localhost:11434"
    },
    "LMStudio": {
      "BaseUrl": "http://localhost:1234"
    }
  },
  "Vector": {
    "Qdrant": {
      "Url": "http://localhost:6333",
      "CollectionName": "documents"
    }
  }
}
```

## 性能优化建议

1. **批量操作**：尽量使用批量API减少网络请求
2. **缓存结果**：对频繁访问的数据进行缓存
3. **异步处理**：使用异步方法避免阻塞
4. **资源管理**：及时释放不需要的资源
5. **错误重试**：对网络相关操作实现重试机制

using System;
using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 书籍基本信息模型
    /// </summary>
    public class BookBasicInfo
    {
        /// <summary>
        /// 书籍标题
        /// </summary>
        [Required(ErrorMessage = "书籍标题不能为空")]
        public string BookTitle { get; set; } = string.Empty;

        /// <summary>
        /// 作者名称
        /// </summary>
        public string AuthorName { get; set; } = string.Empty;

        /// <summary>
        /// 小说类型
        /// </summary>
        public string Genre { get; set; } = "都市修仙";

        /// <summary>
        /// 卷数
        /// </summary>
        [Range(1, 50, ErrorMessage = "卷数必须在1-50之间")]
        public int VolumeCount { get; set; } = 5;

        /// <summary>
        /// 总章节数
        /// </summary>
        [Range(1, 10000, ErrorMessage = "总章节数必须在1-10000之间")]
        public int ChapterCount { get; set; } = 1000;

        /// <summary>
        /// 每章字数
        /// </summary>
        [Range(1000, 20000, ErrorMessage = "每章字数必须在1000-20000之间")]
        public int WordsPerChapter { get; set; } = 6500;

        /// <summary>
        /// 目标总字数
        /// </summary>
        [Range(10000, 10000000, ErrorMessage = "目标总字数必须在1万-1000万之间")]
        public int TargetWordCount { get; set; } = 650000;

        /// <summary>
        /// 分段字数
        /// </summary>
        [Range(500, 3000, ErrorMessage = "分段字数必须在500-3000之间")]
        public int SegmentWords { get; set; } = 1000;

        /// <summary>
        /// 回顾章节数
        /// </summary>
        [Range(0, 10, ErrorMessage = "回顾章节数必须在0-10之间")]
        public int ReviewChapters { get; set; } = 3;

        /// <summary>
        /// 写作风格
        /// </summary>
        public string WritingStyle { get; set; } = "流畅自然";

        /// <summary>
        /// 是否自动保存章节
        /// </summary>
        public bool AutoSave { get; set; } = true;

        /// <summary>
        /// 是否先生成大纲
        /// </summary>
        public bool GenerateOutlineFirst { get; set; } = true;

        /// <summary>
        /// 是否参考编辑器内容
        /// </summary>
        public bool UseReferenceContent { get; set; } = true;

        /// <summary>
        /// 创作方向和基本设定
        /// </summary>
        public string CreativeDirection { get; set; } = string.Empty;

        /// <summary>
        /// 保存时间
        /// </summary>
        public DateTime SavedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 项目路径
        /// </summary>
        public string ProjectPath { get; set; } = string.Empty;

        /// <summary>
        /// 验证书籍基本信息是否有效
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(BookTitle))
            {
                return (false, "书籍标题不能为空");
            }

            if (VolumeCount < 1 || VolumeCount > 50)
            {
                return (false, "卷数必须在1-50之间");
            }

            if (ChapterCount < 1 || ChapterCount > 10000)
            {
                return (false, "总章节数必须在1-10000之间");
            }

            if (WordsPerChapter < 1000 || WordsPerChapter > 20000)
            {
                return (false, "每章字数必须在1000-20000之间");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 创建副本
        /// </summary>
        /// <returns>书籍基本信息副本</returns>
        public BookBasicInfo Clone()
        {
            return new BookBasicInfo
            {
                BookTitle = this.BookTitle,
                VolumeCount = this.VolumeCount,
                ChapterCount = this.ChapterCount,
                WordsPerChapter = this.WordsPerChapter,
                CreativeDirection = this.CreativeDirection,
                SavedAt = this.SavedAt,
                ProjectPath = this.ProjectPath
            };
        }
    }
}

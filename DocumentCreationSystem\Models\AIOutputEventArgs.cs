using System;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// AI输出事件参数
    /// </summary>
    public class AIOutputEventArgs : EventArgs
    {
        /// <summary>
        /// AI生成的内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 章节标题
        /// </summary>
        public string ChapterTitle { get; set; } = string.Empty;

        /// <summary>
        /// 章节编号
        /// </summary>
        public int ChapterNumber { get; set; }

        /// <summary>
        /// 内容类型（如：章节大纲、章节内容、世界观设定等）
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// 生成时间
        /// </summary>
        public DateTime GeneratedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 字数统计
        /// </summary>
        public int WordCount { get; set; }

        /// <summary>
        /// 是否为最终内容
        /// </summary>
        public bool IsFinalContent { get; set; } = true;

        /// <summary>
        /// 构造函数
        /// </summary>
        public AIOutputEventArgs()
        {
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="content">AI生成的内容</param>
        /// <param name="chapterTitle">章节标题</param>
        /// <param name="contentType">内容类型</param>
        public AIOutputEventArgs(string content, string chapterTitle = "", string contentType = "")
        {
            Content = content ?? string.Empty;
            ChapterTitle = chapterTitle ?? string.Empty;
            ContentType = contentType ?? string.Empty;
            WordCount = CalculateWordCount(content);
        }

        /// <summary>
        /// 计算字数
        /// </summary>
        private int CalculateWordCount(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return 0;
            
            // 简单的字数统计，去除空白字符
            return text.Replace(" ", "").Replace("\t", "").Replace("\n", "").Replace("\r", "").Length;
        }
    }
}

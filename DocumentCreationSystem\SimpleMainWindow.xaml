<Window x:Class="DocumentCreationSystem.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="文档管理及AI创作系统 - 测试版本"
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#FF2196F3" Padding="16">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📚" FontSize="24" Margin="0,0,8,0"/>
                <TextBlock Text="文档管理及AI创作系统" 
                          FontSize="18" FontWeight="Bold" 
                          Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航 -->
            <Border Grid.Column="0" Background="#FFF5F5F5" Padding="10" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="功能菜单" FontWeight="Bold" Margin="0,0,0,10"/>
                    <Button Content="新建项目" Margin="0,2" Padding="8,4" Click="NewProject_Click"/>
                    <Button Content="打开项目" Margin="0,2" Padding="8,4" Click="OpenProject_Click"/>
                    <Button Content="AI模型配置" Margin="0,2" Padding="8,4" Click="AIConfig_Click"/>
                    <Button Content="项目工具" Margin="0,2" Padding="8,4" Click="ProjectTools_Click"/>
                    <Button Content="关于系统" Margin="0,2" Padding="8,4" Click="About_Click"/>
                </StackPanel>
            </Border>

            <!-- 右侧内容区域 -->
            <Border Grid.Column="1" Background="White" BorderBrush="#FFCCCCCC" BorderThickness="1" Padding="20">
                <StackPanel>
                    <TextBlock Text="欢迎使用文档管理及AI创作系统" 
                              FontSize="20" FontWeight="Bold" 
                              HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    
                    <TextBlock Text="系统功能：" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBlock Text="• 文档管理和编辑" Margin="20,2"/>
                    <TextBlock Text="• AI辅助创作" Margin="20,2"/>
                    <TextBlock Text="• 项目管理" Margin="20,2"/>
                    <TextBlock Text="• 多种AI模型支持" Margin="20,2"/>
                    
                    <TextBlock Text="当前状态：" FontWeight="Bold" Margin="0,20,0,10"/>
                    <TextBlock x:Name="StatusText" Text="系统已就绪" Margin="20,2" Foreground="Green"/>
                    
                    <TextBlock Text="系统信息：" FontWeight="Bold" Margin="0,20,0,10"/>
                    <TextBlock x:Name="SystemInfoText" Margin="20,2"/>
                </StackPanel>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFF0F0F0" Padding="10,5">
            <DockPanel>
                <TextBlock Text="就绪" DockPanel.Dock="Left"/>
                <TextBlock x:Name="TimeText" DockPanel.Dock="Right" HorizontalAlignment="Right"/>
            </DockPanel>
        </Border>
    </Grid>
</Window>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Views
{
    /// <summary>
    /// 已保存配置选择对话框
    /// </summary>
    public partial class SavedConfigsSelectionDialog : Window
    {
        /// <summary>
        /// 选中的配置
        /// </summary>
        public BookBasicInfo? SelectedConfig { get; private set; }

        /// <summary>
        /// 配置列表
        /// </summary>
        private List<BookBasicInfo> _configs;

        public SavedConfigsSelectionDialog(List<BookBasicInfo> configs)
        {
            InitializeComponent();
            _configs = configs ?? new List<BookBasicInfo>();
            
            // 按保存时间倒序排列
            var sortedConfigs = _configs.OrderByDescending(c => c.SavedAt).ToList();
            ConfigsDataGrid.ItemsSource = sortedConfigs;
            
            // 默认选中第一项
            if (sortedConfigs.Any())
            {
                ConfigsDataGrid.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void Ok_Click(object sender, RoutedEventArgs e)
        {
            SelectedConfig = ConfigsDataGrid.SelectedItem as BookBasicInfo;
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private void Delete_Click(object sender, RoutedEventArgs e)
        {
            var selectedConfig = ConfigsDataGrid.SelectedItem as BookBasicInfo;
            if (selectedConfig == null)
            {
                return;
            }

            var result = MessageBox.Show(
                $"确定要删除配置 \"{selectedConfig.BookTitle}\" 吗？",
                "确认删除",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 从列表中移除
                    _configs.Remove(selectedConfig);
                    
                    // 刷新数据网格
                    var sortedConfigs = _configs.OrderByDescending(c => c.SavedAt).ToList();
                    ConfigsDataGrid.ItemsSource = null;
                    ConfigsDataGrid.ItemsSource = sortedConfigs;
                    
                    // 选中下一项
                    if (sortedConfigs.Any())
                    {
                        ConfigsDataGrid.SelectedIndex = 0;
                    }

                    MessageBox.Show("配置删除成功", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除配置时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 双击数据网格行事件
        /// </summary>
        private void ConfigsDataGrid_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ConfigsDataGrid.SelectedItem != null)
            {
                Ok_Click(sender, new RoutedEventArgs());
            }
        }

        /// <summary>
        /// 窗口加载事件
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // 聚焦到数据网格
            ConfigsDataGrid.Focus();
        }
    }
}

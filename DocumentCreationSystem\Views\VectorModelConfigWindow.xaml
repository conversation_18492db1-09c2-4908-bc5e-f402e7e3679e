<Window x:Class="DocumentCreationSystem.Views.VectorModelConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="向量模型配置" Height="550" Width="750"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <materialDesign:PackIcon Kind="VectorTriangle" Height="24" Width="24"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="向量模型配置" VerticalAlignment="Center"
                         FontSize="18" FontWeight="Medium"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button x:Name="TestConnectionButton" Content="测试连接"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0" Click="TestConnection_Click"
                            ToolTip="测试当前配置的连接，成功后将自动保存配置"/>
                    <Button x:Name="RefreshModelsButton" Content="刷新模型"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="RefreshModels_Click"
                            ToolTip="重新检测可用的向量模型"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="24">

                <!-- 使用说明 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16"
                                   Background="{DynamicResource MaterialDesignCardBackground}">
                    <StackPanel Margin="16">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="Information" VerticalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,8,0"/>
                            <TextBlock Text="配置说明" FontWeight="Medium" FontSize="14"/>
                        </StackPanel>
                        <TextBlock TextWrapping="Wrap" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Text="向量模型用于文档向量化和语义搜索功能：&#x0A;1. 选择您要使用的向量模型平台&#x0A;2. 填写相应的配置信息&#x0A;3. 点击&quot;测试连接&quot;验证配置&#x0A;4. 测试成功后配置将自动保存并应用"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 平台选择 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="向量模型平台选择" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <UniformGrid Columns="2" Rows="1">
                            <RadioButton x:Name="OllamaRadio" Content="Ollama (本地)"
                                       GroupName="VectorPlatform" IsChecked="True"
                                       Margin="8" Checked="Platform_Checked"
                                       ToolTip="本地运行的开源向量模型平台，支持多种向量模型"/>
                            <RadioButton x:Name="LMStudioRadio" Content="LM Studio"
                                       GroupName="VectorPlatform"
                                       Margin="8" Checked="Platform_Checked"
                                       ToolTip="本地向量模型运行环境，提供OpenAI兼容的向量API接口"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 平台配置区域 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <DockPanel Margin="0,0,0,16">
                            <TextBlock x:Name="PlatformTitle" Text="Ollama 向量模型配置" FontWeight="Medium" FontSize="16"
                                     DockPanel.Dock="Left" VerticalAlignment="Center"/>
                            <TextBlock x:Name="PlatformDescription" Text="本地开源向量模型平台，用于文档向量化和语义搜索"
                                     FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     DockPanel.Dock="Right" VerticalAlignment="Center" TextAlignment="Right"/>
                        </DockPanel>
                        
                        <!-- Ollama 向量模型配置 -->
                        <StackPanel x:Name="OllamaConfig" Visibility="Visible">
                            <TextBox x:Name="OllamaUrlTextBox"
                                   materialDesign:HintAssist.Hint="Ollama服务地址"
                                   Text="http://localhost:11434"
                                   Margin="0,0,0,16"
                                   ToolTip="Ollama服务的地址，默认为本地11434端口。确保Ollama服务已启动。"/>

                            <DockPanel Margin="0,0,0,16">
                                <TextBlock Text="可用向量模型:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <Button x:Name="DetectOllamaModelsButton" Content="检测模型"
                                      Style="{StaticResource MaterialDesignFlatButton}"
                                      DockPanel.Dock="Right"
                                      Click="DetectModels_Click"
                                      ToolTip="自动检测Ollama中已安装的向量模型"/>
                            </DockPanel>

                            <ComboBox x:Name="OllamaModelComboBox"
                                    materialDesign:HintAssist.Hint="选择向量模型"
                                    DisplayMemberPath="Name"
                                    SelectedValuePath="Id"
                                    ToolTip="选择要使用的Ollama向量模型。如果列表为空，请先点击'检测模型'按钮。"/>

                            <!-- 推荐向量模型 -->
                            <Expander Header="推荐向量模型下载" Margin="0,16,0,0">
                                <StackPanel Margin="0,8,0,0">
                                    <TextBlock Text="常用向量模型快速下载:" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <UniformGrid Columns="2" Margin="0,0,0,8">
                                        <Button Content="下载 text-embedding-bge-m3" Margin="0,0,4,4"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="text-embedding-bge-m3"
                                              ToolTip="下载BGE-M3多语言向量模型，支持中英文"/>
                                        <Button Content="下载 nomic-embed-text" Margin="4,0,0,4"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="nomic-embed-text"
                                              ToolTip="下载Nomic文本向量模型，轻量级高效"/>
                                        <Button Content="下载 mxbai-embed-large" Margin="0,4,4,0"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="mxbai-embed-large"
                                              ToolTip="下载MixedBread AI大型向量模型"/>
                                        <Button Content="下载 bge-large-zh" Margin="4,4,0,0"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="bge-large-zh"
                                              ToolTip="下载BGE中文大型向量模型"/>
                                    </UniformGrid>

                                    <DockPanel Margin="0,8,0,0">
                                        <TextBox x:Name="CustomModelTextBox"
                                               materialDesign:HintAssist.Hint="自定义向量模型名称"
                                               DockPanel.Dock="Left" Margin="0,0,8,0"/>
                                        <Button Content="下载" DockPanel.Dock="Right"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Click="DownloadCustomModel_Click"
                                              ToolTip="下载自定义向量模型"/>
                                    </DockPanel>

                                    <TextBlock x:Name="DownloadStatusText" Text=""
                                             Margin="0,8,0,0" FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                                    <ProgressBar x:Name="DownloadProgressBar"
                                               Margin="0,4,0,0" Height="4"
                                               Visibility="Collapsed"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>

                        <!-- LM Studio 向量模型配置 -->
                        <StackPanel x:Name="LMStudioConfig" Visibility="Collapsed">
                            <TextBox x:Name="LMStudioUrlTextBox"
                                   materialDesign:HintAssist.Hint="LM Studio服务地址"
                                   Text="http://localhost:1234"
                                   Margin="0,0,0,16"
                                   ToolTip="LM Studio服务的地址，默认为本地1234端口。确保LM Studio已启动并加载了向量模型。"/>

                            <DockPanel Margin="0,0,0,16">
                                <TextBlock Text="可用向量模型:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <Button x:Name="DetectLMStudioModelsButton" Content="检测模型"
                                      Style="{StaticResource MaterialDesignFlatButton}"
                                      DockPanel.Dock="Right"
                                      Click="DetectModels_Click"
                                      ToolTip="自动检测LM Studio中已加载的向量模型"/>
                            </DockPanel>

                            <ComboBox x:Name="LMStudioModelComboBox"
                                    materialDesign:HintAssist.Hint="选择向量模型"
                                    DisplayMemberPath="Name"
                                    SelectedValuePath="Id"
                                    ToolTip="选择要使用的LM Studio向量模型。如果列表为空，请确保LM Studio已加载向量模型并点击'检测模型'。"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 高级设置 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16">
                        <TextBlock Text="高级设置" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <StackPanel>
                            <TextBox x:Name="TimeoutTextBox"
                                   materialDesign:HintAssist.Hint="请求超时(秒)"
                                   Text="30"
                                   Margin="0,0,0,16"
                                   ToolTip="向量模型API请求的超时时间。建议范围：10-120秒"/>

                            <CheckBox x:Name="EnableVectorCacheCheckBox"
                                    Content="启用向量缓存"
                                    IsChecked="True"
                                    ToolTip="启用后将缓存向量化结果，提高重复查询的性能"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <ProgressBar x:Name="StatusProgressBar"
                               Width="20" Height="20"
                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="False"
                               Visibility="Collapsed"
                               Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="就绪" VerticalAlignment="Center"
                             Foreground="White"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Content="取消" Style="{StaticResource MaterialDesignFlatButton}"
                          Margin="0,0,8,0" Click="Cancel_Click"/>
                    <Button x:Name="SaveButton" Content="保存配置"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="Save_Click"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>

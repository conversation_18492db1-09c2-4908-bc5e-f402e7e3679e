using System.Windows;
using System.Windows.Controls;
using DocumentCreationSystem.Models;
using MaterialDesignThemes.Wpf;

namespace DocumentCreationSystem.Views;

/// <summary>
/// 工具参数输入对话框
/// </summary>
public partial class ToolParameterDialog : Window
{
    private readonly ProjectTool _tool;
    private readonly Dictionary<string, Control> _parameterControls;

    public ToolParameterDialog(ProjectTool tool)
    {
        InitializeComponent();
        
        _tool = tool;
        _parameterControls = new Dictionary<string, Control>();
        
        TitleText.Text = $"{tool.Name} - 参数设置";
        
        CreateParameterControls();
    }

    private void CreateParameterControls()
    {
        foreach (var parameter in _tool.Parameters)
        {
            var parameterPanel = new StackPanel { Margin = new Thickness(0, 0, 0, 16) };
            
            // 参数标签
            var label = new TextBlock
            {
                Text = parameter.DisplayName + (parameter.IsRequired ? " *" : ""),
                FontWeight = parameter.IsRequired ? FontWeights.Medium : FontWeights.Normal,
                Margin = new Thickness(0, 0, 0, 8)
            };
            parameterPanel.Children.Add(label);
            
            // 参数描述
            if (!string.IsNullOrEmpty(parameter.Description))
            {
                var description = new TextBlock
                {
                    Text = parameter.Description,
                    FontSize = 12,
                    Foreground = (System.Windows.Media.Brush)FindResource("MaterialDesignBodyLight"),
                    Margin = new Thickness(0, 0, 0, 8)
                };
                parameterPanel.Children.Add(description);
            }
            
            // 参数输入控件
            Control inputControl = parameter.Type.ToLower() switch
            {
                "boolean" => CreateBooleanControl(parameter),
                "integer" => CreateIntegerControl(parameter),
                "float" => CreateFloatControl(parameter),
                "string" when parameter.Options.Any() => CreateComboBoxControl(parameter),
                "string" => CreateTextControl(parameter),
                _ => CreateTextControl(parameter)
            };
            
            parameterPanel.Children.Add(inputControl);
            _parameterControls[parameter.Name] = inputControl;
            
            ParametersPanel.Children.Add(parameterPanel);
        }
    }

    private Control CreateBooleanControl(ToolParameter parameter)
    {
        var checkBox = new CheckBox
        {
            Content = parameter.DisplayName,
            IsChecked = parameter.DefaultValue as bool? ?? false
        };
        return checkBox;
    }

    private Control CreateIntegerControl(ToolParameter parameter)
    {
        var textBox = new TextBox
        {
            Style = (Style)FindResource("MaterialDesignOutlinedTextBox"),
            Text = parameter.DefaultValue?.ToString() ?? ""
        };
        
        HintAssist.SetHint(textBox, "请输入整数");
        HintAssist.SetIsFloating(textBox, true);
        
        return textBox;
    }

    private Control CreateFloatControl(ToolParameter parameter)
    {
        var textBox = new TextBox
        {
            Style = (Style)FindResource("MaterialDesignOutlinedTextBox"),
            Text = parameter.DefaultValue?.ToString() ?? ""
        };
        
        HintAssist.SetHint(textBox, "请输入小数");
        HintAssist.SetIsFloating(textBox, true);
        
        return textBox;
    }

    private Control CreateTextControl(ToolParameter parameter)
    {
        var textBox = new TextBox
        {
            Style = (Style)FindResource("MaterialDesignOutlinedTextBox"),
            Text = parameter.DefaultValue?.ToString() ?? ""
        };
        
        HintAssist.SetHint(textBox, parameter.DisplayName);
        HintAssist.SetIsFloating(textBox, true);
        
        return textBox;
    }

    private Control CreateComboBoxControl(ToolParameter parameter)
    {
        var comboBox = new ComboBox
        {
            Style = (Style)FindResource("MaterialDesignOutlinedComboBox")
        };
        
        HintAssist.SetHint(comboBox, parameter.DisplayName);
        HintAssist.SetIsFloating(comboBox, true);
        
        foreach (var option in parameter.Options)
        {
            var item = new ComboBoxItem
            {
                Content = option,
                Tag = option
            };
            
            if (option == parameter.DefaultValue?.ToString())
            {
                item.IsSelected = true;
            }
            
            comboBox.Items.Add(item);
        }
        
        return comboBox;
    }

    public Dictionary<string, object> GetParameters()
    {
        var parameters = new Dictionary<string, object>();
        
        foreach (var parameter in _tool.Parameters)
        {
            if (_parameterControls.TryGetValue(parameter.Name, out var control))
            {
                object? value = control switch
                {
                    CheckBox checkBox => checkBox.IsChecked ?? false,
                    TextBox textBox => ConvertTextBoxValue(textBox.Text, parameter.Type),
                    ComboBox comboBox => (comboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "",
                    _ => null
                };
                
                if (value != null)
                {
                    parameters[parameter.Name] = value;
                }
            }
        }
        
        return parameters;
    }

    private object ConvertTextBoxValue(string text, string type)
    {
        return type.ToLower() switch
        {
            "integer" => int.TryParse(text, out var intValue) ? intValue : 0,
            "float" => float.TryParse(text, out var floatValue) ? floatValue : 0f,
            _ => text
        };
    }

    private bool ValidateParameters()
    {
        foreach (var parameter in _tool.Parameters.Where(p => p.IsRequired))
        {
            if (_parameterControls.TryGetValue(parameter.Name, out var control))
            {
                var isEmpty = control switch
                {
                    TextBox textBox => string.IsNullOrWhiteSpace(textBox.Text),
                    ComboBox comboBox => comboBox.SelectedItem == null,
                    _ => false
                };
                
                if (isEmpty)
                {
                    MessageBox.Show($"请填写必需参数: {parameter.DisplayName}", "参数验证", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }
        }
        
        return true;
    }

    private void OK_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateParameters())
        {
            DialogResult = true;
            Close();
        }
    }

    private void Cancel_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}

namespace DocumentCreationSystem.Models;

/// <summary>
/// AI模型配置
/// </summary>
public class AIModelConfig
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 选择的AI平台
    /// </summary>
    public string Platform { get; set; } = "Ollama";

    /// <summary>
    /// 温度参数
    /// </summary>
    public float Temperature { get; set; } = 0.7f;

    /// <summary>
    /// 最大Token数
    /// </summary>
    public int MaxTokens { get; set; } = 2000;

    /// <summary>
    /// 是否启用思维链处理
    /// </summary>
    public bool EnableThinkingChain { get; set; } = true;

    /// <summary>
    /// 是否启用分段生成（启用后可以避免长文本生成失败）
    /// </summary>
    public bool EnableSegmentedGeneration { get; set; } = true;

    /// <summary>
    /// 分段生成阈值（字数超过此值时使用分段生成）
    /// </summary>
    public int SegmentationThreshold { get; set; } = 3000;

    /// <summary>
    /// 分段生成时每段的字数（用户可设置）
    /// </summary>
    public int SegmentWordCount { get; set; } = 1500;

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int Timeout { get; set; } = 120;

    /// <summary>
    /// Ollama配置
    /// </summary>
    public OllamaConfig OllamaConfig { get; set; } = new();

    /// <summary>
    /// LM Studio配置
    /// </summary>
    public LMStudioConfig LMStudioConfig { get; set; } = new();

    /// <summary>
    /// 智谱AI配置
    /// </summary>
    public ZhipuAIConfig ZhipuAIConfig { get; set; } = new();

    /// <summary>
    /// DeepSeek配置
    /// </summary>
    public DeepSeekConfig DeepSeekConfig { get; set; } = new();

    /// <summary>
    /// OpenAI自定义配置
    /// </summary>
    public OpenAIConfig OpenAIConfig { get; set; } = new();

    /// <summary>
    /// 阿里模型配置
    /// </summary>
    public AlibabaConfig AlibabaConfig { get; set; } = new();

    /// <summary>
    /// RWKV模型配置
    /// </summary>
    public RWKVConfig RWKVConfig { get; set; } = new();
}

/// <summary>
/// Ollama配置
/// </summary>
public class OllamaConfig
{
    /// <summary>
    /// 服务地址
    /// </summary>
    public string BaseUrl { get; set; } = "http://localhost:11434";

    /// <summary>
    /// 选择的模型
    /// </summary>
    public string SelectedModel { get; set; } = "";

    /// <summary>
    /// 可用模型列表
    /// </summary>
    public List<AIModel> AvailableModels { get; set; } = new();
}

/// <summary>
/// LM Studio配置
/// </summary>
public class LMStudioConfig
{
    /// <summary>
    /// 服务地址
    /// </summary>
    public string BaseUrl { get; set; } = "http://50844s9656.wocp.fun:42440";

    /// <summary>
    /// 选择的模型
    /// </summary>
    public string SelectedModel { get; set; } = "";

    /// <summary>
    /// 可用模型列表
    /// </summary>
    public List<AIModel> AvailableModels { get; set; } = new();
}

/// <summary>
/// 智谱AI配置
/// </summary>
public class ZhipuAIConfig
{
    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://open.bigmodel.cn/api/paas/v4";

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Model { get; set; } = "GLM-4-Flash-250414";

    /// <summary>
    /// 预设模型列表
    /// </summary>
    public static readonly List<string> PresetModels = new()
    {
        "GLM-4-Flash-250414",      // 对话模型 - 主要用于文本创作
        "GLM-4.1V-Thinking-Flash", // 视觉推理模型 - 支持图像理解和推理
        "GLM-4V-Flash",            // 图像理解模型 - 专门用于图像分析
        "GLM-Z1-Flash",            // 推理模型 - 适合复杂逻辑推理
        "Cogview-3-Flash",         // 图像生成模型 - 用于生成图像
        "CogVideoX-Flash"          // 视频生成模型 - 用于生成视频
    };
}

/// <summary>
/// DeepSeek配置
/// </summary>
public class DeepSeekConfig
{
    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://api.deepseek.com";

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Model { get; set; } = "deepseek-chat";

    /// <summary>
    /// 预设模型列表
    /// </summary>
    public static readonly List<string> PresetModels = new()
    {
        "deepseek-chat",     // 主要对话模型 - 适合文本创作和对话
        "deepseek-coder",    // 代码生成模型 - 专门用于编程
        "deepseek-reasoner"  // 推理模型 - 适合复杂逻辑推理
    };
}

/// <summary>
/// OpenAI自定义配置
/// </summary>
public class OpenAIConfig
{
    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://api.openai.com/v1";

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Model { get; set; } = "gpt-3.5-turbo";

    /// <summary>
    /// 预设模型列表
    /// </summary>
    public static readonly List<string> PresetModels = new()
    {
        "gpt-3.5-turbo",        // GPT-3.5 Turbo - 快速响应，成本较低
        "gpt-4",                // GPT-4 - 高质量推理和创作
        "gpt-4-turbo",          // GPT-4 Turbo - 更快的GPT-4版本
        "gpt-4o",               // GPT-4o - 多模态模型
        "gpt-4o-mini",          // GPT-4o Mini - 轻量级多模态模型
        "o1-preview",           // O1 Preview - 高级推理模型
        "o1-mini",              // O1 Mini - 轻量级推理模型
        "Qwen3-30B-A3B",        // 通义千问3-30B-A3B - 本地部署模型
        "Qwen3-32B",            // 通义千问3-32B - 本地部署模型
        "QwQ-32B",              // QwQ-32B - 本地部署模型
        "QwQ-32B-AWQ"           // QwQ-32B-AWQ - 量化版本本地部署模型
    };
}

/// <summary>
/// 阿里模型配置
/// </summary>
public class AlibabaConfig
{
    /// <summary>
    /// API密钥
    /// </summary>
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// 基础URL
    /// </summary>
    public string BaseUrl { get; set; } = "https://dashscope.aliyuncs.com/api/v1";

    /// <summary>
    /// 模型名称
    /// </summary>
    public string Model { get; set; } = "qwen-turbo";

    /// <summary>
    /// 预设模型列表
    /// </summary>
    public static readonly List<string> PresetModels = new()
    {
        "qwen-turbo",           // 通义千问Turbo - 快速响应
        "qwen-plus",            // 通义千问Plus - 平衡性能和成本
        "qwen-max",             // 通义千问Max - 最高性能
        "qwen-max-longcontext", // 通义千问Max长文本 - 支持长文本处理
        "qwen-vl-plus",         // 通义千问视觉Plus - 图像理解
        "qwen-vl-max",          // 通义千问视觉Max - 高级图像理解
        "qwen-audio-turbo",     // 通义千问音频Turbo - 音频处理
        "qwen-coder-turbo"      // 通义千问代码Turbo - 代码生成
    };
}

/// <summary>
/// 向量模型配置
/// </summary>
public class VectorModelConfig
{
    /// <summary>
    /// 配置ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 选择的向量模型平台
    /// </summary>
    public string Platform { get; set; } = "Ollama";

    /// <summary>
    /// 请求超时时间（秒）
    /// </summary>
    public int Timeout { get; set; } = 120;

    /// <summary>
    /// Ollama向量模型配置
    /// </summary>
    public OllamaVectorConfig OllamaVectorConfig { get; set; } = new();

    /// <summary>
    /// LM Studio向量模型配置
    /// </summary>
    public LMStudioVectorConfig LMStudioVectorConfig { get; set; } = new();
}

/// <summary>
/// Ollama向量模型配置
/// </summary>
public class OllamaVectorConfig
{
    /// <summary>
    /// 服务地址
    /// </summary>
    public string BaseUrl { get; set; } = "http://localhost:11434";

    /// <summary>
    /// 选择的向量模型
    /// </summary>
    public string SelectedModel { get; set; } = "";

    /// <summary>
    /// 可用向量模型列表
    /// </summary>
    public List<AIModel> AvailableModels { get; set; } = new();

    /// <summary>
    /// 推荐的向量模型列表
    /// </summary>
    public static readonly List<string> RecommendedModels = new()
    {
        "text-embedding-bge-m3",      // BGE-M3 多语言向量模型
        "nomic-embed-text",           // Nomic 文本向量模型
        "mxbai-embed-large",          // MixedBread AI 大型向量模型
        "all-minilm",                 // 轻量级向量模型
        "bge-large-zh",               // BGE 中文大型向量模型
        "bge-base-zh"                 // BGE 中文基础向量模型
    };
}

/// <summary>
/// LM Studio向量模型配置
/// </summary>
public class LMStudioVectorConfig
{
    /// <summary>
    /// 服务地址
    /// </summary>
    public string BaseUrl { get; set; } = "http://50844s9656.wocp.fun:42440";

    /// <summary>
    /// 选择的向量模型
    /// </summary>
    public string SelectedModel { get; set; } = "";

    /// <summary>
    /// 可用向量模型列表
    /// </summary>
    public List<AIModel> AvailableModels { get; set; } = new();
}

/// <summary>
/// AI模型配置服务接口
/// </summary>
public interface IAIModelConfigService
{
    /// <summary>
    /// 获取当前配置
    /// </summary>
    Task<AIModelConfig> GetConfigAsync();

    /// <summary>
    /// 保存配置
    /// </summary>
    Task SaveConfigAsync(AIModelConfig config);

    /// <summary>
    /// 检测Ollama模型
    /// </summary>
    Task<List<AIModel>> DetectOllamaModelsAsync(string baseUrl);

    /// <summary>
    /// 检测LM Studio模型
    /// </summary>
    Task<List<AIModel>> DetectLMStudioModelsAsync(string baseUrl);

    /// <summary>
    /// 测试连接
    /// </summary>
    Task<bool> TestConnectionAsync(AIModelConfig config);

    /// <summary>
    /// 获取默认配置
    /// </summary>
    AIModelConfig GetDefaultConfig();

    /// <summary>
    /// 强制创建默认配置文件
    /// </summary>
    Task CreateDefaultConfigFileAsync();
}

/// <summary>
/// 向量模型配置服务接口
/// </summary>
public interface IVectorModelConfigService
{
    /// <summary>
    /// 获取当前向量模型配置
    /// </summary>
    Task<VectorModelConfig> GetConfigAsync();

    /// <summary>
    /// 保存向量模型配置
    /// </summary>
    Task SaveConfigAsync(VectorModelConfig config);

    /// <summary>
    /// 检测Ollama向量模型
    /// </summary>
    Task<List<AIModel>> DetectOllamaVectorModelsAsync(string baseUrl);

    /// <summary>
    /// 检测LM Studio向量模型
    /// </summary>
    Task<List<AIModel>> DetectLMStudioVectorModelsAsync(string baseUrl);

    /// <summary>
    /// 测试向量模型连接
    /// </summary>
    Task<bool> TestVectorConnectionAsync(VectorModelConfig config);

    /// <summary>
    /// 获取默认向量模型配置
    /// </summary>
    VectorModelConfig GetDefaultConfig();

    /// <summary>
    /// 强制创建默认向量模型配置文件
    /// </summary>
    Task CreateDefaultConfigFileAsync();
}

/// <summary>
/// RWKV配置
/// </summary>
public class RWKVConfig
{
    /// <summary>
    /// RWKV API服务地址
    /// </summary>
    public string BaseUrl { get; set; } = "http://localhost:8000";

    /// <summary>
    /// API密钥（可选）
    /// </summary>
    public string ApiKey { get; set; } = "";

    /// <summary>
    /// 选择的模型
    /// </summary>
    public string SelectedModel { get; set; } = "";

    /// <summary>
    /// 可用模型列表
    /// </summary>
    public List<AIModel> AvailableModels { get; set; } = new();
}

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;
using System.Text.Json;
using System.IO;

namespace DocumentCreationSystem;

/// <summary>
/// 测试AI配置平台切换
/// </summary>
public class TestConfigSwitch
{
    public static async Task Main(string[] args)
    {
        // 设置服务
        var services = new ServiceCollection();
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        services.AddHttpClient();
        
        // 添加配置
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false)
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // 添加AI配置服务
        services.AddScoped<IAIModelConfigService, AIModelConfigService>();
        
        var serviceProvider = services.BuildServiceProvider();
        var logger = serviceProvider.GetRequiredService<ILogger<TestConfigSwitch>>();
        var configService = serviceProvider.GetRequiredService<IAIModelConfigService>();

        try
        {
            logger.LogInformation("=== AI配置平台切换测试 ===");
            
            // 1. 获取当前配置
            logger.LogInformation("1. 获取当前配置...");
            var currentConfig = await configService.GetConfigAsync();
            logger.LogInformation($"当前平台: {currentConfig.Platform}");
            
            // 2. 测试切换到LM Studio
            logger.LogInformation("\n2. 测试切换到LM Studio平台...");
            var lmStudioConfig = new AIModelConfig
            {
                Platform = "LMStudio",
                Temperature = 0.7f,
                MaxTokens = 2000,
                EnableThinkingChain = true,
                Timeout = 30,
                OllamaConfig = new OllamaConfig
                {
                    BaseUrl = "http://localhost:11434",
                    SelectedModel = "llama3.2:latest"
                },
                LMStudioConfig = new LMStudioConfig
                {
                    BaseUrl = "http://localhost:1234",
                    SelectedModel = "test-lmstudio-model"
                },
                ZhipuAIConfig = new ZhipuAIConfig
                {
                    ApiKey = "test-zhipu-key",
                    BaseUrl = "https://open.bigmodel.cn/api/paas/v4",
                    Model = "GLM-4-Flash-250414"
                },
                DeepSeekConfig = new DeepSeekConfig
                {
                    ApiKey = "test-deepseek-key",
                    BaseUrl = "https://api.deepseek.com",
                    Model = "deepseek-chat"
                }
            };
            
            await configService.SaveConfigAsync(lmStudioConfig);
            logger.LogInformation("LM Studio配置保存完成");
            
            // 3. 重新读取配置验证
            logger.LogInformation("\n3. 重新读取配置验证...");
            var reloadedConfig = await configService.GetConfigAsync();
            logger.LogInformation($"重新加载的平台: {reloadedConfig.Platform}");
            logger.LogInformation($"LMStudio选中模型: {reloadedConfig.LMStudioConfig?.SelectedModel ?? "无"}");
            
            // 4. 测试切换到智谱AI
            logger.LogInformation("\n4. 测试切换到智谱AI平台...");
            var zhipuConfig = new AIModelConfig
            {
                Platform = "ZhipuAI",
                Temperature = 0.8f,
                MaxTokens = 4000,
                EnableThinkingChain = true,
                Timeout = 60,
                OllamaConfig = new OllamaConfig
                {
                    BaseUrl = "http://localhost:11434",
                    SelectedModel = "llama3.2:latest"
                },
                LMStudioConfig = new LMStudioConfig
                {
                    BaseUrl = "http://localhost:1234",
                    SelectedModel = "test-lmstudio-model"
                },
                ZhipuAIConfig = new ZhipuAIConfig
                {
                    ApiKey = "test-zhipu-key-updated",
                    BaseUrl = "https://open.bigmodel.cn/api/paas/v4",
                    Model = "GLM-4V-Flash"
                },
                DeepSeekConfig = new DeepSeekConfig
                {
                    ApiKey = "test-deepseek-key",
                    BaseUrl = "https://api.deepseek.com",
                    Model = "deepseek-chat"
                }
            };
            
            await configService.SaveConfigAsync(zhipuConfig);
            logger.LogInformation("智谱AI配置保存完成");
            
            // 5. 再次验证
            logger.LogInformation("\n5. 再次验证配置...");
            var finalConfig = await configService.GetConfigAsync();
            logger.LogInformation($"最终平台: {finalConfig.Platform}");
            logger.LogInformation($"智谱AI模型: {finalConfig.ZhipuAIConfig?.Model ?? "无"}");
            logger.LogInformation($"智谱AI API Key: {finalConfig.ZhipuAIConfig?.ApiKey ?? "无"}");
            
            // 6. 恢复原始配置
            logger.LogInformation("\n6. 恢复原始配置...");
            await configService.SaveConfigAsync(currentConfig);
            logger.LogInformation("原始配置已恢复");
            
            // 7. 验证恢复
            var restoredConfig = await configService.GetConfigAsync();
            logger.LogInformation($"恢复后的平台: {restoredConfig.Platform}");
            
            // 8. 显示配置文件内容
            var configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "DocumentCreationSystem", "ai-config.json");
            logger.LogInformation($"\n配置文件路径: {configPath}");
            
            if (File.Exists(configPath))
            {
                var configContent = await File.ReadAllTextAsync(configPath);
                logger.LogInformation("最终配置文件内容:");
                logger.LogInformation(configContent);
            }
            
            logger.LogInformation("\n=== 配置平台切换测试完成 ===");
            logger.LogInformation("✅ 所有测试通过，配置保存和加载功能正常");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "测试过程中发生错误");
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
}

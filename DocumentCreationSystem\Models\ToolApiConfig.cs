using System;
using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 工具API配置模型
    /// </summary>
    public class ToolApiConfig
    {
        /// <summary>
        /// 搜索API配置
        /// </summary>
        public SearchApiConfig SearchConfig { get; set; } = new();

        /// <summary>
        /// 翻译API配置
        /// </summary>
        public TranslateApiConfig TranslateConfig { get; set; } = new();

        /// <summary>
        /// 天气API配置
        /// </summary>
        public WeatherApiConfig WeatherConfig { get; set; } = new();

        /// <summary>
        /// 新闻API配置
        /// </summary>
        public NewsApiConfig NewsConfig { get; set; } = new();

        /// <summary>
        /// 验证配置是否有效（用于保存，允许空APIkey）
        /// </summary>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            // 对于保存操作，我们允许空的APIkey，只验证格式
            // 工具的启用状态将根据APIkey是否为空来自动确定
            return (true, string.Empty);
        }

        /// <summary>
        /// 检查各个工具是否可用（有有效的APIkey）
        /// </summary>
        /// <returns>工具可用性状态</returns>
        public ToolAvailabilityStatus GetToolAvailability()
        {
            return new ToolAvailabilityStatus
            {
                IsSearchEnabled = SearchConfig.IsEnabled(),
                IsTranslateEnabled = TranslateConfig.IsEnabled(),
                IsWeatherEnabled = WeatherConfig.IsEnabled(),
                IsNewsEnabled = NewsConfig.IsEnabled()
            };
        }
    }

    /// <summary>
    /// 工具可用性状态
    /// </summary>
    public class ToolAvailabilityStatus
    {
        public bool IsSearchEnabled { get; set; }
        public bool IsTranslateEnabled { get; set; }
        public bool IsWeatherEnabled { get; set; }
        public bool IsNewsEnabled { get; set; }
    }

    /// <summary>
    /// 搜索API配置
    /// </summary>
    public class SearchApiConfig
    {
        /// <summary>
        /// 搜索引擎类型：Google, Bing, Serper
        /// </summary>
        public string SearchEngine { get; set; } = "Google";

        /// <summary>
        /// Google搜索配置
        /// </summary>
        public GoogleSearchConfig GoogleConfig { get; set; } = new();

        /// <summary>
        /// Bing搜索配置
        /// </summary>
        public BingSearchConfig BingConfig { get; set; } = new();

        /// <summary>
        /// Serper搜索配置
        /// </summary>
        public SerperSearchConfig SerperConfig { get; set; } = new();

        /// <summary>
        /// 搜索结果数量
        /// </summary>
        [Range(1, 50, ErrorMessage = "搜索结果数量必须在1-50之间")]
        public int ResultCount { get; set; } = 10;

        /// <summary>
        /// 搜索语言
        /// </summary>
        public string Language { get; set; } = "zh-CN";

        /// <summary>
        /// 是否启用安全搜索
        /// </summary>
        public bool SafeSearch { get; set; } = true;

        /// <summary>
        /// 验证配置（严格验证，用于运行时检查）
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            switch (SearchEngine.ToLower())
            {
                case "google":
                    return GoogleConfig.Validate();
                case "bing":
                    return BingConfig.Validate();
                case "serper":
                    return SerperConfig.Validate();
                default:
                    return (false, "未知的搜索引擎类型");
            }
        }

        /// <summary>
        /// 检查搜索工具是否可用（有有效的APIkey）
        /// </summary>
        public bool IsEnabled()
        {
            switch (SearchEngine.ToLower())
            {
                case "google":
                    return !string.IsNullOrWhiteSpace(GoogleConfig.ApiKey) &&
                           !string.IsNullOrWhiteSpace(GoogleConfig.SearchEngineId);
                case "bing":
                    return !string.IsNullOrWhiteSpace(BingConfig.ApiKey);
                case "serper":
                    return !string.IsNullOrWhiteSpace(SerperConfig.ApiKey);
                default:
                    return false;
            }
        }
    }

    /// <summary>
    /// Google搜索配置
    /// </summary>
    public class GoogleSearchConfig
    {
        /// <summary>
        /// Google API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 搜索引擎ID
        /// </summary>
        public string SearchEngineId { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置（严格验证，用于运行时检查）
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                return (false, "Google API密钥不能为空");
            }

            if (string.IsNullOrWhiteSpace(SearchEngineId))
            {
                return (false, "Google搜索引擎ID不能为空");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证配置（宽松验证，用于保存）
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateForSave()
        {
            // 保存时允许空值，工具将被禁用
            return (true, string.Empty);
        }
    }

    /// <summary>
    /// Bing搜索配置
    /// </summary>
    public class BingSearchConfig
    {
        /// <summary>
        /// Bing API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置（严格验证，用于运行时检查）
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                return (false, "Bing API密钥不能为空");
            }

            return (true, string.Empty);
        }

        /// <summary>
        /// 验证配置（宽松验证，用于保存）
        /// </summary>
        public (bool IsValid, string ErrorMessage) ValidateForSave()
        {
            return (true, string.Empty);
        }
    }

    /// <summary>
    /// Serper搜索配置
    /// </summary>
    public class SerperSearchConfig
    {
        /// <summary>
        /// Serper API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                return (false, "Serper API密钥不能为空");
            }

            return (true, string.Empty);
        }
    }

    /// <summary>
    /// 翻译API配置
    /// </summary>
    public class TranslateApiConfig
    {
        /// <summary>
        /// 翻译服务类型：Google, Baidu, Tencent
        /// </summary>
        public string TranslateService { get; set; } = "Google";

        /// <summary>
        /// Google翻译配置
        /// </summary>
        public GoogleTranslateConfig GoogleConfig { get; set; } = new();

        /// <summary>
        /// 百度翻译配置
        /// </summary>
        public BaiduTranslateConfig BaiduConfig { get; set; } = new();

        /// <summary>
        /// 腾讯翻译配置
        /// </summary>
        public TencentTranslateConfig TencentConfig { get; set; } = new();

        /// <summary>
        /// 验证配置（严格验证，用于运行时检查）
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            switch (TranslateService.ToLower())
            {
                case "google":
                    return GoogleConfig.Validate();
                case "baidu":
                    return BaiduConfig.Validate();
                case "tencent":
                    return TencentConfig.Validate();
                default:
                    return (false, "未知的翻译服务类型");
            }
        }

        /// <summary>
        /// 检查翻译工具是否可用（有有效的APIkey）
        /// </summary>
        public bool IsEnabled()
        {
            switch (TranslateService.ToLower())
            {
                case "google":
                    return !string.IsNullOrWhiteSpace(GoogleConfig.ApiKey);
                case "baidu":
                    return !string.IsNullOrWhiteSpace(BaiduConfig.AppId) &&
                           !string.IsNullOrWhiteSpace(BaiduConfig.SecretKey);
                case "tencent":
                    return !string.IsNullOrWhiteSpace(TencentConfig.SecretId) &&
                           !string.IsNullOrWhiteSpace(TencentConfig.SecretKey);
                default:
                    return false;
            }
        }
    }

    /// <summary>
    /// Google翻译配置
    /// </summary>
    public class GoogleTranslateConfig
    {
        /// <summary>
        /// Google翻译API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(ApiKey))
            {
                return (false, "Google翻译API密钥不能为空");
            }

            return (true, string.Empty);
        }
    }

    /// <summary>
    /// 百度翻译配置
    /// </summary>
    public class BaiduTranslateConfig
    {
        /// <summary>
        /// 百度翻译APP ID
        /// </summary>
        public string AppId { get; set; } = string.Empty;

        /// <summary>
        /// 百度翻译密钥
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(AppId))
            {
                return (false, "百度翻译APP ID不能为空");
            }

            if (string.IsNullOrWhiteSpace(SecretKey))
            {
                return (false, "百度翻译密钥不能为空");
            }

            return (true, string.Empty);
        }
    }

    /// <summary>
    /// 腾讯翻译配置
    /// </summary>
    public class TencentTranslateConfig
    {
        /// <summary>
        /// 腾讯云SecretId
        /// </summary>
        public string SecretId { get; set; } = string.Empty;

        /// <summary>
        /// 腾讯云SecretKey
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            if (string.IsNullOrWhiteSpace(SecretId))
            {
                return (false, "腾讯云SecretId不能为空");
            }

            if (string.IsNullOrWhiteSpace(SecretKey))
            {
                return (false, "腾讯云SecretKey不能为空");
            }

            return (true, string.Empty);
        }
    }

    /// <summary>
    /// 天气API配置
    /// </summary>
    public class WeatherApiConfig
    {
        /// <summary>
        /// OpenWeatherMap API密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            // 天气API是可选的，所以即使为空也返回有效
            return (true, string.Empty);
        }

        /// <summary>
        /// 检查天气工具是否可用
        /// </summary>
        public bool IsEnabled()
        {
            return !string.IsNullOrWhiteSpace(ApiKey);
        }
    }

    /// <summary>
    /// 新闻API配置
    /// </summary>
    public class NewsApiConfig
    {
        /// <summary>
        /// NewsAPI密钥
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 验证配置
        /// </summary>
        public (bool IsValid, string ErrorMessage) Validate()
        {
            // 新闻API是可选的，所以即使为空也返回有效
            return (true, string.Empty);
        }

        /// <summary>
        /// 检查新闻工具是否可用
        /// </summary>
        public bool IsEnabled()
        {
            return !string.IsNullOrWhiteSpace(ApiKey);
        }
    }
}

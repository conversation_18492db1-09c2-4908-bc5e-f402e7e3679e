@echo off
REM 设置编码为UTF-8
chcp 65001 > nul

REM 清除Conda环境变量以避免编码冲突
set CONDA_DEFAULT_ENV=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=

REM 设置.NET环境
set DOTNET_CLI_TELEMETRY_OPTOUT=1

echo 正在编译项目...
echo.

REM 切换到项目目录
cd /d "d:\AI_project\文档管理及创作系统\DocumentCreationSystem"

REM 清理项目
echo 清理项目...
dotnet clean

echo.
echo 恢复NuGet包...
dotnet restore

echo.
echo 编译项目...
dotnet build --configuration Release

echo.
echo 编译完成！
pause

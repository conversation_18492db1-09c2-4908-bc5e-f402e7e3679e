using System;
using System.Text.RegularExpressions;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 简单的思维链测试程序
    /// </summary>
    public class SimpleThinkingChainTest
    {
        // 思维链标记模式 - 支持多种格式
        private static readonly Regex ThinkingChainPattern = new(@"<thinking>(.*?)</thinking>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex ThinkChainPattern = new(@"<think>(.*?)</think>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex QwenThinkPattern = new(@"(.*?)</think>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex OutputPattern = new(@"<o>(.*?)</o>", RegexOptions.Singleline | RegexOptions.IgnoreCase);

        public static void Main(string[] args)
        {
            Console.WriteLine("=== Qwen 思维链格式测试 ===\n");

            TestQwenFormat();
            TestMixedFormats();
            TestEdgeCases();

            Console.WriteLine("=== 所有测试完成 ===");
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试 Qwen 格式 (...</think>)
        /// </summary>
        private static void TestQwenFormat()
        {
            Console.WriteLine("1. 测试 Qwen 思维链格式 (...</think>)");
            Console.WriteLine("----------------------------------------");
            
            var input = @"我需要分析这个问题。

首先，让我理解用户的需求。用户想要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。

然后，我需要查看当前的实现，看看如何添加这种新格式的支持。

最后，我需要修改正则表达式和相关的处理逻辑。
</think>

根据您的需求，我需要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。这是 Qwen 模型使用的思维链格式。

让我为您实现这个功能：

1. 添加新的正则表达式模式
2. 更新解析逻辑
3. 测试新格式的支持";

            var result = ProcessThinkingChain(input);
            
            Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
            Console.WriteLine($"思维链格式: {result.Format}");
            Console.WriteLine($"处理后内容长度: {result.FinalOutput.Length}");
            Console.WriteLine();
            
            Console.WriteLine("处理后内容:");
            Console.WriteLine(result.FinalOutput.Trim());
            Console.WriteLine();
            
            if (result.HasThinkingChain)
            {
                Console.WriteLine("提取的思维链内容:");
                Console.WriteLine(result.ThinkingChain);
                Console.WriteLine();
            }
        }

        /// <summary>
        /// 测试混合格式
        /// </summary>
        private static void TestMixedFormats()
        {
            Console.WriteLine("2. 测试混合格式支持");
            Console.WriteLine("----------------------------------------");
            
            // 测试标准格式
            TestFormat("标准格式 (<think> + <o>)", @"<think>
这是标准格式的思考过程
</think>

<o>
这是标准格式的输出
</o>");

            // 测试传统格式
            TestFormat("传统格式 (<thinking>)", @"<thinking>
这是传统格式的思考过程
</thinking>

这是传统格式的输出");

            // 测试 Qwen 格式
            TestFormat("Qwen 格式 (...</think>)", @"这是 Qwen 格式的思考过程
分析问题的各个方面
</think>

这是 Qwen 格式的输出");
        }

        /// <summary>
        /// 测试边界情况
        /// </summary>
        private static void TestEdgeCases()
        {
            Console.WriteLine("3. 测试边界情况");
            Console.WriteLine("----------------------------------------");
            
            // 测试空内容
            TestFormat("空思维链", @"</think>

这是输出内容");

            // 测试多行思维链
            TestFormat("多行思维链", @"第一行思考
第二行思考
第三行思考
更多的思考内容...
</think>

这是最终的输出结果");

            // 测试包含特殊字符
            TestFormat("包含特殊字符", @"思考过程中包含 <标签> 和 ""引号"" 以及其他特殊字符：!@#$%^&*()
</think>

输出结果");
        }

        private static void TestFormat(string formatName, string input)
        {
            Console.WriteLine($"测试 {formatName}:");
            
            var result = ProcessThinkingChain(input);
            
            Console.WriteLine($"  包含思维链: {result.HasThinkingChain}");
            Console.WriteLine($"  思维链格式: {result.Format}");
            Console.WriteLine($"  处理后内容: {result.FinalOutput.Trim()}");
            Console.WriteLine();
        }

        /// <summary>
        /// 处理思维链内容
        /// </summary>
        private static ThinkingChainResult ProcessThinkingChain(string rawContent)
        {
            var result = new ThinkingChainResult
            {
                RawContent = rawContent,
                HasThinkingChain = false,
                Format = "无",
                ThinkingChain = "",
                FinalOutput = rawContent
            };

            if (string.IsNullOrEmpty(rawContent))
                return result;

            // 优先检测 <o>...</o> 格式
            var outputMatch = OutputPattern.Match(rawContent);
            if (outputMatch.Success)
            {
                result.FinalOutput = outputMatch.Groups[1].Value.Trim();

                // 检测 <think>...</think> 格式的思维链
                var thinkMatch = ThinkChainPattern.Match(rawContent);
                if (thinkMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<think> + <o>";
                    result.ThinkingChain = thinkMatch.Groups[1].Value.Trim();
                }
                else
                {
                    // 检测 <thinking>...</thinking> 格式的思维链
                    var thinkingMatch = ThinkingChainPattern.Match(rawContent);
                    if (thinkingMatch.Success)
                    {
                        result.HasThinkingChain = true;
                        result.Format = "<thinking> + <o>";
                        result.ThinkingChain = thinkingMatch.Groups[1].Value.Trim();
                    }
                    else
                    {
                        // 检测 Qwen 格式的思维链 (...</think>)
                        var qwenMatch = QwenThinkPattern.Match(rawContent);
                        if (qwenMatch.Success)
                        {
                            result.HasThinkingChain = true;
                            result.Format = "Qwen (...think>) + <o>";
                            result.ThinkingChain = qwenMatch.Groups[1].Value.Trim();
                        }
                    }
                }
            }
            else
            {
                // 兼容旧格式：检测 <thinking>...</thinking> 或 <think>...</think> 或 ...</think>
                var thinkingMatch = ThinkingChainPattern.Match(rawContent);
                var thinkMatch = ThinkChainPattern.Match(rawContent);
                var qwenMatch = QwenThinkPattern.Match(rawContent);

                if (thinkingMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<thinking>";
                    result.ThinkingChain = thinkingMatch.Groups[1].Value.Trim();
                    result.FinalOutput = ThinkingChainPattern.Replace(rawContent, "").Trim();
                }
                else if (thinkMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<think>";
                    result.ThinkingChain = thinkMatch.Groups[1].Value.Trim();
                    result.FinalOutput = ThinkChainPattern.Replace(rawContent, "").Trim();
                }
                else if (qwenMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "Qwen (...think>)";
                    result.ThinkingChain = qwenMatch.Groups[1].Value.Trim();
                    result.FinalOutput = QwenThinkPattern.Replace(rawContent, "").Trim();
                }
            }

            return result;
        }

        /// <summary>
        /// 思维链处理结果
        /// </summary>
        public class ThinkingChainResult
        {
            public string RawContent { get; set; } = "";
            public bool HasThinkingChain { get; set; }
            public string Format { get; set; } = "";
            public string ThinkingChain { get; set; } = "";
            public string FinalOutput { get; set; } = "";
        }
    }
}

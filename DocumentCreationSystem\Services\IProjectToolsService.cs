using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 项目专属工具服务接口
/// </summary>
public interface IProjectToolsService
{
    /// <summary>
    /// 获取可用的工具列表
    /// </summary>
    /// <param name="projectType">项目类型</param>
    /// <returns>工具列表</returns>
    Task<List<ProjectTool>> GetAvailableToolsAsync(string projectType);

    /// <summary>
    /// 执行工具
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <param name="parameters">工具参数</param>
    /// <returns>执行结果</returns>
    Task<ToolExecutionResult> ExecuteToolAsync(string toolId, Dictionary<string, object> parameters);

    /// <summary>
    /// 批量文档处理
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="operation">操作类型</param>
    /// <param name="options">处理选项</param>
    /// <returns>处理结果</returns>
    Task<BatchProcessResult> BatchProcessDocumentsAsync(int projectId, string operation, BatchProcessOptions options);

    /// <summary>
    /// AI辅助创作工具
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="creationType">创作类型</param>
    /// <param name="context">创作上下文</param>
    /// <returns>创作结果</returns>
    Task<CreationResult> AIAssistedCreationAsync(int projectId, string creationType, CreationContext context);

    /// <summary>
    /// 内容分析工具
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="analysisType">分析类型</param>
    /// <returns>分析结果</returns>
    Task<AnalysisResult> AnalyzeContentAsync(int projectId, string analysisType);

    /// <summary>
    /// 文档格式转换
    /// </summary>
    /// <param name="sourceFile">源文件路径</param>
    /// <param name="targetFormat">目标格式</param>
    /// <param name="options">转换选项</param>
    /// <returns>转换结果</returns>
    Task<ConversionResult> ConvertDocumentAsync(string sourceFile, string targetFormat, ConversionOptions options);

    /// <summary>
    /// 内容质量检查
    /// </summary>
    /// <param name="content">内容</param>
    /// <param name="checkType">检查类型</param>
    /// <returns>检查结果</returns>
    Task<QualityCheckResult> CheckContentQualityAsync(string content, string checkType);

    /// <summary>
    /// 生成项目报告
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="reportType">报告类型</param>
    /// <returns>报告内容</returns>
    Task<string> GenerateProjectReportAsync(int projectId, string reportType);

    /// <summary>
    /// 导出项目数据
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="exportFormat">导出格式</param>
    /// <param name="options">导出选项</param>
    /// <returns>导出结果</returns>
    Task<ExportResult> ExportProjectDataAsync(int projectId, string exportFormat, ExportOptions options);

    /// <summary>
    /// 备份项目
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="backupOptions">备份选项</param>
    /// <returns>备份结果</returns>
    Task<BackupResult> BackupProjectAsync(int projectId, BackupOptions backupOptions);

    /// <summary>
    /// 恢复项目
    /// </summary>
    /// <param name="backupFile">备份文件路径</param>
    /// <param name="restoreOptions">恢复选项</param>
    /// <returns>恢复结果</returns>
    Task<RestoreResult> RestoreProjectAsync(string backupFile, RestoreOptions restoreOptions);

    /// <summary>
    /// 获取工具使用统计
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    Task<ToolUsageStatistics> GetToolUsageStatisticsAsync(int projectId);

    /// <summary>
    /// 注册自定义工具
    /// </summary>
    /// <param name="tool">工具定义</param>
    /// <returns>注册结果</returns>
    Task<bool> RegisterCustomToolAsync(ProjectTool tool);

    /// <summary>
    /// 移除自定义工具
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <returns>移除结果</returns>
    Task<bool> RemoveCustomToolAsync(string toolId);
}

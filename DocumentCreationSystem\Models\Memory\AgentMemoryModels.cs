using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models.Memory
{
    /// <summary>
    /// Agent记忆 - 统一的记忆模型
    /// </summary>
    public class AgentMemory
    {
        /// <summary>
        /// 记忆唯一标识
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 记忆类型
        /// </summary>
        public MemoryType Type { get; set; } = MemoryType.Knowledge;

        /// <summary>
        /// 记忆内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 记忆摘要
        /// </summary>
        public string? Summary { get; set; }

        /// <summary>
        /// 重要性评分 (0.0 - 1.0)
        /// </summary>
        public double Importance { get; set; } = 0.5;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后访问时间
        /// </summary>
        public DateTime? LastAccessedAt { get; set; }

        /// <summary>
        /// 访问次数
        /// </summary>
        public int AccessCount { get; set; } = 0;

        /// <summary>
        /// 标签列表
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 上下文ID
        /// </summary>
        public string? ContextId { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 记忆来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string? UserId { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string? SessionId { get; set; }

        /// <summary>
        /// 记忆权重（用于排序和过滤）
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 置信度 (0.0 - 1.0)
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 情感标签
        /// </summary>
        public EmotionalTag? EmotionalTag { get; set; }

        /// <summary>
        /// 记忆强度（影响遗忘速度）
        /// </summary>
        public double Strength { get; set; } = 1.0;

        /// <summary>
        /// 相关记忆ID列表
        /// </summary>
        public List<string> RelatedMemoryIds { get; set; } = new();

        /// <summary>
        /// 检查记忆是否已过期
        /// </summary>
        public bool IsExpired => ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;
    }

    /// <summary>
    /// 交互记忆
    /// </summary>
    public class InteractionMemory
    {
        public string Type { get; set; } = string.Empty;
        public string? UserId { get; set; }
        public string Content { get; set; } = string.Empty;
        public bool Success { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 技能记忆
    /// </summary>
    public class SkillMemory
    {
        public string SkillName { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public double Proficiency { get; set; }
        public List<string> Examples { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime LearnedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 上下文记忆
    /// </summary>
    public class ContextMemory
    {
        public string ContextType { get; set; } = string.Empty;
        public string? ProjectId { get; set; }
        public string? SessionId { get; set; }
        public Dictionary<string, object> Data { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 记忆网络
    /// </summary>
    public class MemoryNetwork
    {
        public string AgentId { get; set; } = string.Empty;
        public string? CenterMemoryId { get; set; }
        public Dictionary<string, AgentMemoryItem> Memories { get; set; } = new();
        public List<MemoryAssociation> Associations { get; set; } = new();
    }

    /// <summary>
    /// 记忆关联
    /// </summary>
    public class MemoryAssociation
    {
        public string Id { get; set; } = string.Empty;
        public string MemoryId1 { get; set; } = string.Empty;
        public string MemoryId2 { get; set; } = string.Empty;
        public double Strength { get; set; }
        public AssociationType Type { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 关联类型
    /// </summary>
    public enum AssociationType
    {
        Semantic,    // 语义关联
        Temporal,    // 时间关联
        Causal,      // 因果关联
        Similarity,  // 相似性关联
        Context      // 上下文关联
    }

    /// <summary>
    /// 记忆统计信息
    /// </summary>
    public class MemoryStatistics
    {
        public string AgentId { get; set; } = string.Empty;
        public int TotalMemories { get; set; }
        public int TotalAssociations { get; set; }
        public Dictionary<string, int> MemoryTypeDistribution { get; set; } = new();
        public double AverageMemoryStrength { get; set; }
        public DateTime? OldestMemory { get; set; }
        public DateTime? NewestMemory { get; set; }
        public string? MostAccessedMemory { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 记忆类型枚举
    /// </summary>
    public enum MemoryType
    {
        /// <summary>
        /// 知识记忆 - 事实性信息
        /// </summary>
        Knowledge = 0,

        /// <summary>
        /// 经验记忆 - 过往经历
        /// </summary>
        Experience = 1,

        /// <summary>
        /// 偏好记忆 - 用户偏好
        /// </summary>
        Preference = 2,

        /// <summary>
        /// 技能记忆 - 技能和能力
        /// </summary>
        Skill = 3,

        /// <summary>
        /// 模式记忆 - 行为模式
        /// </summary>
        Pattern = 4,

        /// <summary>
        /// 上下文记忆 - 环境信息
        /// </summary>
        Context = 5,

        /// <summary>
        /// 目标记忆 - 目标和意图
        /// </summary>
        Goal = 6,

        /// <summary>
        /// 情感记忆 - 情感状态
        /// </summary>
        Emotional = 7,

        /// <summary>
        /// 程序记忆 - 程序性知识
        /// </summary>
        Procedural = 8,

        /// <summary>
        /// 语义记忆 - 概念和意义
        /// </summary>
        Semantic = 9,

        /// <summary>
        /// 情节记忆 - 特定事件
        /// </summary>
        Episodic = 10,

        /// <summary>
        /// 工作记忆 - 临时信息
        /// </summary>
        Working = 11,

        /// <summary>
        /// 元记忆 - 关于记忆的记忆
        /// </summary>
        Meta = 12
    }

    /// <summary>
    /// 情感标签
    /// </summary>
    public class EmotionalTag
    {
        /// <summary>
        /// 情感类型
        /// </summary>
        public EmotionType Type { get; set; } = EmotionType.Neutral;

        /// <summary>
        /// 情感强度 (0.0 - 1.0)
        /// </summary>
        public double Intensity { get; set; } = 0.5;

        /// <summary>
        /// 情感价值（正面/负面）(-1.0 到 1.0)
        /// </summary>
        public double Valence { get; set; } = 0.0;

        /// <summary>
        /// 情感描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 情感类型枚举
    /// </summary>
    public enum EmotionType
    {
        /// <summary>
        /// 中性
        /// </summary>
        Neutral,

        /// <summary>
        /// 快乐
        /// </summary>
        Joy,

        /// <summary>
        /// 悲伤
        /// </summary>
        Sadness,

        /// <summary>
        /// 愤怒
        /// </summary>
        Anger,

        /// <summary>
        /// 恐惧
        /// </summary>
        Fear,

        /// <summary>
        /// 惊讶
        /// </summary>
        Surprise,

        /// <summary>
        /// 厌恶
        /// </summary>
        Disgust,

        /// <summary>
        /// 期待
        /// </summary>
        Anticipation,

        /// <summary>
        /// 信任
        /// </summary>
        Trust,

        /// <summary>
        /// 满意
        /// </summary>
        Satisfaction,

        /// <summary>
        /// 困惑
        /// </summary>
        Confusion,

        /// <summary>
        /// 兴奋
        /// </summary>
        Excitement
    }

    /// <summary>
    /// 记忆模式 - 兼容现有模型
    /// </summary>
    public class MemoryPattern
    {
        /// <summary>
        /// 模式ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 模式类型
        /// </summary>
        public string PatternType { get; set; } = string.Empty;

        /// <summary>
        /// 模式描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 模式强度
        /// </summary>
        public double Strength { get; set; } = 0.5;

        /// <summary>
        /// 出现次数
        /// </summary>
        public int Occurrences { get; set; } = 1;

        /// <summary>
        /// 首次观察时间
        /// </summary>
        public DateTime FirstObserved { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后观察时间
        /// </summary>
        public DateTime LastObserved { get; set; } = DateTime.Now;

        /// <summary>
        /// 示例列表
        /// </summary>
        public List<string> Examples { get; set; } = new();

        /// <summary>
        /// 模式置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 相关上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 交互数据
    /// </summary>
    public class InteractionData
    {
        public string Type { get; set; } = string.Empty;
        public string? UserId { get; set; }
        public string Content { get; set; } = string.Empty;
        public bool Success { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 成功案例
    /// </summary>
    public class SuccessCase
    {
        public string TaskType { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public List<string> InvolvedSkills { get; set; } = new();
        public Dictionary<string, object> Conditions { get; set; } = new();
        public List<string> Actions { get; set; } = new();
        public Dictionary<string, object> Outcomes { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 失败案例
    /// </summary>
    public class FailureCase
    {
        public string TaskType { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public List<string> InvolvedSkills { get; set; } = new();
        public Dictionary<string, object> Conditions { get; set; } = new();
        public List<string> Actions { get; set; } = new();
        public Dictionary<string, object> Outcomes { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 学习结果
    /// </summary>
    public class LearningResult
    {
        public string AgentId { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string NewKnowledge { get; set; } = string.Empty;
        public double ConfidenceScore { get; set; }
        public List<Pattern> DiscoveredPatterns { get; set; } = new();
        public List<SkillUpdate> SkillUpdates { get; set; } = new();
        public List<string> ImprovementSuggestions { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 模式
    /// </summary>
    public class Pattern
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public PatternType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Conditions { get; set; } = new();
        public List<string> Actions { get; set; } = new();
        public Dictionary<string, object> Outcomes { get; set; } = new();
        public double Confidence { get; set; }
        public DateTime DiscoveredAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 模式类型
    /// </summary>
    public enum PatternType
    {
        Success,     // 成功模式
        Failure,     // 失败模式
        Behavioral,  // 行为模式
        Temporal,    // 时间模式
        Contextual   // 上下文模式
    }

    /// <summary>
    /// 技能更新
    /// </summary>
    public class SkillUpdate
    {
        public string SkillName { get; set; } = string.Empty;
        public double ProficiencyDelta { get; set; }
        public string Reason { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 学习建议
    /// </summary>
    public class LearningRecommendation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public RecommendationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Priority { get; set; }
        public TimeSpan EstimatedEffort { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 建议类型
    /// </summary>
    public enum RecommendationType
    {
        SkillImprovement,      // 技能提升
        ExperienceAccumulation, // 经验积累
        PatternRecognition,    // 模式识别
        KnowledgeIntegration   // 知识整合
    }

    /// <summary>
    /// 学习进度
    /// </summary>
    public class LearningProgress
    {
        public string AgentId { get; set; } = string.Empty;
        public int TotalSkills { get; set; }
        public double AverageSkillProficiency { get; set; }
        public int TotalExperiences { get; set; }
        public double LearningVelocity { get; set; }
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public DateTime? LastLearningActivity { get; set; }
        public DateTime EvaluatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Agent学习状态
    /// </summary>
    public class AgentLearningState
    {
        public string AgentId { get; set; } = string.Empty;
        public Dictionary<string, double> SkillProficiencies { get; set; } = new();
        public int TotalInteractions { get; set; }
        public int SuccessfulInteractions { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? LastLearningTime { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 知识图谱
    /// </summary>
    public class KnowledgeGraph
    {
        public string AgentId { get; set; } = string.Empty;
        public List<KnowledgeNode> Nodes { get; set; } = new();
        public List<KnowledgeEdge> Edges { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 知识节点
    /// </summary>
    public class KnowledgeNode
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Label { get; set; } = string.Empty;
        public double Weight { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 知识边
    /// </summary>
    public class KnowledgeEdge
    {
        public string SourceId { get; set; } = string.Empty;
        public string TargetId { get; set; } = string.Empty;
        public double Weight { get; set; }
        public string Type { get; set; } = string.Empty;
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// Agent记忆项（简化版本）
    /// </summary>
    public class AgentMemoryItem
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string AgentId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public string Type { get; set; } = "general";
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime LastAccessedAt { get; set; } = DateTime.Now;
        public int AccessCount { get; set; } = 0;
        public double Strength { get; set; } = 1.0;
        public List<string> Tags { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
        public double Importance { get; set; } = 0.5;
    }
}

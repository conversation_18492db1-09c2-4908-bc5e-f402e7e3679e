using System.Text.Json.Serialization;

namespace DocumentCreationSystem.Models;

/// <summary>
/// 思维链响应模型
/// </summary>
public class ThinkingChainResponse
{
    /// <summary>
    /// 原始响应内容
    /// </summary>
    public string RawContent { get; set; } = string.Empty;

    /// <summary>
    /// 思维链部分
    /// </summary>
    public string ThinkingChain { get; set; } = string.Empty;

    /// <summary>
    /// 最终输出内容
    /// </summary>
    public string FinalOutput { get; set; } = string.Empty;

    /// <summary>
    /// 是否包含思维链
    /// </summary>
    public bool HasThinkingChain { get; set; }

    /// <summary>
    /// 思维链步骤列表
    /// </summary>
    public List<ThinkingStep> ThinkingSteps { get; set; } = new();
}

/// <summary>
/// 思维链步骤
/// </summary>
public class ThinkingStep
{
    /// <summary>
    /// 步骤序号
    /// </summary>
    public int StepNumber { get; set; }

    /// <summary>
    /// 步骤标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 步骤内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 步骤类型：分析、推理、总结等
    /// </summary>
    public string StepType { get; set; } = string.Empty;

    /// <summary>
    /// 是否为关键步骤
    /// </summary>
    public bool IsKeyStep { get; set; }
}

/// <summary>
/// 思维链显示配置
/// </summary>
public class ThinkingChainDisplayConfig
{
    /// <summary>
    /// 是否显示思维链
    /// </summary>
    public bool ShowThinkingChain { get; set; } = false;

    /// <summary>
    /// 是否折叠显示
    /// </summary>
    public bool IsCollapsed { get; set; } = true;

    /// <summary>
    /// 是否显示步骤编号
    /// </summary>
    public bool ShowStepNumbers { get; set; } = true;

    /// <summary>
    /// 是否高亮关键步骤
    /// </summary>
    public bool HighlightKeySteps { get; set; } = true;

    /// <summary>
    /// 最大显示步骤数（0表示显示全部）
    /// </summary>
    public int MaxDisplaySteps { get; set; } = 0;

    /// <summary>
    /// 思维链显示样式
    /// </summary>
    public ThinkingChainStyle DisplayStyle { get; set; } = ThinkingChainStyle.Expandable;
}

/// <summary>
/// 思维链显示样式
/// </summary>
public enum ThinkingChainStyle
{
    /// <summary>
    /// 可展开折叠
    /// </summary>
    Expandable,

    /// <summary>
    /// 内联显示
    /// </summary>
    Inline,

    /// <summary>
    /// 侧边栏显示
    /// </summary>
    Sidebar,

    /// <summary>
    /// 弹窗显示
    /// </summary>
    Modal,

    /// <summary>
    /// 隐藏
    /// </summary>
    Hidden
}

/// <summary>
/// 思维链过滤器
/// </summary>
public class ThinkingChainFilter
{
    /// <summary>
    /// 是否启用过滤
    /// </summary>
    public bool EnableFilter { get; set; } = true;

    /// <summary>
    /// 过滤模式
    /// </summary>
    public FilterMode Mode { get; set; } = FilterMode.RemoveThinkingChain;

    /// <summary>
    /// 保留关键步骤
    /// </summary>
    public bool PreserveKeySteps { get; set; } = false;

    /// <summary>
    /// 添加思维链摘要
    /// </summary>
    public bool AddSummary { get; set; } = false;

    /// <summary>
    /// 自定义过滤规则
    /// </summary>
    public List<string> CustomFilterRules { get; set; } = new();
}

/// <summary>
/// 过滤模式
/// </summary>
public enum FilterMode
{
    /// <summary>
    /// 完全移除思维链
    /// </summary>
    RemoveThinkingChain,

    /// <summary>
    /// 保留思维链但标记为隐藏
    /// </summary>
    HideThinkingChain,

    /// <summary>
    /// 压缩思维链为摘要
    /// </summary>
    CompressToSummary,

    /// <summary>
    /// 仅保留关键步骤
    /// </summary>
    KeyStepsOnly
}

/// <summary>
/// 思维链处理结果
/// </summary>
public class ThinkingChainProcessResult
{
    /// <summary>
    /// 处理后的内容
    /// </summary>
    public string ProcessedContent { get; set; } = string.Empty;

    /// <summary>
    /// 原始思维链响应
    /// </summary>
    public ThinkingChainResponse? OriginalResponse { get; set; }

    /// <summary>
    /// 处理状态
    /// </summary>
    public ProcessStatus Status { get; set; } = ProcessStatus.Success;

    /// <summary>
    /// 处理消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime ProcessedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否包含思维链
    /// </summary>
    public bool ContainsThinkingChain { get; set; }

    /// <summary>
    /// 思维链步骤数量
    /// </summary>
    public int ThinkingStepsCount { get; set; }
}

/// <summary>
/// 处理状态
/// </summary>
public enum ProcessStatus
{
    Success,
    Warning,
    Error,
    NoThinkingChain
}

/// <summary>
/// 思维链统计信息
/// </summary>
public class ThinkingChainStatistics
{
    /// <summary>
    /// 总响应数
    /// </summary>
    public int TotalResponses { get; set; }

    /// <summary>
    /// 包含思维链的响应数
    /// </summary>
    public int ResponsesWithThinkingChain { get; set; }

    /// <summary>
    /// 平均思维链步骤数
    /// </summary>
    public float AverageThinkingSteps { get; set; }

    /// <summary>
    /// 最长思维链步骤数
    /// </summary>
    public int MaxThinkingSteps { get; set; }

    /// <summary>
    /// 思维链内容占比
    /// </summary>
    public float ThinkingChainRatio { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

<Window x:Class="DocumentCreationSystem.Views.SavedConfigsSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="选择书籍配置" 
        Height="500" 
        Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="选择要加载的书籍配置" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,16"/>

        <!-- 配置列表 -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <DataGrid x:Name="ConfigsDataGrid"
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      CanUserReorderColumns="False"
                      CanUserResizeRows="False"
                      SelectionMode="Single"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      MouseDoubleClick="ConfigsDataGrid_MouseDoubleClick">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="书籍标题" 
                                        Binding="{Binding BookTitle}" 
                                        Width="200"
                                        IsReadOnly="True"/>
                    <DataGridTextColumn Header="卷数" 
                                        Binding="{Binding VolumeCount}" 
                                        Width="60"
                                        IsReadOnly="True"/>
                    <DataGridTextColumn Header="章节数" 
                                        Binding="{Binding ChapterCount}" 
                                        Width="80"
                                        IsReadOnly="True"/>
                    <DataGridTextColumn Header="每章字数" 
                                        Binding="{Binding WordsPerChapter}" 
                                        Width="80"
                                        IsReadOnly="True"/>
                    <DataGridTextColumn Header="保存时间" 
                                        Binding="{Binding SavedAt, StringFormat=yyyy-MM-dd HH:mm}" 
                                        Width="120"
                                        IsReadOnly="True"/>
                    <DataGridTextColumn Header="创作方向" 
                                        Binding="{Binding CreativeDirection}" 
                                        Width="*"
                                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextTrimming" Value="CharacterEllipsis"/>
                                <Setter Property="ToolTip" Value="{Binding CreativeDirection}"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,16,0,0">
            <Button x:Name="DeleteButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="Delete_Click"
                    Margin="0,0,8,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Delete" Margin="0,0,8,0"/>
                    <TextBlock Text="删除"/>
                </StackPanel>
            </Button>
            <Button x:Name="CancelButton"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Click="Cancel_Click"
                    Margin="0,0,8,0">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Cancel" Margin="0,0,8,0"/>
                    <TextBlock Text="取消"/>
                </StackPanel>
            </Button>
            <Button x:Name="OkButton"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="Ok_Click"
                    IsDefault="True">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Check" Margin="0,0,8,0"/>
                    <TextBlock Text="确定"/>
                </StackPanel>
            </Button>
        </StackPanel>
    </Grid>
</Window>

# 章节细纲差异化改进方案

## 问题分析

### 原始问题
用户反馈：**同一卷中每一章的细纲内容都差不多一样**

### 根本原因
1. **信息过载**：提示词包含过多世界设定内容，导致AI模型"迷失焦点"
2. **缺乏差异化指导**：没有明确要求每章的独特性和差异化
3. **参考内容过多**：前几章细纲全部作为参考，容易产生模式化
4. **温度参数偏低**：0.8的temperature不足以产生足够的创意变化

## 改进策略

### 1. 精简世界设定参考
**改进前：**
- 读取全部23个世界设定文件
- 不区分重要性，全部加入提示词
- 每个文件内容长度不限制

**改进后：**
- 根据章节位置智能选择相关设定文件
- 始终包含核心设定：角色设定、关系网络
- 根据章节阶段选择额外设定：
  - 前期章节：世界观、地图、种族
  - 中期章节：势力、政治、时间线
  - 后期章节：修炼、功法、灵宝

### 2. 强化差异化要求
**改进前：**
- 通用的章节生成要求
- 没有强调章节独特性
- 缺乏明确的差异化指导

**改进后：**
- 明确标注章节在卷中的位置和进度
- 强调"与众不同"的创作要求
- 要求每章有独特的场景、冲突或人物发展
- 设置专门的"本章核心亮点"部分

### 3. 优化参考内容
**改进前：**
- 参考前3章的完整细纲
- 容易产生模式化思维

**改进后：**
- 只参考最近1-2章的细纲
- 重点关注剧情连贯性而非内容模仿
- 明确要求避免内容雷同

### 4. 调整AI参数
**改进前：**
- temperature = 0.8（偏保守）

**改进后：**
- temperature = 0.9（增加创意性）
- 鼓励更多样化的输出

## 技术实现

### 1. 智能世界设定选择
```csharp
private async Task<Dictionary<string, string>> ReadRelevantWorldSettingsForChapterAsync(
    string projectPath, int chapterNumber, VolumeOutline volume)
{
    // 始终包含核心设定
    var alwaysIncluded = new[] { "角色设定管理", "关系网络" };
    
    // 根据章节位置选择额外设定
    bool isEarlyChapter = chapterNumber <= volume.StartChapter + (volume.EndChapter - volume.StartChapter) / 3;
    bool isLateChapter = chapterNumber >= volume.EndChapter - (volume.EndChapter - volume.StartChapter) / 3;
    
    if (isEarlyChapter)
    {
        // 前期：世界观建立
        var earlyFocus = new[] { "世界观设定管理", "地图结构管理", "种族类别管理" };
    }
    else if (isLateChapter)
    {
        // 后期：修炼突破
        var lateFocus = new[] { "修炼体系设定管理", "功法体系管理", "灵宝体系管理" };
    }
    else
    {
        // 中期：势力冲突
        var midFocus = new[] { "势力管理", "政治体系管理", "时间线管理" };
    }
}
```

### 2. 聚焦的提示词构建
```csharp
private string BuildFocusedChapterOutlinePrompt(...)
{
    // 确定章节在卷中的位置
    int chapterPositionInVolume = chapterNumber - volume.StartChapter + 1;
    double progressPercentage = (double)chapterPositionInVolume / chaptersInVolume;
    
    // 根据位置确定剧情发展阶段
    string stageDescription;
    if (progressPercentage <= 0.25)
        stageDescription = "本章处于卷宗前期，应着重铺设背景、引入人物和初步展开冲突";
    else if (progressPercentage <= 0.75)
        stageDescription = "本章处于卷宗中期，应着重发展冲突、深化人物关系和推进主线剧情";
    else
        stageDescription = "本章处于卷宗后期，应着重推向高潮、解决关键冲突并为下一卷做铺垫";
}
```

### 3. 差异化细纲格式
```markdown
## 第X章：[创新性标题，反映本章独特内容]

### 本章核心亮点（必须与前几章有明显区别）
- 独特场景：[本章特有的场景或环境，不可与前几章重复]
- 核心冲突：[本章的主要冲突或挑战，需有新意]
- 人物发展：[角色在本章的成长或变化]
- 读者期待：[读者会特别期待的精彩内容]

### 剧情发展（必须推进主线）
- 起始情境：[本章开始时的情况，承接上章]
- 情节推进：[本章的主要事件发展]
- 高潮设计：[本章的高潮或转折点]
- 结尾铺垫：[为下一章做的铺垫]
```

## 预期改进效果

### 1. 章节差异化显著提升
- 每章都有明确的独特亮点
- 避免重复的场景和冲突设计
- 增强读者的阅读新鲜感

### 2. 剧情推进更加明确
- 明确标注章节在卷中的位置和作用
- 确保每章都有实质性的剧情推进
- 避免原地踏步的情况

### 3. 世界设定运用更加精准
- 根据章节需要选择相关设定
- 避免信息过载导致的焦点分散
- 提高设定文件的有效利用率

### 4. AI生成质量提升
- 更高的创意性和多样性
- 更聚焦的内容生成
- 更符合用户期待的差异化效果

## 使用建议

### 1. 世界设定文件优化
- 确保核心设定文件（角色设定、关系网络）内容详细
- 根据小说类型重点完善相关设定文件
- 定期更新角色关系和时间线信息

### 2. 卷宗大纲优化
- 在卷宗大纲中明确各阶段的重点
- 为前期、中期、后期设定不同的主题和冲突
- 确保卷宗内部有清晰的发展脉络

### 3. 生成结果检查
- 重点检查每章的"核心亮点"是否真正独特
- 确认章节间的剧情推进是否明显
- 验证世界设定的运用是否合理

### 4. 迭代优化
- 根据生成效果调整世界设定文件的重点
- 优化卷宗大纲的阶段划分
- 持续改进提示词的表达方式

## 后续优化方向

1. **动态冲突生成**：根据前几章的冲突类型，自动避免重复
2. **角色发展追踪**：跟踪角色在各章节的发展轨迹，确保合理成长
3. **场景多样性检查**：自动检测场景重复，提醒使用新场景
4. **读者期待分析**：根据小说类型和进度，预测读者期待点

通过这些改进，分步写书功能将能够生成更加多样化、有趣且符合逻辑的章节细纲，显著提升创作质量和用户体验。

# 清除Conda环境变量
$env:CONDA_DEFAULT_ENV = $null
$env:CONDA_PREFIX = $null
$env:CONDA_PROMPT_MODIFIER = $null
$env:CONDA_PYTHON_EXE = $null
$env:CONDA_EXE = $null
$env:CONDA_ROOT = $null
$env:CONDA_SHLVL = $null

# 设置工作目录
Set-Location "d:\AI_project\文档管理及创作系统\DocumentCreationSystem"

Write-Host "正在编译项目..." -ForegroundColor Green

# 清理项目
Write-Host "清理项目..." -ForegroundColor Yellow
dotnet clean

# 恢复NuGet包
Write-Host "恢复NuGet包..." -ForegroundColor Yellow
dotnet restore

# 编译项目
Write-Host "编译项目..." -ForegroundColor Yellow
dotnet build --configuration Release

Write-Host "编译完成！" -ForegroundColor Green

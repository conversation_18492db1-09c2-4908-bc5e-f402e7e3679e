using System;
using System.Threading;
using System.Threading.Tasks;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 专利交底书写作服务接口
    /// </summary>
    public interface IPatentDisclosureService
    {
        /// <summary>
        /// 分析技术素材文件
        /// </summary>
        /// <param name="materialFiles">技术素材文件路径列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>技术分析结果</returns>
        Task<TechnicalAnalysisResult> AnalyzeTechnicalMaterialsAsync(
            System.Collections.Generic.List<string> materialFiles, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成专利大纲
        /// </summary>
        /// <param name="request">专利生成请求</param>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>专利大纲</returns>
        Task<PatentOutline> GeneratePatentOutlineAsync(
            PatentDisclosureRequest request, 
            TechnicalAnalysisResult technicalAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成发明名称
        /// </summary>
        /// <param name="request">专利生成请求</param>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>发明名称</returns>
        Task<string> GenerateInventionTitleAsync(
            PatentDisclosureRequest request, 
            TechnicalAnalysisResult technicalAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成数学公式
        /// </summary>
        /// <param name="formulaType">公式类型</param>
        /// <param name="context">上下文信息</param>
        /// <param name="complexityLevel">复杂度等级</param>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>数学公式</returns>
        Task<MathematicalFormula> GenerateMathematicalFormulaAsync(
            string formulaType,
            string context,
            int complexityLevel,
            TechnicalAnalysisResult technicalAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成专利章节内容
        /// </summary>
        /// <param name="sectionOutline">章节大纲</param>
        /// <param name="request">专利生成请求</param>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="previousSections">前面已生成的章节</param>
        /// <param name="availableFormulas">可用的数学公式</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>章节内容</returns>
        Task<PatentSection> GeneratePatentSectionAsync(
            PatentSectionOutline sectionOutline,
            PatentDisclosureRequest request,
            TechnicalAnalysisResult technicalAnalysis,
            System.Collections.Generic.List<PatentSection> previousSections,
            System.Collections.Generic.List<MathematicalFormula> availableFormulas,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成完整专利交底书
        /// </summary>
        /// <param name="request">专利生成请求</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>专利交底书生成结果</returns>
        Task<PatentDisclosureResult> GeneratePatentDisclosureAsync(
            PatentDisclosureRequest request,
            IProgress<string>? progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 格式化专利交底书内容
        /// </summary>
        /// <param name="sections">专利章节列表</param>
        /// <param name="formulas">数学公式列表</param>
        /// <param name="request">专利生成请求</param>
        /// <param name="inventionTitle">发明名称</param>
        /// <returns>格式化后的专利交底书内容</returns>
        string FormatPatentDisclosureContent(
            System.Collections.Generic.List<PatentSection> sections,
            System.Collections.Generic.List<MathematicalFormula> formulas,
            PatentDisclosureRequest request, 
            string inventionTitle);

        /// <summary>
        /// 验证专利交底书质量
        /// </summary>
        /// <param name="content">专利交底书内容</param>
        /// <param name="request">专利生成请求</param>
        /// <returns>质量评分 (0-100)</returns>
        Task<int> ValidatePatentQualityAsync(string content, PatentDisclosureRequest request);

        /// <summary>
        /// 生成权利要求书
        /// </summary>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="request">专利生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>权利要求书内容</returns>
        Task<string> GenerateClaimsAsync(
            TechnicalAnalysisResult technicalAnalysis,
            PatentDisclosureRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成附图说明
        /// </summary>
        /// <param name="technicalAnalysis">技术分析结果</param>
        /// <param name="request">专利生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>附图说明内容</returns>
        Task<string> GenerateDrawingDescriptionsAsync(
            TechnicalAnalysisResult technicalAnalysis,
            PatentDisclosureRequest request,
            CancellationToken cancellationToken = default);
    }
}

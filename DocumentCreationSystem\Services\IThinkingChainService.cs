using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 思维链处理服务接口
/// </summary>
public interface IThinkingChainService
{
    /// <summary>
    /// 解析思维链响应
    /// </summary>
    /// <param name="rawContent">原始响应内容</param>
    /// <returns>思维链响应对象</returns>
    ThinkingChainResponse ParseThinkingChainResponse(string rawContent);

    /// <summary>
    /// 简单的思维链过滤方法，直接处理字符串内容
    /// </summary>
    /// <param name="content">原始内容</param>
    /// <returns>过滤后的内容</returns>
    string FilterThinkingChain(string content);

    /// <summary>
    /// 过滤思维链内容
    /// </summary>
    /// <param name="response">思维链响应</param>
    /// <param name="filter">过滤器配置</param>
    /// <returns>过滤后的内容</returns>
    string FilterThinkingChain(ThinkingChainResponse response, ThinkingChainFilter filter);

    /// <summary>
    /// 处理思维链响应
    /// </summary>
    /// <param name="rawContent">原始响应内容</param>
    /// <param name="filter">过滤器配置</param>
    /// <returns>处理结果</returns>
    ThinkingChainProcessResult ProcessThinkingChainResponse(string rawContent, ThinkingChainFilter? filter = null);

    /// <summary>
    /// 生成思维链摘要
    /// </summary>
    /// <param name="response">思维链响应</param>
    /// <returns>摘要内容</returns>
    string GenerateThinkingChainSummary(ThinkingChainResponse response);

    /// <summary>
    /// 格式化思维链显示
    /// </summary>
    /// <param name="response">思维链响应</param>
    /// <param name="config">显示配置</param>
    /// <returns>格式化后的显示内容</returns>
    string FormatThinkingChainDisplay(ThinkingChainResponse response, ThinkingChainDisplayConfig config);

    /// <summary>
    /// 检测是否包含思维链
    /// </summary>
    /// <param name="content">内容</param>
    /// <returns>是否包含思维链</returns>
    bool ContainsThinkingChain(string content);

    /// <summary>
    /// 获取思维链统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    ThinkingChainStatistics GetStatistics();

    /// <summary>
    /// 重置统计信息
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// 更新显示配置
    /// </summary>
    /// <param name="config">新的显示配置</param>
    void UpdateDisplayConfig(ThinkingChainDisplayConfig config);

    /// <summary>
    /// 获取当前显示配置
    /// </summary>
    /// <returns>当前显示配置</returns>
    ThinkingChainDisplayConfig GetDisplayConfig();

    /// <summary>
    /// 导出思维链数据
    /// </summary>
    /// <param name="responses">思维链响应列表</param>
    /// <param name="format">导出格式</param>
    /// <returns>导出的数据</returns>
    string ExportThinkingChainData(List<ThinkingChainResponse> responses, string format = "json");

    /// <summary>
    /// 分析思维链模式
    /// </summary>
    /// <param name="responses">思维链响应列表</param>
    /// <returns>分析结果</returns>
    Dictionary<string, object> AnalyzeThinkingChainPatterns(List<ThinkingChainResponse> responses);

    /// <summary>
    /// 过滤章节内容中的非小说正文部分
    /// </summary>
    /// <param name="content">章节内容</param>
    /// <returns>过滤后的纯小说正文</returns>
    string FilterChapterContent(string content);
}

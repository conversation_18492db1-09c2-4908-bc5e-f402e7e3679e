using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 数据存储服务接口
    /// </summary>
    public interface IDataStorageService
    {
        // 项目管理
        Task<List<Project>> GetProjectsAsync();
        Task<Project?> GetProjectAsync(int id);
        Task<Project> CreateProjectAsync(Project project);
        Task<Project> UpdateProjectAsync(Project project);
        Task DeleteProjectAsync(int id);

        // 文档管理
        Task<List<Document>> GetDocumentsAsync(int projectId);
        Task<Document?> GetDocumentAsync(int id);
        Task<Document> CreateDocumentAsync(Document document);
        Task<Document> UpdateDocumentAsync(Document document);
        Task DeleteDocumentAsync(int id);

        // 小说项目管理
        Task<NovelProject?> GetNovelProjectAsync(int novelProjectId);
        Task<NovelProject?> GetNovelProjectByProjectIdAsync(int projectId);
        Task<NovelProject> CreateNovelProjectAsync(NovelProject novelProject);
        Task<NovelProject> UpdateNovelProjectAsync(NovelProject novelProject);

        // 章节管理
        Task<List<Chapter>> GetChaptersAsync(int novelProjectId);
        Task<Chapter?> GetChapterAsync(int id);
        Task<Chapter> CreateChapterAsync(Chapter chapter);
        Task<Chapter> UpdateChapterAsync(Chapter chapter);
        Task DeleteChapterAsync(int id);

        // 角色管理
        Task<List<Character>> GetCharactersAsync(int novelProjectId);
        Task<Character?> GetCharacterAsync(int id);
        Task<Character> CreateCharacterAsync(Character character);
        Task<Character> UpdateCharacterAsync(Character character);
        Task DeleteCharacterAsync(int id);

        // 向量记录管理
        Task<List<VectorRecord>> GetVectorRecordsAsync(int documentId);
        Task<VectorRecord?> GetVectorRecordAsync(int id);
        Task<VectorRecord> CreateVectorRecordAsync(VectorRecord vectorRecord);
        Task<VectorRecord> UpdateVectorRecordAsync(VectorRecord vectorRecord);
        Task DeleteVectorRecordAsync(int id);

        // AI模型配置
        Task<List<AIModelConfig>> GetAIModelConfigsAsync();
        Task<AIModelConfig?> GetAIModelConfigAsync(int id);
        Task<AIModelConfig> CreateAIModelConfigAsync(AIModelConfig config);
        Task<AIModelConfig> UpdateAIModelConfigAsync(AIModelConfig config);
        Task DeleteAIModelConfigAsync(int id);

        // 世界设定管理
        Task<WorldSetting?> GetWorldSettingAsync(int novelProjectId);
        Task<WorldSetting> CreateWorldSettingAsync(WorldSetting worldSetting);
        Task<WorldSetting> UpdateWorldSettingAsync(WorldSetting worldSetting);
        Task DeleteWorldSettingAsync(int id);

        // 提示词配置管理
        Task<List<PromptConfig>> GetPromptConfigsAsync();
        Task<PromptConfig?> GetPromptConfigAsync(int id);
        Task<PromptConfig> CreatePromptConfigAsync(PromptConfig config);
        Task<PromptConfig> UpdatePromptConfigAsync(PromptConfig config);
        Task DeletePromptConfigAsync(int id);

        // 写书流程配置管理
        Task<List<WritingWorkflowConfig>> GetWritingWorkflowConfigsAsync();
        Task<WritingWorkflowConfig?> GetWritingWorkflowConfigAsync(int id);
        Task<WritingWorkflowConfig> CreateWritingWorkflowConfigAsync(WritingWorkflowConfig config);
        Task<WritingWorkflowConfig> UpdateWritingWorkflowConfigAsync(WritingWorkflowConfig config);
        Task DeleteWritingWorkflowConfigAsync(int id);

        // 数据持久化
        Task SaveAsync();
        Task LoadAsync();

        // 通用键值存储
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SaveAsync<T>(string key, T value) where T : class;
        Task SaveAsync<T>(string key, T value, TimeSpan expiration) where T : class;
        Task<bool> DeleteAsync(string key);
        Task<bool> ExistsAsync(string key);

        // 数据清理和验证
        Task<DataCleanupResult> CleanupInvalidProjectReferencesAsync();
        Task<DataIntegrityReport> ValidateDataIntegrityAsync();
    }
}

using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem.Examples
{
    /// <summary>
    /// 增强功能使用示例
    /// </summary>
    public class EnhancedFeaturesExample
    {
        /// <summary>
        /// 演示如何配置和使用增强写书功能
        /// </summary>
        public static void ConfigureServices(IServiceCollection services)
        {
            // 配置日志
            services.AddLogging(builder => builder.AddConsole());

            // 注册核心服务（假设已存在）
            // services.AddScoped<IAIService, YourAIService>();
            // services.AddScoped<IDataStorageService, YourDataStorageService>();

            // 注册增强服务
            services.AddScoped<EnhancedChapterContentService>();
            services.AddScoped<CharacterUpdateService>();
            services.AddScoped<ChapterOutlineUpdateService>();
            services.AddScoped<TimelineService>();
            services.AddScoped<ContentQualityService>();

            // 注册现有服务（如果需要）
            services.AddScoped<StepByStepWritingService>();
            services.AddScoped<AutomatedChapterCreationService>();
        }

        /// <summary>
        /// 演示增强章节内容生成的使用
        /// </summary>
        public static async Task<string> DemonstrateEnhancedChapterGeneration(
            EnhancedChapterContentService enhancedService)
        {
            var request = new EnhancedChapterRequest
            {
                ProjectId = 1,
                ProjectPath = @"C:\MyNovel\Project1",
                ChapterNumber = 5,
                ChapterOutline = @"
## 第5章：突破

### 场景设定
- 时间：深夜
- 地点：修炼室
- 环境：灵气浓郁

### 人物动态
- 主角：李明准备突破筑基期
- 重要角色：张师傅在外护法

### 情节发展
李明运转功法，感受到瓶颈松动的迹象。
突然，一股强大的灵气波动从体内爆发。
张师傅察觉到异常，立即加强护法阵法。
",
                TargetWordCount = 3000,
                BookTitle = "修仙传奇",
                CreativeDirection = "重点描写突破过程的细节和心理变化"
            };

            try
            {
                var result = await enhancedService.GenerateEnhancedChapterContentAsync(request);
                
                if (result.IsSuccess)
                {
                    Console.WriteLine($"章节生成成功！");
                    Console.WriteLine($"字数：{result.WordCount}");
                    Console.WriteLine($"质量分数：{result.QualityScore:F2}");
                    Console.WriteLine($"生成时间：{result.GenerationTime.TotalSeconds:F1}秒");
                    
                    return result.Content;
                }
                else
                {
                    Console.WriteLine($"章节生成失败：{result.ErrorMessage}");
                    return "";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调用失败：{ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// 演示内容清理功能的使用
        /// </summary>
        public static void DemonstrateContentCleaning(ContentQualityService qualityService)
        {
            var rawContent = @"
这是章节的开始。

<output>
李明深吸一口气，开始运转《太极玄功》。
随着功法的运转，他感受到体内灵气的流动。
</output>

突然，一股强大的力量从丹田涌出。

<response>
这股力量让李明感到前所未有的充实感。
他知道，突破的时机到了。
</response>

张师傅在外面感受到了这股波动。
";

            Console.WriteLine("=== 内容清理演示 ===");
            Console.WriteLine("原始内容：");
            Console.WriteLine(rawContent);
            Console.WriteLine();

            var cleanedContent = qualityService.CleanOutputTags(rawContent);
            
            Console.WriteLine("清理后内容：");
            Console.WriteLine(cleanedContent);
            Console.WriteLine();
            
            Console.WriteLine($"清理效果：{(rawContent.Length - cleanedContent.Length)}个字符被移除");
        }

        /// <summary>
        /// 演示角色更新功能的使用
        /// </summary>
        public static async Task DemonstrateCharacterUpdate(
            CharacterUpdateService characterService)
        {
            var chapterContent = @"
李明成功突破到筑基期，实力大增。他的修为从练气九层提升到筑基一层，
灵识范围扩大到方圆百米。张师傅看到李明的突破，露出了欣慰的笑容，
决定传授他更高级的剑法。两人的师徒关系更加深厚。
";

            try
            {
                var result = await characterService.UpdateCharactersAfterChapterAsync(
                    projectId: 1,
                    chapterNumber: 5,
                    chapterContent: chapterContent,
                    chapterTitle: "第5章：突破"
                );

                if (result.IsSuccess)
                {
                    Console.WriteLine("=== 角色更新演示 ===");
                    Console.WriteLine($"更新了 {result.UpdatedCharacterCount} 个角色");
                    
                    foreach (var change in result.CharacterChanges)
                    {
                        Console.WriteLine($"角色：{change.CharacterName}");
                        if (!string.IsNullOrEmpty(change.AttributeChanges))
                            Console.WriteLine($"  属性变化：{change.AttributeChanges}");
                        if (!string.IsNullOrEmpty(change.StatusChanges))
                            Console.WriteLine($"  状态变化：{change.StatusChanges}");
                        if (!string.IsNullOrEmpty(change.RelationshipChanges))
                            Console.WriteLine($"  关系变化：{change.RelationshipChanges}");
                    }
                }
                else
                {
                    Console.WriteLine($"角色更新失败：{result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"角色更新调用失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 演示时间线更新功能的使用
        /// </summary>
        public static async Task DemonstrateTimelineUpdate(TimelineService timelineService)
        {
            try
            {
                // 获取最后三章的时间线
                var timeline = await timelineService.GetLastThreeChaptersTimelineAsync(
                    projectId: 1,
                    currentChapter: 5
                );

                Console.WriteLine("=== 时间线演示 ===");
                Console.WriteLine("最后三章时间线：");
                Console.WriteLine(timeline);
                Console.WriteLine();

                // 更新当前章节的时间线
                var chapterContent = "李明成功突破到筑基期...";
                await timelineService.UpdateChapterTimelineAsync(
                    projectId: 1,
                    chapterNumber: 5,
                    chapterContent: chapterContent,
                    chapterTitle: "第5章：突破"
                );

                Console.WriteLine("时间线更新完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"时间线操作失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 完整的使用示例
        /// </summary>
        public static async Task CompleteExample(IServiceProvider serviceProvider)
        {
            Console.WriteLine("=== 增强写书功能完整演示 ===");
            Console.WriteLine();

            try
            {
                // 获取服务
                var enhancedService = serviceProvider.GetService<EnhancedChapterContentService>();
                var qualityService = serviceProvider.GetRequiredService<ContentQualityService>();
                var characterService = serviceProvider.GetService<CharacterUpdateService>();
                var timelineService = serviceProvider.GetService<TimelineService>();

                // 1. 生成增强章节内容
                if (enhancedService != null)
                {
                    Console.WriteLine("1. 生成增强章节内容...");
                    var content = await DemonstrateEnhancedChapterGeneration(enhancedService);
                    Console.WriteLine();
                }

                // 2. 演示内容清理
                Console.WriteLine("2. 演示内容清理...");
                DemonstrateContentCleaning(qualityService);
                Console.WriteLine();

                // 3. 演示角色更新
                if (characterService != null)
                {
                    Console.WriteLine("3. 演示角色更新...");
                    await DemonstrateCharacterUpdate(characterService);
                    Console.WriteLine();
                }

                // 4. 演示时间线更新
                if (timelineService != null)
                {
                    Console.WriteLine("4. 演示时间线更新...");
                    await DemonstrateTimelineUpdate(timelineService);
                    Console.WriteLine();
                }

                Console.WriteLine("=== 演示完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"演示过程中发生错误：{ex.Message}");
            }
        }
    }
}

[{"Name": "filesystem", "Description": "文件系统操作工具，提供文件读写、目录管理等功能", "ServerPath": "npx", "Args": ["@modelcontextprotocol/server-filesystem", "C:\\Users\\<USER>\\Documents"], "Enabled": true, "Environment": {}, "TimeoutMs": 30000, "AutoReconnect": true, "MaxReconnectAttempts": 3, "ReconnectIntervalMs": 5000, "WorkingDirectory": "", "LogLevel": "info"}, {"Name": "brave-search", "Description": "Brave搜索引擎工具，提供网页搜索功能", "ServerPath": "npx", "Args": ["@modelcontextprotocol/server-brave-search"], "Enabled": false, "Environment": {"BRAVE_API_KEY": "your-brave-api-key-here"}, "TimeoutMs": 30000, "AutoReconnect": true, "MaxReconnectAttempts": 3, "ReconnectIntervalMs": 5000, "WorkingDirectory": "", "LogLevel": "info"}, {"Name": "github", "Description": "GitHub仓库操作工具，提供代码仓库管理功能", "ServerPath": "npx", "Args": ["@modelcontextprotocol/server-github"], "Enabled": false, "Environment": {"GITHUB_PERSONAL_ACCESS_TOKEN": "your-github-token-here"}, "TimeoutMs": 45000, "AutoReconnect": true, "MaxReconnectAttempts": 5, "ReconnectIntervalMs": 10000, "WorkingDirectory": "", "LogLevel": "debug"}, {"Name": "sqlite", "Description": "SQLite数据库工具，提供数据库查询和操作功能", "ServerPath": "npx", "Args": ["@modelcontextprotocol/server-sqlite", "C:\\Data\\database.db"], "Enabled": false, "Environment": {}, "TimeoutMs": 60000, "AutoReconnect": true, "MaxReconnectAttempts": 3, "ReconnectIntervalMs": 5000, "WorkingDirectory": "", "LogLevel": "warn"}, {"Name": "puppeteer", "Description": "Puppeteer浏览器自动化工具，提供网页操作功能", "ServerPath": "npx", "Args": ["@modelcontextprotocol/server-puppeteer"], "Enabled": false, "Environment": {"PUPPETEER_EXECUTABLE_PATH": "", "PUPPETEER_HEADLESS": "true"}, "TimeoutMs": 120000, "AutoReconnect": false, "MaxReconnectAttempts": 1, "ReconnectIntervalMs": 30000, "WorkingDirectory": "", "LogLevel": "error"}, {"Name": "custom-tool", "Description": "自定义工具示例配置", "ServerPath": "python", "Args": ["C:\\Tools\\custom-mcp-server.py", "--port", "8080", "--config", "config.json"], "Enabled": false, "Environment": {"CUSTOM_API_KEY": "your-custom-api-key", "CUSTOM_CONFIG_PATH": "C:\\Tools\\config", "PYTHONPATH": "C:\\Tools\\lib"}, "TimeoutMs": 30000, "AutoReconnect": true, "MaxReconnectAttempts": 3, "ReconnectIntervalMs": 5000, "WorkingDirectory": "C:\\Tools", "LogLevel": "info", "CustomSettings": {"MaxConcurrentRequests": 10, "CacheEnabled": true, "CacheTTL": 3600, "RetryPolicy": "exponential"}}]
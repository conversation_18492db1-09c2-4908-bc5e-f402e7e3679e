<Window x:Class="DocumentCreationSystem.Views.OutlineGenerationDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="生成大纲" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="AI大纲生成" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,24"/>
        
        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 书籍信息 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="书籍信息" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <TextBox x:Name="BookTitleTextBox"
                                 materialDesign:HintAssist.Hint="书籍名称"
                                 Margin="0,0,0,16"
                                 Text=""/>
                        
                        <TextBox x:Name="CreativeDirectionTextBox"
                                 materialDesign:HintAssist.Hint="创作方向和主题"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 MinHeight="80"
                                 Margin="0,0,0,16"
                                 Text=""
                                 ToolTip="详细描述您想要创作的小说主题、故事背景、主要人物和核心冲突。这是生成大纲的重要依据，描述越详细，生成的大纲越符合您的构思。"/>

                        <CheckBox x:Name="UseReferenceContentCheckBox"
                                  Content="参考文档编辑器中的内容"
                                  IsChecked="True"
                                  Margin="0,0,0,8"
                                  ToolTip="开启后，AI会参考文档编辑器中的现有内容（如人物设定、世界观等）来生成大纲，确保大纲与已有设定保持一致。"/>

                        <CheckBox x:Name="AutoSaveCheckBox"
                                  Content="自动保存大纲到项目文件夹"
                                  IsChecked="True"
                                  Margin="0,0,0,8"
                                  ToolTip="开启后，生成的大纲会自动保存为文档文件到当前项目文件夹中，便于后续查看和修改。"/>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 参考内容预览 -->
                <materialDesign:Card x:Name="ReferenceContentCard" 
                                     Padding="16" 
                                     Margin="0,0,0,16"
                                     Visibility="Visible">
                    <StackPanel>
                        <TextBlock Text="参考内容预览" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <TextBox x:Name="ReferenceContentTextBox"
                                 IsReadOnly="True"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 MaxHeight="150"
                                 VerticalScrollBarVisibility="Auto"
                                 Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                 Text=""/>
                        
                        <TextBlock x:Name="ReferenceStatsTextBlock"
                                   Text=""
                                   Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                   Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 大纲设置 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="大纲设置" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="TargetChaptersTextBox"
                                         materialDesign:HintAssist.Hint="目标章节数"
                                         Text="100"
                                         Margin="0,0,0,16"
                                         ToolTip="设置小说的总章节数量。建议：短篇小说20-50章，中篇小说50-100章，长篇小说100-300章。这个数值会影响大纲的整体结构规划。"/>

                                <TextBox x:Name="TargetWordsPerChapterTextBox"
                                         materialDesign:HintAssist.Hint="每章字数"
                                         Text="6500"
                                         Margin="0,0,0,16"
                                         ToolTip="设置每章的目标字数。建议：网络小说2000-8000字/章，传统小说3000-6000字/章。这个数值会影响章节内容的详细程度和节奏安排。"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBox x:Name="VolumeCountTextBox"
                                         materialDesign:HintAssist.Hint="分卷数量"
                                         Text="10"
                                         Margin="0,0,0,16"
                                         ToolTip="将小说分成若干卷，每卷包含若干章节。建议：短篇1-2卷，中篇3-5卷，长篇5-15卷。分卷有助于故事结构层次化，便于情节安排和读者阅读。"/>

                                <ComboBox x:Name="OutlineDetailComboBox"
                                          materialDesign:HintAssist.Hint="大纲详细程度"
                                          SelectedIndex="1"
                                          Margin="0,0,0,16"
                                          ToolTip="选择大纲的详细程度：&#x0a;• 简要大纲：只包含主要情节线和关键转折点&#x0a;• 详细大纲：包含每章的主要内容和人物发展&#x0a;• 超详细大纲：包含详细的场景描述、对话要点和细节安排">
                                    <ComboBoxItem Content="简要大纲"/>
                                    <ComboBoxItem Content="详细大纲"/>
                                    <ComboBoxItem Content="超详细大纲"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 状态栏 -->
        <materialDesign:Card Grid.Row="2" 
                             Padding="12" 
                             Margin="0,16,0,16"
                             Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon x:Name="StatusIcon" 
                                         Kind="Information" 
                                         VerticalAlignment="Center" 
                                         Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusTextBlock" 
                           Text="准备生成大纲" 
                           VerticalAlignment="Center"/>
                <ProgressBar x:Name="ProgressBar" 
                             Width="100" 
                             Height="4" 
                             Margin="16,0,0,0"
                             Visibility="Collapsed"
                             IsIndeterminate="True"/>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="取消" 
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="0,0,8,0"
                    Click="Cancel_Click"/>
            <Button x:Name="GenerateButton"
                    Content="生成大纲" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="Generate_Click"/>
        </StackPanel>
    </Grid>
</Window>

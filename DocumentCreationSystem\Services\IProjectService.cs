using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 项目服务接口
/// </summary>
public interface IProjectService
{
    /// <summary>
    /// 创建新项目
    /// </summary>
    /// <param name="name">项目名称</param>
    /// <param name="type">项目类型</param>
    /// <param name="rootPath">项目根路径</param>
    /// <param name="description">项目描述</param>
    /// <returns>创建的项目</returns>
    Task<Project> CreateProjectAsync(string name, string type, string rootPath, string? description = null);

    /// <summary>
    /// 获取项目信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>项目信息</returns>
    Task<Project?> GetProjectAsync(int projectId);

    /// <summary>
    /// 根据项目ID获取项目信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>项目信息</returns>
    Task<Project?> GetProjectByIdAsync(int projectId);

    /// <summary>
    /// 根据项目路径获取项目信息
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <returns>项目信息</returns>
    Task<Project?> GetProjectByPathAsync(string projectPath);

    /// <summary>
    /// 获取所有项目
    /// </summary>
    /// <returns>项目列表</returns>
    Task<List<Project>> GetAllProjectsAsync();

    /// <summary>
    /// 更新项目信息
    /// </summary>
    /// <param name="project">项目对象</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateProjectAsync(Project project);

    /// <summary>
    /// 删除项目
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="deleteFiles">是否删除文件</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteProjectAsync(int projectId, bool deleteFiles = false);

    /// <summary>
    /// 创建小说项目
    /// </summary>
    /// <param name="projectName">项目名称</param>
    /// <param name="novelTitle">小说标题</param>
    /// <param name="creativeDirection">创作方向</param>
    /// <param name="rootPath">项目根路径</param>
    /// <returns>创建的小说项目</returns>
    Task<NovelProject> CreateNovelProjectAsync(string projectName, string novelTitle, string creativeDirection, string rootPath);

    /// <summary>
    /// 获取小说项目信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>小说项目信息</returns>
    Task<NovelProject?> GetNovelProjectAsync(int projectId);

    /// <summary>
    /// 更新小说项目信息
    /// </summary>
    /// <param name="novelProject">小说项目对象</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateNovelProjectAsync(NovelProject novelProject);

    /// <summary>
    /// 获取项目统计信息
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>统计信息</returns>
    Task<ProjectStatistics> GetProjectStatisticsAsync(int projectId);

    /// <summary>
    /// 初始化项目文件夹结构
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <param name="projectType">项目类型</param>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeProjectStructureAsync(string projectPath, string projectType);

    /// <summary>
    /// 备份项目
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="backupPath">备份路径</param>
    /// <returns>是否备份成功</returns>
    Task<bool> BackupProjectAsync(int projectId, string backupPath);

    /// <summary>
    /// 恢复项目
    /// </summary>
    /// <param name="backupPath">备份路径</param>
    /// <param name="restorePath">恢复路径</param>
    /// <returns>恢复的项目</returns>
    Task<Project?> RestoreProjectAsync(string backupPath, string restorePath);

    /// <summary>
    /// 检查项目路径是否有效
    /// </summary>
    /// <param name="path">路径</param>
    /// <returns>是否有效</returns>
    bool IsValidProjectPath(string path);

    /// <summary>
    /// 获取项目配置
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>项目配置</returns>
    Task<ProjectConfig?> GetProjectConfigAsync(int projectId);

    /// <summary>
    /// 更新项目配置
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="config">配置对象</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateProjectConfigAsync(int projectId, ProjectConfig config);
}

/// <summary>
/// 项目统计信息
/// </summary>
public class ProjectStatistics
{
    public int TotalDocuments { get; set; }
    public int TotalWordCount { get; set; }
    public int TotalCharacters { get; set; }
    public long TotalFileSize { get; set; }
    public DateTime LastModified { get; set; }
    public int VectorizedDocuments { get; set; }
    public int PendingVectorization { get; set; }

    // 小说项目特有统计
    public int? TotalChapters { get; set; }
    public int? CompletedChapters { get; set; }
    public int? TotalCharacterCount { get; set; }
    public float? CompletionPercentage { get; set; }
}

/// <summary>
/// 项目配置
/// </summary>
public class ProjectConfig
{
    public string AIModel { get; set; } = string.Empty;
    public string VectorModel { get; set; } = string.Empty;
    public int ChunkSize { get; set; } = 500;
    public int ChunkOverlap { get; set; } = 50;
    public bool AutoVectorize { get; set; } = true;
    public bool AutoBackup { get; set; } = false;
    public int BackupInterval { get; set; } = 24; // 小时
    public string DefaultDocumentFormat { get; set; } = "docx";
    public Dictionary<string, object> CustomSettings { get; set; } = new();

    // 小说项目特有配置
    public int? TargetWordsPerChapter { get; set; } = 6500;
    public int? ConsistencyCheckInterval { get; set; } = 1000; // 每多少字检查一次
    public bool? AutoGenerateOutline { get; set; } = true;
    public bool? AutoUpdateCharacters { get; set; } = true;
    public string? WritingStyle { get; set; }
}

/// <summary>
/// 项目类型常量
/// </summary>
public static class ProjectTypes
{
    public const string Normal = "Normal";
    public const string Novel = "Novel";
}

/// <summary>
/// 项目状态常量
/// </summary>
public static class ProjectStatus
{
    public const string Active = "Active";
    public const string Archived = "Archived";
    public const string Deleted = "Deleted";
}

using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// AI服务接口
/// </summary>
public interface IAIService
{
    /// <summary>
    /// 获取可用的AI模型列表
    /// </summary>
    Task<List<AIModel>> GetAvailableModelsAsync();

    /// <summary>
    /// 设置当前使用的AI模型
    /// </summary>
    Task<bool> SetCurrentModelAsync(string modelId);

    /// <summary>
    /// 获取当前使用的AI模型
    /// </summary>
    AIModel? GetCurrentModel();

    /// <summary>
    /// 生成文本内容
    /// </summary>
    /// <param name="prompt">提示词</param>
    /// <param name="maxTokens">最大token数</param>
    /// <param name="temperature">温度参数</param>
    /// <returns>生成的文本</returns>
    Task<string> GenerateTextAsync(string prompt, int maxTokens = 2000, float temperature = 0.7f);

    /// <summary>
    /// 润色文本
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="style">润色风格</param>
    /// <returns>润色后的文本</returns>
    Task<string> PolishTextAsync(string text, string style = "通用");

    /// <summary>
    /// 扩写文本
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="targetLength">目标长度</param>
    /// <param name="context">上下文信息</param>
    /// <returns>扩写后的文本</returns>
    Task<string> ExpandTextAsync(string text, int targetLength, string? context = null);

    /// <summary>
    /// 生成章节内容
    /// </summary>
    /// <param name="outline">章节大纲</param>
    /// <param name="context">上下文信息</param>
    /// <param name="targetWordCount">目标字数</param>
    /// <returns>生成的章节内容</returns>
    Task<string> GenerateChapterAsync(string outline, string? context = null, int targetWordCount = 6500);

    /// <summary>
    /// 检查上下文一致性
    /// </summary>
    /// <param name="currentText">当前文本</param>
    /// <param name="previousContext">之前的上下文</param>
    /// <returns>一致性检查结果</returns>
    Task<ConsistencyCheckResult> CheckConsistencyAsync(string currentText, string previousContext);

    /// <summary>
    /// 生成大纲
    /// </summary>
    /// <param name="description">描述</param>
    /// <param name="outlineType">大纲类型：全书、卷宗、章节</param>
    /// <returns>生成的大纲</returns>
    Task<string> GenerateOutlineAsync(string description, string outlineType);

    /// <summary>
    /// 提取角色信息
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <returns>提取的角色信息</returns>
    Task<List<CharacterInfo>> ExtractCharacterInfoAsync(string text);

    /// <summary>
    /// 获取当前提供者的可用模型（仅AIServiceManager实现）
    /// </summary>
    Task<List<AIModel>> GetCurrentProviderModelsAsync() => GetAvailableModelsAsync();

    /// <summary>
    /// 获取当前提供者名称（仅AIServiceManager实现）
    /// </summary>
    string? GetCurrentProviderName() => null;
}



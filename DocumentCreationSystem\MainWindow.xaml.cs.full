using System.Windows;
using System.Windows.Threading;
using System.Windows.Input;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.ViewModels;
using DocumentCreationSystem.Views;
using DocumentCreationSystem.Models;
using System.Windows.Documents;
using System.Windows.Controls;
using System.IO;
using System.Linq;
using System.Text;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace DocumentCreationSystem
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<MainWindow> _logger;
        private readonly IAIService _aiService;
        private readonly IProjectService _projectService;
        private readonly IDocumentService _documentService;
        private readonly IVectorService _vectorService;
        private readonly IProjectHistoryService _historyService;
        private readonly DispatcherTimer _timer;
        private MainWindowViewModel? _viewModel;
        private Project? _currentProject;

        public MainWindow(IServiceProvider serviceProvider)
        {
            ILogger<MainWindow>? logger = null;
            try
            {
                _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

                // 获取Logger
                logger = serviceProvider.GetRequiredService<ILogger<MainWindow>>();
                _logger = logger;
                _logger.LogInformation("开始初始化MainWindow...");

                // 先初始化XAML组件
                _logger.LogInformation("正在初始化XAML组件...");
                InitializeComponent();
                _logger.LogInformation("XAML组件初始化完成");

                // 逐个获取服务，每个都有错误处理
                try
                {
                    _aiService = serviceProvider.GetRequiredService<IAIService>();
                    _logger.LogInformation("IAIService 注入成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取IAIService失败");
                    throw new InvalidOperationException("无法获取AI服务", ex);
                }

                try
                {
                    _projectService = serviceProvider.GetRequiredService<IProjectService>();
                    _logger.LogInformation("IProjectService 注入成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取IProjectService失败");
                    throw new InvalidOperationException("无法获取项目服务", ex);
                }

                try
                {
                    _documentService = serviceProvider.GetRequiredService<IDocumentService>();
                    _logger.LogInformation("IDocumentService 注入成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取IDocumentService失败");
                    throw new InvalidOperationException("无法获取文档服务", ex);
                }

                try
                {
                    _vectorService = serviceProvider.GetRequiredService<IVectorService>();
                    _logger.LogInformation("IVectorService 注入成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取IVectorService失败");
                    throw new InvalidOperationException("无法获取向量服务", ex);
                }

                try
                {
                    _historyService = serviceProvider.GetRequiredService<IProjectHistoryService>();
                    _logger.LogInformation("IProjectHistoryService 注入成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "获取IVectorService失败");
                    throw new InvalidOperationException("无法获取向量服务", ex);
                }

                // 初始化定时器用于更新时间
                try
                {
                    _timer = new DispatcherTimer
                    {
                        Interval = TimeSpan.FromSeconds(1)
                    };
                    _timer.Tick += Timer_Tick;
                    _timer.Start();
                    _logger.LogInformation("定时器初始化成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "定时器初始化失败");
                    throw new InvalidOperationException("无法初始化定时器", ex);
                }

                // 延迟初始化界面，等待UI完全加载
                try
                {
                    this.Loaded += MainWindow_Loaded;
                    _logger.LogInformation("MainWindow构造函数完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "界面初始化设置失败");
                    throw new InvalidOperationException("无法设置界面初始化", ex);
                }
            }
            catch (Exception ex)
            {
                // 如果logger还没有初始化，使用MessageBox显示错误
                if (logger != null)
                {
                    logger.LogError(ex, "MainWindow构造函数中发生错误");
                }

                var errorMessage = $"MainWindow初始化失败:\n\n错误类型: {ex.GetType().Name}\n错误消息: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\n内部错误: {ex.InnerException.Message}";
                }
                errorMessage += $"\n\n堆栈跟踪:\n{ex.StackTrace}";

                MessageBox.Show(errorMessage, "MainWindow初始化错误", MessageBoxButton.OK, MessageBoxImage.Error);
                throw;
            }
        }

        private async void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await InitializeAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "MainWindow加载时初始化失败");
                MessageBox.Show($"界面初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("正在初始化主窗口...");

                // 检查必要的UI控件是否存在
                if (DocumentEditorControl == null)
                {
                    throw new InvalidOperationException("DocumentEditorControl未找到");
                }

                if (ProjectTreeView == null)
                {
                    throw new InvalidOperationException("ProjectTreeView未找到");
                }

                // 设置DocumentEditor的服务依赖
                try
                {
                    DocumentEditorControl.SetServices(_vectorService, _documentService);
                    _logger.LogInformation("DocumentEditor服务依赖设置成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "设置DocumentEditor服务依赖失败");
                    throw new InvalidOperationException("无法设置DocumentEditor服务依赖", ex);
                }

                // 订阅文档保存事件
                try
                {
                    DocumentEditorControl.DocumentSaved += DocumentEditor_DocumentSaved;
                    _logger.LogInformation("文档保存事件订阅成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "订阅文档保存事件失败");
                    throw new InvalidOperationException("无法订阅文档保存事件", ex);
                }

                // 创建并设置ViewModel（在UI初始化完成后）
                try
                {
                    _viewModel = new MainWindowViewModel(_serviceProvider);
                    DataContext = _viewModel;
                    _logger.LogInformation("ViewModel创建和设置成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建ViewModel失败");
                    throw new InvalidOperationException("无法创建ViewModel", ex);
                }

                // 手动设置项目树的数据源
                try
                {
                    ProjectTreeView.ItemsSource = _viewModel.ProjectItems;
                    _logger.LogInformation("项目树数据源设置成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "设置项目树数据源失败");
                    throw new InvalidOperationException("无法设置项目树数据源", ex);
                }

                // 加载历史项目列表
                try
                {
                    await LoadHistoryProjectsAsync();
                    _logger.LogInformation("历史项目列表加载成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "加载历史项目列表失败");
                    // 历史项目加载失败不应该阻止初始化
                }

                // 设置状态
                try
                {
                    UpdateStatus("系统就绪", true);
                    _logger.LogInformation("状态更新成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新状态失败");
                    // 状态更新失败不应该阻止初始化
                }

                _logger.LogInformation("主窗口初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化主窗口时发生错误");
                try
                {
                    UpdateStatus($"初始化失败: {ex.Message}", false);
                }
                catch
                {
                    // 如果连状态更新都失败了，忽略这个错误
                }
                throw; // 重新抛出异常，让调用者知道初始化失败
            }
        }



        private void Timer_Tick(object? sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private async void DocumentEditor_DocumentSaved(object? sender, Controls.DocumentSavedEventArgs e)
        {
            try
            {
                _logger.LogInformation($"文档已保存: {e.FilePath}");

                // 如果当前有选中的项目，更新项目路径
                if (_currentProject != null)
                {
                    DocumentEditorControl.CurrentProjectPath = _currentProject.RootPath;
                }

                // 更新状态
                UpdateStatus($"文档已保存: {Path.GetFileName(e.FilePath)}", true);

                // 如果文档在项目文件夹中，刷新项目导航
                if (_currentProject != null && e.FilePath.StartsWith(_currentProject.RootPath))
                {
                    await RefreshProjectNavigation();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理文档保存事件时发生错误");
                UpdateStatus("文档保存后处理失败", false);
            }
        }

        private void UpdateStatus(string message, bool isSuccess)
        {
            StatusText.Text = message;
            StatusIcon.Kind = isSuccess ?
                MaterialDesignThemes.Wpf.PackIconKind.CheckCircle :
                MaterialDesignThemes.Wpf.PackIconKind.AlertCircle;
            StatusIcon.Foreground = isSuccess ?
                System.Windows.Media.Brushes.LightGreen :
                System.Windows.Media.Brushes.Orange;
        }





        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }

        // 事件处理方法
        private async void PolishText_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(DocumentEditorControl.SelectedText))
                {
                    var selectedText = DocumentEditorControl.SelectedText;
                    UpdateStatus("正在润色文本...", true);

                    var polishedText = await _aiService.PolishTextAsync(selectedText, "文学");
                    DocumentEditorControl.SelectedText = polishedText;

                    UpdateStatus("文本润色完成", true);
                }
                else
                {
                    UpdateStatus("请先选择要润色的文本", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "润色文本时发生错误");
                UpdateStatus("润色失败", false);
            }
        }

        private async void ExpandText_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(DocumentEditorControl.SelectedText))
                {
                    var selectedText = DocumentEditorControl.SelectedText;
                    UpdateStatus("正在扩写内容...", true);

                    var expandedText = await _aiService.ExpandTextAsync(selectedText, 500, "");
                    DocumentEditorControl.SelectedText = expandedText;

                    UpdateStatus("内容扩写完成", true);
                }
                else
                {
                    UpdateStatus("请先选择要扩写的文本", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扩写内容时发生错误");
                UpdateStatus("扩写失败", false);
            }
        }

        private void GenerateOutline_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在打开大纲生成对话框...", false);

                // 创建大纲生成对话框
                var dialog = new Views.OutlineGenerationDialog(App.ServiceProvider);
                dialog.Owner = this;

                // 设置参考内容（文档编辑器中的内容）
                var referenceContent = DocumentEditorControl.Text;
                if (!string.IsNullOrWhiteSpace(referenceContent))
                {
                    dialog.ReferenceContent = referenceContent;
                }

                // 设置当前项目
                if (_currentProject != null)
                {
                    dialog.CurrentProject = _currentProject;
                }

                // 显示对话框
                var result = dialog.ShowDialog();

                if (result == true && dialog.IsConfirmed && !string.IsNullOrWhiteSpace(dialog.GeneratedOutline))
                {
                    // 将生成的大纲插入到编辑器中
                    var currentText = DocumentEditorControl.Text;
                    var insertPosition = currentText.Length;

                    var outlineText = string.IsNullOrEmpty(currentText)
                        ? dialog.GeneratedOutline
                        : "\n\n=== 生成的大纲 ===\n\n" + dialog.GeneratedOutline;

                    DocumentEditorControl.Text = currentText + outlineText;

                    UpdateStatus("大纲生成完成并已插入编辑器", true);
                    _logger.LogInformation("大纲生成完成并已插入编辑器");
                }
                else
                {
                    UpdateStatus("大纲生成已取消", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成大纲时发生错误");
                UpdateStatus("大纲生成失败", false);
                MessageBox.Show($"生成大纲时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CreateChapter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在创作章节...", true);

                var chapterOutline = "第一章：觉醒 - 主角在图书馆发现古籍，觉醒修仙资质";
                var chapterContent = await _aiService.GenerateTextAsync($"请根据以下章节大纲创作详细内容：{chapterOutline}");

                // 将章节内容插入到编辑器中
                DocumentEditorControl.Text += "\n\n" + chapterContent;

                UpdateStatus("章节创作完成", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创作章节时发生错误");
                UpdateStatus("章节创作失败", false);
            }
        }

        private void OneClickWriting_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在打开一键写书对话框...", false);

                // 检查是否有打开的项目
                if (_currentProject == null)
                {
                    MessageBox.Show("请先打开一个项目才能使用一键写书功能。", "提示",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 创建一键写书对话框
                var dialog = new Views.OneClickWritingDialog(App.ServiceProvider);
                dialog.Owner = this;

                // 设置参考内容（文档编辑器中的内容）
                var referenceContent = DocumentEditorControl.Text;
                if (!string.IsNullOrWhiteSpace(referenceContent))
                {
                    dialog.ReferenceContent = referenceContent;
                }

                // 设置当前项目
                dialog.CurrentProject = _currentProject;

                // 显示对话框
                var result = dialog.ShowDialog();

                if (result == true && dialog.IsCompleted)
                {
                    UpdateStatus("一键写书完成！", true);
                    _logger.LogInformation("一键写书完成");

                    // 刷新项目文件夹显示
                    if (_currentProject != null)
                    {
                        LoadProjectFolder(_currentProject.RootPath);
                    }
                }
                else
                {
                    UpdateStatus("一键写书已取消", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "一键写书时发生错误");
                UpdateStatus("一键写书失败", false);
                MessageBox.Show($"一键写书时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreativeRequest_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Views.CreativeRequestDialog(App.ServiceProvider);
                dialog.Owner = this;

                if (dialog.ShowDialog() == true && dialog.IsConfirmed && !string.IsNullOrWhiteSpace(dialog.GeneratedContent))
                {
                    // 将生成的内容插入到编辑器中
                    var currentText = DocumentEditorControl.Text;

                    // 在文档末尾插入内容
                    var newText = currentText + "\n\n" + dialog.GeneratedContent + "\n\n";
                    DocumentEditorControl.Text = newText;

                    UpdateStatus("创作内容已插入", true);
                    _logger.LogInformation("按要求创作内容已插入到编辑器");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "按要求创作时发生错误");
                UpdateStatus("创作失败", false);
            }
        }

        /// <summary>
        /// 从光标处续写功能
        /// </summary>
        private async void ContinueWriting_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在分析上下文并续写...", true);

                // 获取当前文档内容和光标位置
                var fullText = DocumentEditorControl.Text;
                var caretOffset = DocumentEditorControl.TextEditor?.CaretOffset ?? 0;

                if (string.IsNullOrWhiteSpace(fullText))
                {
                    MessageBox.Show("文档为空，请先输入一些内容作为续写的基础", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    UpdateStatus("续写取消", false);
                    return;
                }

                // 分析上下文
                var contextResult = AnalyzeContextForContinuation(fullText, caretOffset);

                if (string.IsNullOrWhiteSpace(contextResult.PreviousContext))
                {
                    MessageBox.Show("无法获取足够的上下文信息进行续写", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    UpdateStatus("续写取消", false);
                    return;
                }

                // 构建续写提示词
                var prompt = BuildContinuationPrompt(contextResult.PreviousContext, contextResult.CurrentPosition);

                // 获取AI参数
                var temperature = (float)TemperatureSlider.Value;
                var maxTokens = (int)MaxTokensSlider.Value;

                // 调用AI生成续写内容
                var continuationText = await _aiService.GenerateTextAsync(prompt, maxTokens, temperature);

                if (!string.IsNullOrWhiteSpace(continuationText))
                {
                    // 在光标位置插入续写内容
                    var beforeCursor = fullText.Substring(0, caretOffset);
                    var afterCursor = fullText.Substring(caretOffset);

                    // 确保续写内容前后有适当的空格或换行
                    var insertText = continuationText;
                    if (!beforeCursor.EndsWith(" ") && !beforeCursor.EndsWith("\n") && !insertText.StartsWith(" "))
                    {
                        insertText = " " + insertText;
                    }

                    var newText = beforeCursor + insertText + afterCursor;
                    DocumentEditorControl.Text = newText;

                    // 将光标移动到续写内容的末尾
                    var newCaretPosition = caretOffset + insertText.Length;
                    if (DocumentEditorControl.TextEditor != null)
                    {
                        DocumentEditorControl.TextEditor.CaretOffset = newCaretPosition;
                        DocumentEditorControl.TextEditor.Focus();
                    }

                    UpdateStatus($"续写完成，已添加 {continuationText.Length} 个字符", true);
                    _logger.LogInformation($"续写完成，在位置 {caretOffset} 插入了 {continuationText.Length} 个字符");
                }
                else
                {
                    UpdateStatus("续写失败，AI未返回内容", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "续写时发生错误");
                UpdateStatus("续写失败", false);
                MessageBox.Show($"续写时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 分析续写的上下文
        /// </summary>
        private (string PreviousContext, string CurrentPosition) AnalyzeContextForContinuation(string fullText, int caretOffset)
        {
            // 获取光标前的文本作为上下文
            var beforeCursor = fullText.Substring(0, caretOffset);
            var afterCursor = fullText.Substring(caretOffset);

            // 获取最近的上下文（最多1000个字符）
            var contextLength = Math.Min(1000, beforeCursor.Length);
            var previousContext = beforeCursor.Length > contextLength
                ? beforeCursor.Substring(beforeCursor.Length - contextLength)
                : beforeCursor;

            // 分析当前位置的情况
            var currentPosition = "文档中间";
            if (caretOffset == 0)
            {
                currentPosition = "文档开头";
            }
            else if (caretOffset >= fullText.Length)
            {
                currentPosition = "文档末尾";
            }
            else if (string.IsNullOrWhiteSpace(afterCursor.Trim()))
            {
                currentPosition = "文档末尾";
            }

            return (previousContext, currentPosition);
        }

        /// <summary>
        /// 构建续写的提示词
        /// </summary>
        private string BuildContinuationPrompt(string previousContext, string currentPosition)
        {
            var prompt = $@"请根据以下上下文内容进行自然的续写。要求：

1. 仔细分析上下文的内容、风格、语调和情节发展
2. 保持与前文的连贯性和一致性
3. 续写内容应该自然流畅，符合前文的写作风格
4. 如果是小说，请注意人物性格、情节发展的连贯性
5. 如果是技术文档，请保持专业性和逻辑性
6. 续写长度适中，大约200-500字
7. 不要重复前文内容，直接从当前位置继续

当前位置：{currentPosition}

上下文内容：
{previousContext}

请从上述内容的结尾处自然地继续写下去：";

            return prompt;
        }

        private async void CheckConsistency_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在检查一致性...", true);

                var currentText = DocumentEditorControl.Text;

                if (!string.IsNullOrWhiteSpace(currentText))
                {
                    var previousContext = "林晨是一名大学生，性格内向";
                    var result = await _aiService.CheckConsistencyAsync(currentText, previousContext);

                    UpdateStatus($"一致性检查完成: {(result.IsConsistent ? "通过" : "发现问题")}", result.IsConsistent);
                }
                else
                {
                    UpdateStatus("没有内容可检查", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查一致性时发生错误");
                UpdateStatus("一致性检查失败", false);
            }
        }

        private void ProjectTools_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentProject == null)
                {
                    MessageBox.Show("请先打开一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var toolsWindow = new ProjectToolsWindow(_serviceProvider, _currentProject.Id);
                toolsWindow.Owner = this;
                toolsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开项目工具窗口时发生错误");
                UpdateStatus("打开项目工具失败", false);
            }
        }

        private void Settings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var settingsDialog = new SettingsDialog(_serviceProvider);
                settingsDialog.Owner = this;
                settingsDialog.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开设置对话框时发生错误");
                UpdateStatus("打开设置失败", false);
            }
        }

        private void FullScreen_Click(object sender, RoutedEventArgs e)
        {
            // 这里可以实现全屏编辑功能
            // 暂时显示一个消息
            UpdateStatus("全屏编辑功能开发中...", true);
        }

        private async void AIModelConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 添加日志记录
                _logger.LogInformation("AI模型配置菜单被点击");
                UpdateStatus("正在打开AI模型配置窗口...", false);

                // 检查ServiceProvider是否可用
                if (App.ServiceProvider == null)
                {
                    _logger.LogError("ServiceProvider为null");
                    UpdateStatus("服务提供程序未初始化", true);
                    MessageBox.Show("服务提供程序未初始化，请重启应用程序。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 在UI线程创建配置窗口
                Views.AIModelConfigWindow? configWindow = null;

                try
                {
                    _logger.LogInformation("正在创建AI模型配置窗口");
                    configWindow = new Views.AIModelConfigWindow(App.ServiceProvider);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建AI配置窗口失败");
                    UpdateStatus($"创建配置窗口失败: {ex.Message}", true);
                    MessageBox.Show($"创建配置窗口失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 设置窗口所有者并显示
                configWindow.Owner = this;
                _logger.LogInformation("显示AI模型配置窗口");
                UpdateStatus("AI模型配置窗口已打开", false);

                var result = configWindow.ShowDialog();
                _logger.LogInformation($"配置窗口关闭，结果: {result}");

                if (result == true && configWindow.Result != null)
                {
                    UpdateStatus("正在保存配置...", false);
                    _logger.LogInformation("开始保存配置");

                    // 保存配置
                    var configService = App.ServiceProvider.GetRequiredService<IAIModelConfigService>();
                    await configService.SaveConfigAsync(configWindow.Result);

                    // 重新加载AI服务配置
                    UpdateStatus("正在重新加载AI服务...", false);
                    if (_aiService is AIServiceManager aiServiceManager)
                    {
                        await aiServiceManager.ReloadConfigurationAsync();
                        _logger.LogInformation("AI服务配置已重新加载");
                    }

                    UpdateStatus("AI模型配置已保存", false);
                    _logger.LogInformation("配置保存完成");
                }
                else
                {
                    UpdateStatus("配置已取消", false);
                    _logger.LogInformation("用户取消了配置");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开AI模型配置窗口失败");
                UpdateStatus($"配置失败: {ex.Message}", true);

                // 显示详细错误信息
                var errorMessage = $"打开AI模型配置窗口时发生错误：\n\n{ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\n内部错误：{ex.InnerException.Message}";
                }
                errorMessage += $"\n\n堆栈跟踪：\n{ex.StackTrace}";
                MessageBox.Show(errorMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private async void NewProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Views.NewProjectDialog();
                dialog.Owner = this;
                if (dialog.ShowDialog() == true && dialog.IsConfirmed)
                {
                    UpdateStatus("正在创建新项目...", true);

                    var project = await _projectService.CreateProjectAsync(
                        dialog.ProjectName,
                        dialog.ProjectType,
                        dialog.ProjectPath,
                        dialog.ProjectDescription);

                    UpdateStatus("项目创建成功", true);
                    _logger.LogInformation($"创建新项目: {dialog.ProjectName}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建项目时发生错误");
                UpdateStatus("项目创建失败", false);
            }
        }

        private void OpenProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("打开项目功能开发中...", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开项目时发生错误");
                UpdateStatus("项目打开失败", false);
            }
        }

        private async void SaveDocument_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_viewModel?.CurrentDocumentPath == null)
                {
                    UpdateStatus("没有打开的文档可保存", false);
                    return;
                }

                UpdateStatus("正在保存文档...", true);

                var content = DocumentEditorControl.Text;
                var filePath = _viewModel.CurrentDocumentPath;
                var extension = Path.GetExtension(filePath).ToLower();

                switch (extension)
                {
                    case ".docx":
                        SaveDocxFile(filePath, content);
                        break;
                    case ".txt":
                    case ".md":
                        await File.WriteAllTextAsync(filePath, content);
                        break;
                    default:
                        await File.WriteAllTextAsync(filePath, content);
                        break;
                }

                UpdateStatus($"文档已保存: {Path.GetFileName(filePath)}", true);
                _logger.LogInformation($"成功保存文档: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存文档时发生错误");
                UpdateStatus($"文档保存失败: {ex.Message}", false);
                MessageBox.Show($"保存文档失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveDocxFile(string filePath, string content)
        {
            try
            {
                // 创建备份
                var backupPath = filePath + ".backup";
                if (File.Exists(filePath))
                {
                    File.Copy(filePath, backupPath, true);
                }

                using var document = WordprocessingDocument.Create(filePath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document, true);

                // 添加主文档部分
                var mainPart = document.AddMainDocumentPart();
                mainPart.Document = new DocumentFormat.OpenXml.Wordprocessing.Document();
                var body = mainPart.Document.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Body());

                // 将文本内容按行分割并添加为段落
                var lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.None);
                foreach (var line in lines)
                {
                    var paragraph = new DocumentFormat.OpenXml.Wordprocessing.Paragraph();
                    var run = new DocumentFormat.OpenXml.Wordprocessing.Run();
                    var text = new DocumentFormat.OpenXml.Wordprocessing.Text(line);
                    run.Append(text);
                    paragraph.Append(run);
                    body.Append(paragraph);
                }

                // 删除备份文件
                if (File.Exists(backupPath))
                {
                    File.Delete(backupPath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存docx文件失败: {filePath}");

                // 恢复备份
                var backupPath = filePath + ".backup";
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, filePath, true);
                    File.Delete(backupPath);
                }

                throw new InvalidOperationException($"无法保存Word文档：{ex.Message}", ex);
            }
        }

        private async void CreateProjectFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 检查是否按住Ctrl键，如果是则创建简单文件夹
                if (Keyboard.IsKeyDown(Key.LeftCtrl) || Keyboard.IsKeyDown(Key.RightCtrl))
                {
                    await CreateSimpleFolderAsync();
                    return;
                }

                // 使用新的创建项目对话框
                var createDialog = new Views.CreateProjectDialog
                {
                    Owner = this
                };

                if (createDialog.ShowDialog() != true)
                {
                    UpdateStatus("取消创建项目文件夹", false);
                    return;
                }

                var folderName = createDialog.ProjectName;
                var projectType = createDialog.ProjectType;
                var basePath = createDialog.ProjectPath;
                var description = createDialog.ProjectDescription;

                // 构建完整的项目路径
                var projectFolderPath = Path.Combine(basePath, folderName);

                // 检查文件夹是否已存在
                if (Directory.Exists(projectFolderPath))
                {
                    var result = MessageBox.Show(
                        $"文件夹 '{folderName}' 已存在，是否要将其导入为项目？",
                        "文件夹已存在",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 检查数据库连接
                        try
                        {
                            var projectService = App.ServiceProvider.GetRequiredService<Services.IProjectService>();

                            // 先测试数据库连接
                            var testProjects = await projectService.GetAllProjectsAsync();
                            _logger.LogInformation($"数据库连接正常，当前有 {testProjects.Count} 个项目");

                            var existingProject = await projectService.GetProjectByPathAsync(projectFolderPath);

                            if (existingProject == null)
                        {
                            // 验证项目名称和路径
                            if (string.IsNullOrWhiteSpace(folderName) || folderName.Length > 200)
                            {
                                MessageBox.Show("项目名称无效或过长（最大200字符）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                return;
                            }

                            if (projectFolderPath.Length > 500)
                            {
                                MessageBox.Show("项目路径过长（最大500字符）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                return;
                            }

                            // 导入为新项目
                            var project = await projectService.CreateProjectAsync(
                                folderName,
                                "Normal",
                                projectFolderPath,
                                $"从现有文件夹导入的项目: {projectFolderPath}");

                            // 刷新项目导航
                            if (_viewModel != null)
                            {
                                await _viewModel.RefreshProjectsAsync();
                            }

                            UpdateStatus($"已导入项目文件夹: {folderName}", true);
                        }
                            else
                            {
                                UpdateStatus($"项目文件夹已存在: {folderName}", true);
                            }
                        }
                        catch (Exception dbEx)
                        {
                            _logger.LogError(dbEx, "数据库操作失败");
                            var errorMessage = $"数据库操作失败: {dbEx.Message}";
                            if (dbEx.InnerException != null)
                            {
                                errorMessage += $"\n内部错误: {dbEx.InnerException.Message}";
                            }
                            MessageBox.Show(errorMessage, "数据库错误", MessageBoxButton.OK, MessageBoxImage.Error);
                            UpdateStatus("数据库操作失败", false);
                            return;
                        }
                    }
                    else
                    {
                        UpdateStatus("取消创建项目文件夹", false);
                    }
                    return;
                }

                // 验证项目名称和路径
                if (string.IsNullOrWhiteSpace(folderName) || folderName.Length > 200)
                {
                    MessageBox.Show("项目名称无效或过长（最大200字符）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (projectFolderPath.Length > 500)
                {
                    MessageBox.Show("项目路径过长（最大500字符）", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                UpdateStatus($"正在创建项目文件夹: {folderName}", true);

                // 使用ProjectService创建项目
                var projectSvc = App.ServiceProvider.GetRequiredService<Services.IProjectService>();
                var historyService = App.ServiceProvider.GetRequiredService<Services.IProjectHistoryService>();

                // 先测试数据库连接
                var allProjects = await projectSvc.GetAllProjectsAsync();
                _logger.LogInformation($"数据库连接正常，当前有 {allProjects.Count} 个项目");

                var newProject = await projectSvc.CreateProjectAsync(
                    folderName,
                    projectType,
                    projectFolderPath,
                    string.IsNullOrWhiteSpace(description) ? $"通过对话框创建的项目: {folderName}" : description);

                // 添加到项目历史记录
                await historyService.AddOrUpdateHistoryAsync(projectFolderPath, folderName, projectType);

                // 刷新项目导航
                if (_viewModel != null)
                {
                    await _viewModel.RefreshProjectsAsync();
                    _viewModel.CurrentProjectName = newProject.Name;
                }

                UpdateStatus($"项目文件夹创建成功: {folderName}", true);
                _logger.LogInformation($"创建项目文件夹: {projectFolderPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建项目文件夹时发生错误");
                UpdateStatus("创建项目文件夹失败", false);
                MessageBox.Show($"创建项目文件夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 创建简单文件夹
        /// </summary>
        private async Task CreateSimpleFolderAsync()
        {
            try
            {
                // 获取文件夹名称
                string? folderName = ShowInputDialog("创建文件夹", "请输入文件夹名称:", "新文件夹");

                if (string.IsNullOrWhiteSpace(folderName))
                {
                    UpdateStatus("取消创建文件夹", false);
                    return;
                }

                // 验证文件夹名称（不能包含非法字符）
                var invalidChars = Path.GetInvalidFileNameChars();
                if (folderName.Any(c => invalidChars.Contains(c)))
                {
                    MessageBox.Show("文件夹名称包含非法字符，请重新输入", "验证错误",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 确定创建路径
                string basePath;
                if (_currentProject != null && Directory.Exists(_currentProject.RootPath))
                {
                    basePath = _currentProject.RootPath;
                }
                else
                {
                    // 使用软件根目录
                    basePath = AppDomain.CurrentDomain.BaseDirectory;
                }

                var folderPath = Path.Combine(basePath, folderName);

                // 检查文件夹是否已存在
                if (Directory.Exists(folderPath))
                {
                    var result = MessageBox.Show(
                        $"文件夹 '{folderName}' 已存在，是否继续？",
                        "文件夹已存在",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result != MessageBoxResult.Yes)
                    {
                        return;
                    }
                }
                else
                {
                    // 创建文件夹
                    Directory.CreateDirectory(folderPath);
                }

                // 添加到项目历史记录
                await _historyService.AddOrUpdateHistoryAsync(folderPath, folderName, "Normal");

                // 刷新项目导航
                if (_viewModel != null)
                {
                    await _viewModel.RefreshProjectsAsync();
                }

                // 刷新历史项目列表
                await LoadHistoryProjectsAsync();

                UpdateStatus($"文件夹创建成功: {folderName}", true);
                _logger.LogInformation($"创建简单文件夹: {folderPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建简单文件夹时发生错误");
                UpdateStatus("创建文件夹失败", false);
                MessageBox.Show($"创建文件夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示简单的输入对话框
        /// </summary>
        /// <param name="title">对话框标题</param>
        /// <param name="prompt">提示信息</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>用户输入的文本，如果取消则返回null</returns>
        private string? ShowInputDialog(string title, string prompt, string defaultValue = "")
        {
            var dialog = new Window
            {
                Title = title,
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this,
                ResizeMode = ResizeMode.NoResize
            };

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var promptLabel = new TextBlock
            {
                Text = prompt,
                Margin = new Thickness(20, 20, 20, 10),
                TextWrapping = TextWrapping.Wrap
            };
            Grid.SetRow(promptLabel, 0);
            grid.Children.Add(promptLabel);

            var textBox = new TextBox
            {
                Text = defaultValue,
                Margin = new Thickness(20, 0, 20, 20),
                Padding = new Thickness(5)
            };
            Grid.SetRow(textBox, 1);
            grid.Children.Add(textBox);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(20, 0, 20, 20)
            };

            var okButton = new Button
            {
                Content = "确定",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0),
                IsDefault = true
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30,
                IsCancel = true
            };

            bool? dialogResult = null;
            okButton.Click += (s, e) => { dialogResult = true; dialog.Close(); };
            cancelButton.Click += (s, e) => { dialogResult = false; dialog.Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            Grid.SetRow(buttonPanel, 2);
            grid.Children.Add(buttonPanel);

            dialog.Content = grid;
            textBox.Focus();
            textBox.SelectAll();

            dialog.ShowDialog();

            return dialogResult == true ? textBox.Text : null;
        }

        private async void CreateDocxFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 检查是否有选中的项目
                var selectedProject = GetSelectedProject();
                if (selectedProject == null)
                {
                    var result = MessageBox.Show(
                        "请先选择一个项目文件夹，或者创建一个新的项目文件夹。\n\n是否要创建新的项目文件夹？",
                        "未选择项目",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 调用创建项目文件夹功能
                        CreateProjectFolder_Click(sender, e);
                        return;
                    }
                    else
                    {
                        UpdateStatus("取消创建.docx文件", false);
                        return;
                    }
                }

                // 创建输入对话框让用户输入文件名
                string? fileName = ShowInputDialog("创建.docx文件", "请输入文件名称:", "新文档");

                if (string.IsNullOrWhiteSpace(fileName))
                {
                    UpdateStatus("取消创建.docx文件", false);
                    return;
                }

                // 确保文件名有.docx扩展名
                if (!fileName.EndsWith(".docx", StringComparison.OrdinalIgnoreCase))
                {
                    fileName += ".docx";
                }

                UpdateStatus($"正在创建.docx文件: {fileName}", true);

                // 使用DocumentService创建文档
                var documentService = App.ServiceProvider.GetRequiredService<Services.IDocumentService>();
                var document = await documentService.CreateDocumentAsync(
                    selectedProject.ProjectId ?? 0,
                    fileName,
                    $"这是一个新创建的文档：{Path.GetFileNameWithoutExtension(fileName)}\n\n创建时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // 刷新项目导航
                if (_viewModel != null)
                {
                    await _viewModel.RefreshProjectsAsync();
                }

                // 在编辑器中打开文档
                var documentContent = await documentService.ReadDocumentAsync(document.Id);
                DocumentEditorControl.Text = documentContent;

                if (_viewModel != null)
                {
                    _viewModel.DocumentContent = documentContent;

                    // 获取项目的完整信息来构建文档路径
                    var projectService = App.ServiceProvider.GetRequiredService<Services.IProjectService>();
                    var project = await projectService.GetProjectAsync(selectedProject.ProjectId ?? 0);
                    if (project != null)
                    {
                        _viewModel.CurrentDocumentPath = Path.Combine(project.RootPath, "Documents", fileName);
                    }
                }

                UpdateStatus($".docx文件创建成功: {fileName}", true);
                _logger.LogInformation($"创建.docx文件: {fileName} 在项目: {selectedProject.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建.docx文件时发生错误");
                UpdateStatus("创建.docx文件失败", false);
                MessageBox.Show($"创建文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取当前选中的项目
        /// </summary>
        /// <returns>选中的项目，如果没有选中则返回null</returns>
        private ViewModels.ProjectTreeItem? GetSelectedProject()
        {
            // 首先检查ViewModel中的选中项目
            if (_viewModel?.SelectedProjectItem != null)
            {
                var selectedItem = _viewModel.SelectedProjectItem;

                // 如果选中的是项目，直接返回
                if (selectedItem.Type == ViewModels.ProjectTreeItemType.Project && selectedItem.ProjectId.HasValue)
                {
                    return selectedItem;
                }

                // 如果选中的是文档，找到其父项目
                if (selectedItem.Type == ViewModels.ProjectTreeItemType.Document && selectedItem.ProjectId.HasValue)
                {
                    var parentProject = _viewModel.ProjectItems.FirstOrDefault(p => p.ProjectId == selectedItem.ProjectId);
                    if (parentProject != null)
                    {
                        return parentProject;
                    }
                }
            }

            // 如果没有通过ViewModel找到，检查是否有当前项目
            if (_currentProject != null)
            {
                // 在项目列表中找到对应的项目项
                var projectItem = _viewModel?.ProjectItems.FirstOrDefault(p => p.ProjectId == _currentProject.Id);
                if (projectItem != null)
                {
                    return projectItem;
                }

                // 如果项目列表中没有找到，创建一个临时的ProjectTreeItem
                return new ViewModels.ProjectTreeItem
                {
                    Name = _currentProject.Name,
                    Type = ViewModels.ProjectTreeItemType.Project,
                    ProjectId = _currentProject.Id
                };
            }

            // 如果都没有，检查是否只有一个项目
            if (_viewModel?.ProjectItems.Count == 1)
            {
                var firstProject = _viewModel.ProjectItems.First();
                if (firstProject.ProjectId.HasValue)
                {
                    return firstProject;
                }
            }

            return null;
        }

        /// <summary>
        /// 创建.docx文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">文件内容</param>
        private async Task CreateDocxFileAsync(string filePath, string content)
        {
            await Task.Run(() =>
            {
                using var document = DocumentFormat.OpenXml.Packaging.WordprocessingDocument.Create(filePath, DocumentFormat.OpenXml.WordprocessingDocumentType.Document);
                var mainPart = document.AddMainDocumentPart();
                mainPart.Document = new DocumentFormat.OpenXml.Wordprocessing.Document();
                var body = mainPart.Document.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Body());

                if (!string.IsNullOrEmpty(content))
                {
                    var paragraphs = content.Split('\n', StringSplitOptions.None);
                    foreach (var paragraphText in paragraphs)
                    {
                        var paragraph = new DocumentFormat.OpenXml.Wordprocessing.Paragraph();
                        var run = new DocumentFormat.OpenXml.Wordprocessing.Run();
                        run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(paragraphText));
                        paragraph.AppendChild(run);
                        body.AppendChild(paragraph);
                    }
                }
                else
                {
                    // 创建一个空段落
                    var paragraph = new DocumentFormat.OpenXml.Wordprocessing.Paragraph();
                    var run = new DocumentFormat.OpenXml.Wordprocessing.Run();
                    run.AppendChild(new DocumentFormat.OpenXml.Wordprocessing.Text(""));
                    paragraph.AppendChild(run);
                    body.AppendChild(paragraph);
                }
            });
        }

        private async void OpenProjectFolder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderDialog = new Microsoft.Win32.OpenFolderDialog
                {
                    Title = "选择项目文件夹",
                    Multiselect = false
                };

                if (folderDialog.ShowDialog() == true)
                {
                    var selectedPath = folderDialog.FolderName;
                    var projectName = Path.GetFileName(selectedPath);
                    UpdateStatus($"正在打开项目文件夹: {selectedPath}", true);

                    // 添加到项目历史记录
                    var historyService = App.ServiceProvider.GetRequiredService<Services.IProjectHistoryService>();
                    await historyService.AddOrUpdateHistoryAsync(selectedPath, projectName, "Normal");

                    // 创建或加载项目
                    _currentProject = new Project
                    {
                        Id = 1, // 临时ID
                        Name = projectName,
                        RootPath = selectedPath,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    // 加载项目文件夹内容
                    LoadProjectFolder(selectedPath);

                    UpdateStatus("项目文件夹已打开", true);
                    _logger.LogInformation($"打开项目文件夹: {selectedPath}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开项目文件夹时发生错误");
                UpdateStatus("打开项目文件夹失败", false);
            }
        }

        private async void RefreshProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在刷新项目...", true);

                // 清理无效的历史记录
                var cleanedCount = await _historyService.CleanupInvalidHistoryAsync();
                if (cleanedCount > 0)
                {
                    _logger.LogInformation($"清理了 {cleanedCount} 个无效的历史记录");
                }

                // 刷新项目数据
                if (_viewModel != null)
                {
                    await _viewModel.RefreshProjectsAsync();
                    _viewModel.StatusMessage = "项目数据已刷新";
                }

                // 刷新历史项目列表
                await LoadHistoryProjectsAsync();

                // 如果当前有项目，刷新项目导航
                if (_currentProject != null)
                {
                    await RefreshProjectNavigation();
                }

                UpdateStatus("项目已刷新", true);
                _logger.LogInformation("项目已刷新");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新项目时发生错误");
                UpdateStatus("项目刷新失败", false);
                MessageBox.Show($"刷新项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProjectFolder(string folderPath)
        {
            try
            {
                // 更新状态显示当前文件夹
                if (_viewModel != null)
                {
                    _viewModel.CurrentProjectName = $"文件夹: {System.IO.Path.GetFileName(folderPath)}";

                    // 清空当前项目树
                    _viewModel.ProjectItems.Clear();

                    // 扫描文件夹中的文档文件
                    ScanFolderFiles(folderPath);
                }

                _logger.LogInformation($"项目文件夹加载完成: {folderPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载项目文件夹失败: {folderPath}");
                throw;
            }
        }

        private void ScanFolderFiles(string folderPath)
        {
            try
            {
                // 支持的文档文件扩展名
                var supportedExtensions = new[] { ".docx", ".doc", ".txt", ".md" };

                // 获取文件夹中的所有文档文件
                var files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories)
                    .Where(file => supportedExtensions.Contains(Path.GetExtension(file).ToLower()))
                    .OrderBy(file => file)
                    .ToList();

                if (files.Any())
                {
                    // 创建文件夹根节点
                    var folderItem = new ViewModels.ProjectTreeItem
                    {
                        Name = Path.GetFileName(folderPath),
                        Type = ViewModels.ProjectTreeItemType.Project,
                        Children = new System.Collections.ObjectModel.ObservableCollection<ViewModels.ProjectTreeItem>()
                    };

                    // 按目录结构组织文件
                    var fileGroups = files.GroupBy(file => Path.GetDirectoryName(file));

                    foreach (var group in fileGroups)
                    {
                        var relativePath = Path.GetRelativePath(folderPath, group.Key ?? folderPath);

                        // 如果是根目录的文件，直接添加到根节点
                        if (relativePath == ".")
                        {
                            foreach (var file in group)
                            {
                                var fileItem = new ViewModels.ProjectTreeItem
                                {
                                    Name = Path.GetFileName(file),
                                    Type = ViewModels.ProjectTreeItemType.Document,
                                    FilePath = file
                                };
                                folderItem.Children.Add(fileItem);
                            }
                        }
                        else
                        {
                            // 创建子文件夹节点
                            var subFolderItem = new ViewModels.ProjectTreeItem
                            {
                                Name = relativePath,
                                Type = ViewModels.ProjectTreeItemType.Project,
                                Children = new System.Collections.ObjectModel.ObservableCollection<ViewModels.ProjectTreeItem>()
                            };

                            foreach (var file in group)
                            {
                                var fileItem = new ViewModels.ProjectTreeItem
                                {
                                    Name = Path.GetFileName(file),
                                    Type = ViewModels.ProjectTreeItemType.Document,
                                    FilePath = file
                                };
                                subFolderItem.Children.Add(fileItem);
                            }

                            folderItem.Children.Add(subFolderItem);
                        }
                    }

                    _viewModel?.ProjectItems.Add(folderItem);
                    _logger.LogInformation($"扫描到 {files.Count} 个文档文件");
                }
                else
                {
                    _logger.LogInformation("文件夹中没有找到支持的文档文件");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "扫描文件夹文件时发生错误");
                throw;
            }
        }

        private async void ProjectTreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is ViewModels.ProjectTreeItem selectedItem &&
                selectedItem.Type == ViewModels.ProjectTreeItemType.Document &&
                !string.IsNullOrEmpty(selectedItem.FilePath))
            {
                await LoadDocumentAsync(selectedItem.FilePath);
            }
        }

        /// <summary>
        /// 项目树双击事件处理
        /// </summary>
        private async void ProjectTreeView_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            try
            {
                if (ProjectTreeView.SelectedItem is ViewModels.ProjectTreeItem selectedItem)
                {
                    // 如果是文件夹类型，进入该文件夹
                    if (selectedItem.Type == ViewModels.ProjectTreeItemType.Project)
                    {
                        await EnterProjectFolderAsync(selectedItem);
                    }
                    // 如果是文档类型，打开文档（这个已经在SelectedItemChanged中处理了）
                    else if (selectedItem.Type == ViewModels.ProjectTreeItemType.Document &&
                             !string.IsNullOrEmpty(selectedItem.FilePath))
                    {
                        await LoadDocumentAsync(selectedItem.FilePath);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理项目树双击事件失败");
                UpdateStatus("操作失败", false);
            }
        }

        /// <summary>
        /// 进入项目文件夹
        /// </summary>
        private async Task EnterProjectFolderAsync(ViewModels.ProjectTreeItem folderItem)
        {
            try
            {
                string folderPath = string.Empty;

                // 获取文件夹路径
                if (!string.IsNullOrEmpty(folderItem.FilePath))
                {
                    // 如果有直接的文件路径，使用它
                    folderPath = folderItem.FilePath;
                }
                else
                {
                    // 否则根据当前项目和文件夹名称构建路径
                    folderPath = GetFolderPath(folderItem);
                }

                if (string.IsNullOrEmpty(folderPath) || !Directory.Exists(folderPath))
                {
                    MessageBox.Show($"文件夹路径不存在: {folderPath}", "路径错误",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                UpdateStatus($"正在进入文件夹: {folderItem.Name}", true);

                // 添加到历史记录
                await _historyService.AddOrUpdateHistoryAsync(folderPath, folderItem.Name, "Normal");

                // 创建新的项目对象
                _currentProject = new Project
                {
                    Id = 1, // 临时ID
                    Name = folderItem.Name,
                    RootPath = folderPath,
                    Type = "Normal",
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // 加载文件夹内容
                LoadProjectFolder(folderPath);

                // 刷新历史列表
                await LoadHistoryProjectsAsync();

                UpdateStatus($"已进入文件夹: {folderItem.Name}", true);
                _logger.LogInformation($"进入项目文件夹: {folderPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"进入项目文件夹失败: {folderItem.Name}");
                UpdateStatus("进入文件夹失败", false);
                MessageBox.Show($"进入文件夹失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadDocumentAsync(string filePath)
        {
            try
            {
                UpdateStatus($"正在加载文档: {Path.GetFileName(filePath)}", true);

                string content = "";
                var extension = Path.GetExtension(filePath).ToLower();

                switch (extension)
                {
                    case ".docx":
                        content = ReadDocxFile(filePath);
                        break;
                    case ".txt":
                        content = await File.ReadAllTextAsync(filePath);
                        break;
                    case ".md":
                        content = await File.ReadAllTextAsync(filePath);
                        break;
                    default:
                        content = await File.ReadAllTextAsync(filePath);
                        break;
                }

                // 更新编辑器内容
                DocumentEditorControl.Text = content;

                // 更新ViewModel
                if (_viewModel != null)
                {
                    _viewModel.DocumentContent = content;
                    _viewModel.CurrentDocumentPath = filePath;
                }

                UpdateStatus($"文档已加载: {Path.GetFileName(filePath)}", true);
                _logger.LogInformation($"成功加载文档: {filePath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载文档失败: {filePath}");
                UpdateStatus($"加载文档失败: {ex.Message}", false);
                MessageBox.Show($"无法加载文档：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string ReadDocxFile(string filePath)
        {
            try
            {
                using var document = WordprocessingDocument.Open(filePath, false);
                var body = document.MainDocumentPart?.Document?.Body;
                if (body == null) return "";

                var text = new StringBuilder();
                foreach (var paragraph in body.Elements<DocumentFormat.OpenXml.Wordprocessing.Paragraph>())
                {
                    text.AppendLine(paragraph.InnerText);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"读取docx文件失败: {filePath}");
                throw new InvalidOperationException($"无法读取Word文档：{ex.Message}", ex);
            }
        }

        private async void TestAIConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateStatus("正在测试AI连接...", true);

                // 测试当前AI服务连接
                var testResult = await _aiService.GenerateTextAsync("测试连接");

                if (!string.IsNullOrEmpty(testResult))
                {
                    UpdateStatus("AI连接测试成功", true);
                    MessageBox.Show("AI模型连接正常！", "连接测试", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    UpdateStatus("AI连接测试失败", false);
                    MessageBox.Show("AI模型连接失败，请检查配置。", "连接测试", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试AI连接时发生错误");
                UpdateStatus("AI连接测试失败", false);
                MessageBox.Show($"连接测试失败：{ex.Message}", "连接测试", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 刷新项目导航
        /// </summary>
        private async Task RefreshProjectNavigation()
        {
            try
            {
                if (_currentProject == null)
                {
                    // 如果没有当前项目，刷新ViewModel中的项目列表
                    if (_viewModel != null)
                    {
                        await _viewModel.RefreshProjectsAsync();
                    }
                    return;
                }

                // 检查当前项目路径是否仍然存在
                if (!Directory.Exists(_currentProject.RootPath))
                {
                    _logger.LogWarning($"当前项目路径不存在: {_currentProject.RootPath}");
                    _currentProject = null;

                    // 清空项目树
                    if (_viewModel != null)
                    {
                        _viewModel.ProjectItems.Clear();
                        _viewModel.CurrentProjectName = "未选择项目";
                    }

                    UpdateStatus("当前项目路径不存在，已清空项目", false);
                    return;
                }

                // 重新加载项目文件夹内容
                LoadProjectFolder(_currentProject.RootPath);

                _logger.LogInformation($"项目导航已刷新: {_currentProject.Name}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新项目导航失败");
            }
        }

        #region 项目导航右键菜单事件处理

        /// <summary>
        /// 复制文件或文件夹
        /// </summary>
        private void CopyItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItem = ProjectTreeView.SelectedItem as ViewModels.ProjectTreeItem;
                if (selectedItem == null)
                {
                    MessageBox.Show("请先选择要复制的项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (!string.IsNullOrEmpty(selectedItem.FilePath) && File.Exists(selectedItem.FilePath))
                {
                    // 复制文件到剪贴板
                    var files = new System.Collections.Specialized.StringCollection();
                    files.Add(selectedItem.FilePath);
                    Clipboard.SetFileDropList(files);
                    UpdateStatus($"已复制文件: {selectedItem.Name}", true);
                    _logger.LogInformation($"复制文件: {selectedItem.FilePath}");
                }
                else if (selectedItem.Type == ViewModels.ProjectTreeItemType.Project)
                {
                    // 对于文件夹，复制整个文件夹路径
                    var folderPath = GetFolderPath(selectedItem);
                    if (!string.IsNullOrEmpty(folderPath) && Directory.Exists(folderPath))
                    {
                        var files = new System.Collections.Specialized.StringCollection();
                        files.Add(folderPath);
                        Clipboard.SetFileDropList(files);
                        UpdateStatus($"已复制文件夹: {selectedItem.Name}", true);
                        _logger.LogInformation($"复制文件夹: {folderPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "复制项目失败");
                UpdateStatus("复制失败", false);
                MessageBox.Show($"复制失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 粘贴文件或文件夹
        /// </summary>
        private async void PasteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!Clipboard.ContainsFileDropList())
                {
                    MessageBox.Show("剪贴板中没有文件", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var selectedItem = ProjectTreeView.SelectedItem as ViewModels.ProjectTreeItem;
                var targetPath = GetTargetPasteDirectory(selectedItem);

                if (string.IsNullOrEmpty(targetPath))
                {
                    MessageBox.Show("无法确定粘贴目标位置", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var files = Clipboard.GetFileDropList();
                var copiedCount = 0;

                foreach (string? sourceFile in files)
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(sourceFile) && File.Exists(sourceFile))
                        {
                            var fileName = Path.GetFileName(sourceFile);
                            var targetFile = Path.Combine(targetPath, fileName);

                            // 如果目标文件已存在，询问是否覆盖
                            if (File.Exists(targetFile))
                            {
                                var result = MessageBox.Show($"文件 {fileName} 已存在，是否覆盖？",
                                    "确认覆盖", MessageBoxButton.YesNo, MessageBoxImage.Question);
                                if (result != MessageBoxResult.Yes)
                                    continue;
                            }

                            File.Copy(sourceFile, targetFile, true);
                            copiedCount++;
                        }
                        else if (Directory.Exists(sourceFile))
                        {
                            var dirName = Path.GetFileName(sourceFile);
                            var targetDir = Path.Combine(targetPath, dirName);
                            CopyDirectory(sourceFile, targetDir);
                            copiedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"粘贴文件失败: {sourceFile}");
                    }
                }

                if (copiedCount > 0)
                {
                    UpdateStatus($"已粘贴 {copiedCount} 个项目", true);
                    await RefreshProjectNavigation();
                }
                else
                {
                    UpdateStatus("粘贴失败", false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "粘贴操作失败");
                UpdateStatus("粘贴失败", false);
                MessageBox.Show($"粘贴失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 查看文件或文件夹路径
        /// </summary>
        private void ShowPath_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItem = ProjectTreeView.SelectedItem as ViewModels.ProjectTreeItem;
                if (selectedItem == null)
                {
                    MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                string path = string.Empty;
                if (!string.IsNullOrEmpty(selectedItem.FilePath))
                {
                    path = selectedItem.FilePath;
                }
                else
                {
                    path = GetFolderPath(selectedItem);
                }

                if (!string.IsNullOrEmpty(path))
                {
                    // 将路径复制到剪贴板
                    Clipboard.SetText(path);
                    MessageBox.Show($"路径: {path}\n\n路径已复制到剪贴板", "文件路径",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    _logger.LogInformation($"查看路径: {path}");
                }
                else
                {
                    MessageBox.Show("无法获取路径信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查看路径失败");
                MessageBox.Show($"查看路径失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在资源管理器中打开
        /// </summary>
        private void OpenInExplorer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItem = ProjectTreeView.SelectedItem as ViewModels.ProjectTreeItem;
                if (selectedItem == null)
                {
                    MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                string path = string.Empty;
                if (!string.IsNullOrEmpty(selectedItem.FilePath) && File.Exists(selectedItem.FilePath))
                {
                    // 选中文件并打开资源管理器
                    System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{selectedItem.FilePath}\"");
                    _logger.LogInformation($"在资源管理器中打开文件: {selectedItem.FilePath}");
                }
                else
                {
                    path = GetFolderPath(selectedItem);
                    if (!string.IsNullOrEmpty(path) && Directory.Exists(path))
                    {
                        System.Diagnostics.Process.Start("explorer.exe", path);
                        _logger.LogInformation($"在资源管理器中打开文件夹: {path}");
                    }
                    else
                    {
                        MessageBox.Show("路径不存在", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                }

                UpdateStatus("已在资源管理器中打开", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "在资源管理器中打开失败");
                MessageBox.Show($"打开失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除文件或文件夹
        /// </summary>
        private async void DeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedItem = ProjectTreeView.SelectedItem as ViewModels.ProjectTreeItem;
                if (selectedItem == null)
                {
                    MessageBox.Show("请先选择要删除的项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                string itemPath = string.Empty;
                string itemType = string.Empty;

                if (!string.IsNullOrEmpty(selectedItem.FilePath) && File.Exists(selectedItem.FilePath))
                {
                    itemPath = selectedItem.FilePath;
                    itemType = "文件";
                }
                else
                {
                    itemPath = GetFolderPath(selectedItem);
                    itemType = "文件夹";
                }

                if (string.IsNullOrEmpty(itemPath))
                {
                    MessageBox.Show("无法确定要删除的项目路径", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 确认删除
                var result = MessageBox.Show($"确定要删除{itemType} \"{selectedItem.Name}\" 吗？\n\n路径: {itemPath}\n\n注意：此操作不可撤销！",
                    "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;

                // 执行删除
                if (itemType == "文件")
                {
                    File.Delete(itemPath);
                }
                else
                {
                    Directory.Delete(itemPath, true);
                }

                UpdateStatus($"已删除{itemType}: {selectedItem.Name}", true);
                _logger.LogInformation($"删除{itemType}: {itemPath}");

                // 刷新项目导航
                await RefreshProjectNavigation();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除项目失败");
                UpdateStatus("删除失败", false);
                MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取文件夹路径
        /// </summary>
        private string GetFolderPath(ViewModels.ProjectTreeItem item)
        {
            if (item == null) return string.Empty;

            // 如果是文件，返回其所在目录
            if (!string.IsNullOrEmpty(item.FilePath) && File.Exists(item.FilePath))
            {
                return Path.GetDirectoryName(item.FilePath) ?? string.Empty;
            }

            // 如果是项目文件夹，直接返回其FilePath（项目根路径）
            if (item.Type == ViewModels.ProjectTreeItemType.Project && !string.IsNullOrEmpty(item.FilePath))
            {
                return item.FilePath;
            }

            // 如果是项目根目录但FilePath为空，使用当前项目的RootPath
            if (item.Type == ViewModels.ProjectTreeItemType.Project && _currentProject != null)
            {
                return _currentProject.RootPath;
            }

            return string.Empty;
        }

        /// <summary>
        /// 获取粘贴目标目录
        /// </summary>
        private string GetTargetPasteDirectory(ViewModels.ProjectTreeItem? selectedItem)
        {
            if (selectedItem == null)
            {
                // 如果没有选中项目，使用当前项目根目录
                return _currentProject?.RootPath ?? string.Empty;
            }

            // 如果选中的是文件，使用其所在目录
            if (!string.IsNullOrEmpty(selectedItem.FilePath) && File.Exists(selectedItem.FilePath))
            {
                return Path.GetDirectoryName(selectedItem.FilePath) ?? string.Empty;
            }

            // 如果选中的是文件夹，使用该文件夹
            if (selectedItem.Type == ViewModels.ProjectTreeItemType.Project)
            {
                return GetFolderPath(selectedItem);
            }

            // 默认使用项目根目录
            return _currentProject?.RootPath ?? string.Empty;
        }

        /// <summary>
        /// 复制目录及其所有内容
        /// </summary>
        private void CopyDirectory(string sourceDir, string targetDir)
        {
            if (!Directory.Exists(sourceDir))
                return;

            // 创建目标目录
            Directory.CreateDirectory(targetDir);

            // 复制文件
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string fileName = Path.GetFileName(file);
                string targetFile = Path.Combine(targetDir, fileName);
                File.Copy(file, targetFile, true);
            }

            // 递归复制子目录
            foreach (string subDir in Directory.GetDirectories(sourceDir))
            {
                string dirName = Path.GetFileName(subDir);
                string targetSubDir = Path.Combine(targetDir, dirName);
                CopyDirectory(subDir, targetSubDir);
            }
        }

        #endregion

        #region 历史项目管理

        /// <summary>
        /// 加载历史项目列表
        /// </summary>
        private async Task LoadHistoryProjectsAsync()
        {
            try
            {
                // 先清理无效的历史记录
                var cleanedCount = await _historyService.CleanupInvalidHistoryAsync();
                if (cleanedCount > 0)
                {
                    _logger.LogInformation($"自动清理了 {cleanedCount} 个无效的历史记录");
                }

                var historyProjects = await _historyService.GetRecentHistoryAsync(20);
                HistoryProjectListBox.ItemsSource = historyProjects;
                _logger.LogInformation($"加载了 {historyProjects.Count} 个历史项目");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载历史项目列表失败");
            }
        }

        /// <summary>
        /// 刷新历史项目列表
        /// </summary>
        private async void RefreshHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadHistoryProjectsAsync();
                UpdateStatus("历史项目列表已刷新", true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新历史项目列表失败");
                UpdateStatus("刷新历史项目列表失败", false);
            }
        }

        /// <summary>
        /// 清理无效历史记录
        /// </summary>
        private async void CleanupInvalidHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 先获取无效记录数量
                var invalidHistories = await _historyService.GetInvalidHistoryAsync();

                if (invalidHistories.Count == 0)
                {
                    MessageBox.Show("没有发现无效的历史记录。", "信息",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"发现 {invalidHistories.Count} 个无效的历史记录（路径不存在）。是否要清理这些记录？",
                    "清理无效记录",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var cleanedCount = await _historyService.CleanupInvalidHistoryAsync();
                    await LoadHistoryProjectsAsync();
                    UpdateStatus($"已清理 {cleanedCount} 个无效记录", true);

                    MessageBox.Show($"成功清理了 {cleanedCount} 个无效的历史记录。", "清理完成",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理无效历史记录失败");
                UpdateStatus("清理无效记录失败", false);
                MessageBox.Show($"清理无效记录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清空历史记录
        /// </summary>
        private async void ClearHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要清空所有历史记录吗？此操作不可撤销。",
                    "确认清空",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await _historyService.ClearAllHistoryAsync();
                    await LoadHistoryProjectsAsync();
                    UpdateStatus("历史记录已清空", true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空历史记录失败");
                UpdateStatus("清空历史记录失败", false);
                MessageBox.Show($"清空历史记录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 历史项目列表选择变化
        /// </summary>
        private void HistoryProjectListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0 && e.AddedItems[0] is ProjectHistory selectedHistory)
            {
                try
                {
                    // 双击才打开项目，单击只是选中
                    // 这里可以显示项目详细信息
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "处理历史项目选择失败");
                }
            }
        }

        /// <summary>
        /// 切换收藏状态
        /// </summary>
        private async void ToggleFavorite_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is ProjectHistory history)
                {
                    await _historyService.SetFavoriteAsync(history.RootPath, !history.IsFavorite);
                    await LoadHistoryProjectsAsync();

                    var status = history.IsFavorite ? "已取消收藏" : "已添加收藏";
                    UpdateStatus($"{history.Name} {status}", true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换收藏状态失败");
                UpdateStatus("切换收藏状态失败", false);
            }
        }

        /// <summary>
        /// 打开历史项目
        /// </summary>
        private async void OpenHistoryProject_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (HistoryProjectListBox.SelectedItem is ProjectHistory selectedHistory)
                {
                    if (!_historyService.IsProjectPathValid(selectedHistory.RootPath))
                    {
                        MessageBox.Show($"项目路径不存在: {selectedHistory.RootPath}", "路径错误",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    UpdateStatus($"正在打开项目: {selectedHistory.Name}", true);

                    // 更新历史记录
                    await _historyService.AddOrUpdateHistoryAsync(
                        selectedHistory.RootPath,
                        selectedHistory.Name,
                        selectedHistory.Type);

                    // 创建项目对象
                    _currentProject = new Project
                    {
                        Id = 1, // 临时ID
                        Name = selectedHistory.Name,
                        RootPath = selectedHistory.RootPath,
                        Type = selectedHistory.Type,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };

                    // 加载项目文件夹内容
                    LoadProjectFolder(selectedHistory.RootPath);

                    // 刷新历史列表
                    await LoadHistoryProjectsAsync();

                    UpdateStatus($"项目已打开: {selectedHistory.Name}", true);
                    _logger.LogInformation($"从历史记录打开项目: {selectedHistory.RootPath}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开历史项目失败");
                UpdateStatus("打开历史项目失败", false);
                MessageBox.Show($"打开项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 在资源管理器中打开历史项目
        /// </summary>
        private void OpenHistoryInExplorer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (HistoryProjectListBox.SelectedItem is ProjectHistory selectedHistory)
                {
                    if (_historyService.IsProjectPathValid(selectedHistory.RootPath))
                    {
                        System.Diagnostics.Process.Start("explorer.exe", selectedHistory.RootPath);
                        UpdateStatus($"已在资源管理器中打开: {selectedHistory.Name}", true);
                    }
                    else
                    {
                        MessageBox.Show($"项目路径不存在: {selectedHistory.RootPath}", "路径错误",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "在资源管理器中打开历史项目失败");
                MessageBox.Show($"打开失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从历史记录中移除
        /// </summary>
        private async void RemoveFromHistory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (HistoryProjectListBox.SelectedItem is ProjectHistory selectedHistory)
                {
                    var result = MessageBox.Show(
                        $"确定要从历史记录中移除项目 '{selectedHistory.Name}' 吗？",
                        "确认移除",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        await _historyService.RemoveHistoryAsync(selectedHistory.RootPath);
                        await LoadHistoryProjectsAsync();
                        UpdateStatus($"已从历史记录中移除: {selectedHistory.Name}", true);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从历史记录中移除项目失败");
                UpdateStatus("移除历史记录失败", false);
                MessageBox.Show($"移除失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

    }
}

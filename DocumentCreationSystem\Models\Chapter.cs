

namespace DocumentCreationSystem.Models;

/// <summary>
/// 章节实体类
/// </summary>
public class Chapter
{
    public int Id { get; set; }

    /// <summary>
    /// 所属小说项目ID
    /// </summary>
    public int NovelProjectId { get; set; }

    /// <summary>
    /// 关联的文档ID（可选，如果章节对应独立文档）
    /// </summary>
    public int? DocumentId { get; set; }

    /// <summary>
    /// 章节号
    /// </summary>
    public int ChapterNumber { get; set; }

    /// <summary>
    /// 章节标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 章节细纲
    /// </summary>
    public string? Outline { get; set; }

    /// <summary>
    /// 章节摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 章节内容
    /// </summary>
    public string? Content { get; set; }

    /// <summary>
    /// 章节内容文件路径（相对于项目根目录）
    /// </summary>
    public string? ContentPath { get; set; }

    /// <summary>
    /// 字数统计
    /// </summary>
    public int WordCount { get; set; } = 0;

    /// <summary>
    /// 目标字数
    /// </summary>
    public int TargetWordCount { get; set; } = 6500;

    /// <summary>
    /// 章节状态：Planning-规划中，Writing-创作中，Reviewing-审核中，Completed-已完成
    /// </summary>
    public string Status { get; set; } = "Planning";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 开始创作时间
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// 完成时间
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 上下文一致性检查记录（JSON格式）
    /// </summary>
    public string? ConsistencyCheckLog { get; set; }

    /// <summary>
    /// 创作进度百分比（0-100）
    /// </summary>
    public int Progress { get; set; } = 0;

    /// <summary>
    /// 是否自动创作
    /// </summary>
    public bool IsAutoGenerated { get; set; } = false;

    /// <summary>
    /// 排序顺序
    /// </summary>
    public int SortOrder { get; set; }


}

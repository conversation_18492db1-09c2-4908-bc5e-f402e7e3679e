using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// Agent学习引擎接口
    /// </summary>
    public interface IAgentLearningEngine
    {
        /// <summary>
        /// 从成功案例中学习
        /// </summary>
        Task LearnFromSuccessAsync(string agentId, string successCase);

        /// <summary>
        /// 从失败案例中学习
        /// </summary>
        Task LearnFromFailureAsync(string agentId, string failureCase);

        /// <summary>
        /// 获取学习统计
        /// </summary>
        Task<string> GetLearningStatsAsync(string agentId);
    }

    /// <summary>
    /// Agent学习引擎实现
    /// </summary>
    public class AgentLearningEngine : IAgentLearningEngine
    {
        private readonly ILogger<AgentLearningEngine> _logger;
        private readonly Dictionary<string, LearningStats> _learningStats = new();

        public AgentLearningEngine(ILogger<AgentLearningEngine> logger)
        {
            _logger = logger;
        }

        public async Task LearnFromSuccessAsync(string agentId, string successCase)
        {
            try
            {
                var stats = GetOrCreateStats(agentId);
                stats.SuccessCount++;
                stats.LastLearningTime = DateTime.Now;

                _logger.LogInformation($"Agent {agentId} 从成功案例中学习: {successCase}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"从成功案例学习失败: Agent {agentId}");
                throw;
            }
        }

        public async Task LearnFromFailureAsync(string agentId, string failureCase)
        {
            try
            {
                var stats = GetOrCreateStats(agentId);
                stats.FailureCount++;
                stats.LastLearningTime = DateTime.Now;

                _logger.LogInformation($"Agent {agentId} 从失败案例中学习: {failureCase}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"从失败案例学习失败: Agent {agentId}");
                throw;
            }
        }

        public async Task<string> GetLearningStatsAsync(string agentId)
        {
            try
            {
                var stats = GetOrCreateStats(agentId);
                return $"成功案例: {stats.SuccessCount}, 失败案例: {stats.FailureCount}, 最后学习时间: {stats.LastLearningTime}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取学习统计失败: Agent {agentId}");
                return "无法获取学习统计";
            }
        }

        private LearningStats GetOrCreateStats(string agentId)
        {
            if (!_learningStats.ContainsKey(agentId))
            {
                _learningStats[agentId] = new LearningStats
                {
                    AgentId = agentId,
                    SuccessCount = 0,
                    FailureCount = 0,
                    LastLearningTime = DateTime.Now
                };
            }
            return _learningStats[agentId];
        }
    }

    /// <summary>
    /// 学习统计
    /// </summary>
    public class LearningStats
    {
        public string AgentId { get; set; } = string.Empty;
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public DateTime LastLearningTime { get; set; }
    }
}

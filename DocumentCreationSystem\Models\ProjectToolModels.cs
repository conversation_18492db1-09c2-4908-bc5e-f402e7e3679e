using System.Text.Json.Serialization;

namespace DocumentCreationSystem.Models;

/// <summary>
/// 项目工具定义
/// </summary>
public class ProjectTool
{
    /// <summary>
    /// 工具ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 工具名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工具描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 工具类别
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 支持的项目类型
    /// </summary>
    public List<string> SupportedProjectTypes { get; set; } = new();

    /// <summary>
    /// 工具图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 是否为内置工具
    /// </summary>
    public bool IsBuiltIn { get; set; } = true;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 工具参数定义
    /// </summary>
    public List<ToolParameter> Parameters { get; set; } = new();

    /// <summary>
    /// 工具版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }
}

/// <summary>
/// 工具参数定义
/// </summary>
public class ToolParameter
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 可选值列表
    /// </summary>
    public List<string> Options { get; set; } = new();

    /// <summary>
    /// 验证规则
    /// </summary>
    public string ValidationRule { get; set; } = string.Empty;
}

/// <summary>
/// 工具执行结果
/// </summary>
public class ToolExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 结果数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 生成的文件列表
    /// </summary>
    public List<string> GeneratedFiles { get; set; } = new();

    /// <summary>
    /// 执行日志
    /// </summary>
    public List<string> Logs { get; set; } = new();
}

/// <summary>
/// 批量处理选项
/// </summary>
public class BatchProcessOptions
{
    /// <summary>
    /// 包含的文件扩展名
    /// </summary>
    public List<string> IncludeExtensions { get; set; } = new();

    /// <summary>
    /// 排除的文件扩展名
    /// </summary>
    public List<string> ExcludeExtensions { get; set; } = new();

    /// <summary>
    /// 是否包含子目录
    /// </summary>
    public bool IncludeSubdirectories { get; set; } = true;

    /// <summary>
    /// 最大文件大小（字节）
    /// </summary>
    public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100MB

    /// <summary>
    /// 并行处理数量
    /// </summary>
    public int ParallelCount { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// 是否创建备份
    /// </summary>
    public bool CreateBackup { get; set; } = true;

    /// <summary>
    /// 自定义过滤器
    /// </summary>
    public string? CustomFilter { get; set; }
}

/// <summary>
/// 批量处理结果
/// </summary>
public class BatchProcessResult
{
    /// <summary>
    /// 总文件数
    /// </summary>
    public int TotalFiles { get; set; }

    /// <summary>
    /// 成功处理的文件数
    /// </summary>
    public int SuccessCount { get; set; }

    /// <summary>
    /// 失败的文件数
    /// </summary>
    public int FailureCount { get; set; }

    /// <summary>
    /// 跳过的文件数
    /// </summary>
    public int SkippedCount { get; set; }

    /// <summary>
    /// 处理详情
    /// </summary>
    public List<FileProcessResult> ProcessResults { get; set; } = new();

    /// <summary>
    /// 总处理时间
    /// </summary>
    public TimeSpan TotalProcessTime { get; set; }

    /// <summary>
    /// 错误摘要
    /// </summary>
    public List<string> ErrorSummary { get; set; } = new();
}

/// <summary>
/// 文件处理结果
/// </summary>
public class FileProcessResult
{
    /// <summary>
    /// 文件路径
    /// </summary>
    public string FilePath { get; set; } = string.Empty;

    /// <summary>
    /// 处理状态
    /// </summary>
    public ProcessStatus Status { get; set; }

    /// <summary>
    /// 处理消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 处理时间
    /// </summary>
    public TimeSpan ProcessTime { get; set; }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string? OutputPath { get; set; }
}

/// <summary>
/// 创作上下文
/// </summary>
public class CreationContext
{
    /// <summary>
    /// 创作主题
    /// </summary>
    public string Theme { get; set; } = string.Empty;

    /// <summary>
    /// 创作风格
    /// </summary>
    public string Style { get; set; } = string.Empty;

    /// <summary>
    /// 目标长度
    /// </summary>
    public int TargetLength { get; set; }

    /// <summary>
    /// 参考内容
    /// </summary>
    public string? ReferenceContent { get; set; }

    /// <summary>
    /// 角色信息
    /// </summary>
    public List<CharacterInfo> Characters { get; set; } = new();

    /// <summary>
    /// 背景设定
    /// </summary>
    public string? BackgroundSetting { get; set; }

    /// <summary>
    /// 创作要求
    /// </summary>
    public List<string> Requirements { get; set; } = new();

    /// <summary>
    /// 自定义参数
    /// </summary>
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// 创作结果
/// </summary>
public class CreationResult
{
    /// <summary>
    /// 生成的内容
    /// </summary>
    public string Content { get; set; } = string.Empty;

    /// <summary>
    /// 内容类型
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// 质量评分
    /// </summary>
    public float QualityScore { get; set; }

    /// <summary>
    /// 创作统计
    /// </summary>
    public CreationStatistics Statistics { get; set; } = new();

    /// <summary>
    /// 建议改进
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// 使用的AI模型
    /// </summary>
    public string? UsedModel { get; set; }

    /// <summary>
    /// 创作时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 分析结果
/// </summary>
public class AnalysisResult
{
    /// <summary>
    /// 分析类型
    /// </summary>
    public string AnalysisType { get; set; } = string.Empty;

    /// <summary>
    /// 分析摘要
    /// </summary>
    public string Summary { get; set; } = string.Empty;

    /// <summary>
    /// 详细结果
    /// </summary>
    public Dictionary<string, object> Details { get; set; } = new();

    /// <summary>
    /// 评分
    /// </summary>
    public float Score { get; set; }

    /// <summary>
    /// 建议
    /// </summary>
    public List<string> Recommendations { get; set; } = new();

    /// <summary>
    /// 分析时间
    /// </summary>
    public DateTime AnalyzedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 转换选项
/// </summary>
public class ConversionOptions
{
    /// <summary>
    /// 保持格式
    /// </summary>
    public bool PreserveFormatting { get; set; } = true;

    /// <summary>
    /// 包含图片
    /// </summary>
    public bool IncludeImages { get; set; } = true;

    /// <summary>
    /// 压缩质量
    /// </summary>
    public int CompressionQuality { get; set; } = 85;

    /// <summary>
    /// 自定义样式
    /// </summary>
    public string? CustomStyle { get; set; }

    /// <summary>
    /// 输出目录
    /// </summary>
    public string? OutputDirectory { get; set; }
}

/// <summary>
/// 转换结果
/// </summary>
public class ConversionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;

    /// <summary>
    /// 原始文件大小
    /// </summary>
    public long OriginalSize { get; set; }

    /// <summary>
    /// 转换后文件大小
    /// </summary>
    public long ConvertedSize { get; set; }

    /// <summary>
    /// 转换时间
    /// </summary>
    public TimeSpan ConversionTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 质量检查结果
/// </summary>
public class QualityCheckResult
{
    /// <summary>
    /// 总体质量评分
    /// </summary>
    public float OverallScore { get; set; }

    /// <summary>
    /// 检查项目结果
    /// </summary>
    public Dictionary<string, QualityCheckItem> CheckItems { get; set; } = new();

    /// <summary>
    /// 发现的问题
    /// </summary>
    public List<QualityIssue> Issues { get; set; } = new();

    /// <summary>
    /// 改进建议
    /// </summary>
    public List<string> Suggestions { get; set; } = new();

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 质量检查项目
/// </summary>
public class QualityCheckItem
{
    /// <summary>
    /// 检查项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 评分
    /// </summary>
    public float Score { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    public string Details { get; set; } = string.Empty;
}

/// <summary>
/// 质量问题
/// </summary>
public class QualityIssue
{
    /// <summary>
    /// 问题类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 严重程度
    /// </summary>
    public string Severity { get; set; } = string.Empty;

    /// <summary>
    /// 问题描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 位置信息
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// 建议修复方案
    /// </summary>
    public string? SuggestedFix { get; set; }
}

/// <summary>
/// 导出选项
/// </summary>
public class ExportOptions
{
    /// <summary>
    /// 包含的内容类型
    /// </summary>
    public List<string> IncludeContentTypes { get; set; } = new();

    /// <summary>
    /// 是否包含元数据
    /// </summary>
    public bool IncludeMetadata { get; set; } = true;

    /// <summary>
    /// 是否包含附件
    /// </summary>
    public bool IncludeAttachments { get; set; } = true;

    /// <summary>
    /// 压缩级别
    /// </summary>
    public int CompressionLevel { get; set; } = 6;

    /// <summary>
    /// 输出目录
    /// </summary>
    public string? OutputDirectory { get; set; }

    /// <summary>
    /// 文件名模板
    /// </summary>
    public string? FileNameTemplate { get; set; }
}

/// <summary>
/// 导出结果
/// </summary>
public class ExportResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 导出文件路径
    /// </summary>
    public string ExportPath { get; set; } = string.Empty;

    /// <summary>
    /// 导出的文件数量
    /// </summary>
    public int FileCount { get; set; }

    /// <summary>
    /// 总文件大小
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// 导出时间
    /// </summary>
    public TimeSpan ExportTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 备份选项
/// </summary>
public class BackupOptions
{
    /// <summary>
    /// 备份类型
    /// </summary>
    public string BackupType { get; set; } = "Full"; // Full, Incremental, Differential

    /// <summary>
    /// 是否包含文件
    /// </summary>
    public bool IncludeFiles { get; set; } = true;

    /// <summary>
    /// 是否包含数据库
    /// </summary>
    public bool IncludeDatabase { get; set; } = true;

    /// <summary>
    /// 是否包含配置
    /// </summary>
    public bool IncludeConfiguration { get; set; } = true;

    /// <summary>
    /// 压缩级别
    /// </summary>
    public int CompressionLevel { get; set; } = 6;

    /// <summary>
    /// 备份目录
    /// </summary>
    public string? BackupDirectory { get; set; }

    /// <summary>
    /// 是否加密
    /// </summary>
    public bool Encrypt { get; set; } = false;

    /// <summary>
    /// 加密密码
    /// </summary>
    public string? EncryptionPassword { get; set; }
}

/// <summary>
/// 备份结果
/// </summary>
public class BackupResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 备份文件路径
    /// </summary>
    public string BackupPath { get; set; } = string.Empty;

    /// <summary>
    /// 备份大小
    /// </summary>
    public long BackupSize { get; set; }

    /// <summary>
    /// 备份时间
    /// </summary>
    public TimeSpan BackupTime { get; set; }

    /// <summary>
    /// 备份的文件数量
    /// </summary>
    public int FileCount { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 备份创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 恢复选项
/// </summary>
public class RestoreOptions
{
    /// <summary>
    /// 目标目录
    /// </summary>
    public string TargetDirectory { get; set; } = string.Empty;

    /// <summary>
    /// 是否覆盖现有文件
    /// </summary>
    public bool OverwriteExisting { get; set; } = false;

    /// <summary>
    /// 是否恢复数据库
    /// </summary>
    public bool RestoreDatabase { get; set; } = true;

    /// <summary>
    /// 是否恢复配置
    /// </summary>
    public bool RestoreConfiguration { get; set; } = true;

    /// <summary>
    /// 解密密码
    /// </summary>
    public string? DecryptionPassword { get; set; }

    /// <summary>
    /// 验证完整性
    /// </summary>
    public bool VerifyIntegrity { get; set; } = true;
}

/// <summary>
/// 恢复结果
/// </summary>
public class RestoreResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 恢复的文件数量
    /// </summary>
    public int RestoredFileCount { get; set; }

    /// <summary>
    /// 恢复时间
    /// </summary>
    public TimeSpan RestoreTime { get; set; }

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 恢复详情
    /// </summary>
    public List<string> RestoreDetails { get; set; } = new();

    /// <summary>
    /// 恢复完成时间
    /// </summary>
    public DateTime CompletedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 工具使用统计
/// </summary>
public class ToolUsageStatistics
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 统计期间
    /// </summary>
    public DateTimeRange Period { get; set; } = new();

    /// <summary>
    /// 工具使用次数统计
    /// </summary>
    public Dictionary<string, int> ToolUsageCounts { get; set; } = new();

    /// <summary>
    /// 工具执行时间统计
    /// </summary>
    public Dictionary<string, TimeSpan> ToolExecutionTimes { get; set; } = new();

    /// <summary>
    /// 最常用的工具
    /// </summary>
    public List<string> MostUsedTools { get; set; } = new();

    /// <summary>
    /// 总使用次数
    /// </summary>
    public int TotalUsageCount { get; set; }

    /// <summary>
    /// 总执行时间
    /// </summary>
    public TimeSpan TotalExecutionTime { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public float SuccessRate { get; set; }

    /// <summary>
    /// 统计生成时间
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 日期时间范围
/// </summary>
public class DateTimeRange
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 开始时间（别名，兼容性）
    /// </summary>
    public DateTime Start => StartTime;

    /// <summary>
    /// 结束时间（别名，兼容性）
    /// </summary>
    public DateTime End => EndTime;

    /// <summary>
    /// 时间跨度
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;
}

/// <summary>
/// 创作统计信息
/// </summary>
public class CreationStatistics
{
    /// <summary>
    /// 总字数
    /// </summary>
    public int TotalWords { get; set; }

    /// <summary>
    /// 总段落数
    /// </summary>
    public int TotalParagraphs { get; set; }

    /// <summary>
    /// 总句子数
    /// </summary>
    public int TotalSentences { get; set; }

    /// <summary>
    /// 总写作时间
    /// </summary>
    public TimeSpan TotalWritingTime { get; set; }

    /// <summary>
    /// 平均写作速度（字/分钟）
    /// </summary>
    public float AverageWritingSpeed { get; set; }

    /// <summary>
    /// 创作开始时间
    /// </summary>
    public DateTime StartTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 创作结束时间
    /// </summary>
    public DateTime EndTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 修改次数
    /// </summary>
    public int RevisionCount { get; set; }

    /// <summary>
    /// 使用的AI提示次数
    /// </summary>
    public int AIPromptCount { get; set; }
}

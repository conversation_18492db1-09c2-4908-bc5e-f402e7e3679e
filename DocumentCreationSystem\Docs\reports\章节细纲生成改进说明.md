# 章节细纲生成改进说明

## 问题描述

在分布执行一键写书过程中，章节细纲的剧情出现重复，没有很好地按照全书大纲设定、世界设定、分卷设定去生成章节细纲。

## 问题分析

### 原有问题
1. **世界设定利用不足**：章节细纲生成时只使用了基本的书籍信息（标题、创作方向、卷宗大纲、全书大纲），没有充分利用项目中的23个世界设定文件
2. **前文参考缺失**：没有参考前几章的细纲内容，导致剧情重复
3. **提示词不够详细**：AI提示词缺乏对世界观一致性的强调
4. **更新逻辑简单**：章节细纲更新时没有考虑世界设定的约束

### 世界设定文件列表
项目中包含以下23个世界设定管理文件：
- 世界观设定管理.md
- 维度结构管理.md  
- 地图结构管理.md
- 秘境管理.md
- 时间线管理.md
- 剧情大纲设定管理.md
- 角色设定管理.md
- 关系网络.md
- 种族类别管理.md
- 修炼体系设定管理.md
- 功法体系管理.md
- 武器管理.md
- 灵宝体系管理.md
- 装备体系管理.md
- 势力管理.md
- 政治体系管理.md
- 司法体系管理.md
- 商业体系管理.md
- 职业体系管理.md
- 货币体系管理.md
- 资源管理.md
- 宠物体系管理.md
- 生民体系管理.md

## 改进方案

### 1. 增强章节细纲生成逻辑

#### 改进的 `GenerateChapterOutlineAsync` 方法
```csharp
private async Task<string> GenerateChapterOutlineAsync(StepExecutionState state, int chapterNumber, VolumeOutline volume)
{
    // 获取项目路径
    var projectPath = await GetProjectPathAsync(state.NovelProjectId);
    
    // 读取相关的世界设定文件
    var worldSettings = await ReadRelevantWorldSettingsAsync(projectPath);
    
    // 获取前几章的细纲作为上下文
    var previousOutlines = GetPreviousChapterOutlines(state, chapterNumber, 3);
    
    // 构建增强的提示词
    var prompt = BuildEnhancedChapterOutlinePrompt(state, chapterNumber, volume, worldSettings, previousOutlines);

    return await _aiService.GenerateTextAsync(prompt, 3000, 0.8f);
}
```

#### 新增辅助方法

1. **`ReadRelevantWorldSettingsAsync`**：读取项目中的世界设定文件
   - 自动扫描23个世界设定文件
   - 过滤空文件和无效内容
   - 限制每个文件内容长度避免提示词过长

2. **`GetPreviousChapterOutlines`**：获取前几章细纲作为参考
   - 获取前3章的细纲内容
   - 提取章节标题和主要内容
   - 避免剧情重复

3. **`GetProjectPathAsync`**：获取项目路径
   - 支持自定义项目路径
   - 提供默认路径fallback

### 2. 增强的提示词结构

新的提示词包含以下部分：

```
=== 基本信息 ===
书籍标题、创作方向、章节号、所属卷宗、目标字数

=== 全书大纲 ===
完整的全书大纲内容

=== 当前卷宗大纲 ===
当前卷宗的详细设定

=== 相关世界设定 ===
从23个世界设定文件中读取的相关内容

=== 前几章细纲参考 ===
前3章的细纲内容，避免剧情重复

=== 章节细纲生成要求 ===
详细的生成要求，强调：
1. 严格遵循全书大纲的主线发展
2. 符合当前卷宗的具体设定和进度
3. 充分利用世界观设定中的元素
4. 与前几章的剧情发展保持连贯性
5. 避免重复前几章的剧情内容
6. 为后续章节的发展做好铺垫
```

### 3. 改进章节细纲更新逻辑

#### 增强的 `UpdateChapterOutlineAfterContentAsync` 方法
- 在更新下一章细纲时也考虑世界设定
- 确保更新后的细纲与世界观保持一致
- 避免因前文变化导致的设定冲突

### 4. 细纲格式优化

新的细纲格式包含更多世界观相关信息：

```
## 第X章：[章节标题]

### 场景设定
- 时间：[具体时间，参考时间线设定]
- 地点：[详细地点描述，参考地图和世界观设定]
- 环境：[环境氛围和背景，结合世界观特色]
- 相关势力：[涉及的势力或组织]

### 人物安排
- 主要人物：[本章出场的主要角色及其状态，参考角色设定]
- 次要人物：[配角和群众角色]
- 人物关系：[角色间的关系变化，参考关系网络]
- 人物心理：[关键角色的心理状态和动机]

### 主线发展
- 核心事件：[本章的核心情节，严格按照大纲推进]
- 爽点设计：[读者期待的精彩内容，结合修炼体系等设定]
- 冲突点：[矛盾和冲突的爆发，涉及的势力斗争等]
- 悬念设置：[留给读者的疑问和期待]
- 世界观展现：[本章展现的世界观元素]

### 预期结果
- 情节推进：[对整体故事的推进作用，与大纲的对应关系]
- 角色成长：[角色的变化和成长，修炼进展等]
- 伏笔铺设：[为后续章节埋下的伏笔]
- 章节结尾：[本章的结束方式和过渡]
```

## 预期效果

### 1. 剧情一致性提升
- 章节细纲严格按照全书大纲和世界设定生成
- 避免与已有设定冲突的情节
- 确保世界观的连贯性

### 2. 剧情重复减少
- 通过参考前几章细纲避免重复剧情
- AI明确知道已经发生的事件
- 确保故事推进的连续性

### 3. 世界观丰富度提升
- 充分利用23个世界设定文件的内容
- 在章节中自然融入世界观元素
- 提升小说的深度和可读性

### 4. 创作质量提升
- 更详细的细纲指导正文创作
- 减少创作过程中的设定错误
- 提高整体创作效率

## 使用说明

1. **确保世界设定文件完整**：在项目文件夹中创建相关的世界设定文件
2. **定期更新世界设定**：随着故事发展更新相关设定文件
3. **检查细纲质量**：生成细纲后检查是否符合预期
4. **适当调整参数**：根据需要调整AI生成参数

## 技术实现

### 文件修改
- `DocumentCreationSystem/Services/StepByStepWritingService.cs`：主要改进文件
- 新增了世界设定读取和前文参考功能
- 优化了提示词构建逻辑

### 测试覆盖
- `DocumentCreationSystem/Tests/StepByStepWritingServiceTests.cs`：新增测试文件
- 验证世界设定集成功能
- 测试前文参考逻辑

这些改进将显著提升章节细纲生成的质量，确保剧情的一致性和丰富性。

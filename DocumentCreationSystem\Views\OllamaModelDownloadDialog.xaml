<Window x:Class="DocumentCreationSystem.Views.OllamaModelDownloadDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Ollama模型下载" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#004578"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter Property="Background" Value="#CCCCCC"/>
                                <Setter Property="Foreground" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#A71E2A"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="Ollama模型下载" 
                   FontSize="18" FontWeight="Bold" 
                   Margin="0,0,0,20" HorizontalAlignment="Center"/>

        <!-- 服务器配置 -->
        <GroupBox Grid.Row="1" Header="服务器配置" Margin="0,0,0,15">
            <Grid Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="Ollama地址:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Grid.Column="1" x:Name="OllamaUrlTextBox" 
                         Text="http://localhost:11434" 
                         VerticalAlignment="Center" Padding="8,5"/>
                <Button Grid.Column="2" Content="测试连接" 
                        Style="{StaticResource ModernButtonStyle}"
                        Click="TestConnection_Click" x:Name="TestConnectionButton"/>
            </Grid>
        </GroupBox>

        <!-- 模型选择 -->
        <GroupBox Grid.Row="2" Header="模型选择" Margin="0,0,0,15">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 预设模型 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="常用模型:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <ComboBox x:Name="PresetModelsComboBox" Width="200" 
                              SelectedIndex="0" VerticalAlignment="Center">
                        <ComboBoxItem Content="bge-m3 (向量模型)"/>
                        <ComboBoxItem Content="llama3.2:3b"/>
                        <ComboBoxItem Content="llama3.2:1b"/>
                        <ComboBoxItem Content="qwen2.5:7b"/>
                        <ComboBoxItem Content="qwen2.5:3b"/>
                        <ComboBoxItem Content="qwen2.5:1.5b"/>
                        <ComboBoxItem Content="gemma2:2b"/>
                        <ComboBoxItem Content="phi3:mini"/>
                        <ComboBoxItem Content="自定义..."/>
                    </ComboBox>
                </StackPanel>
                
                <!-- 自定义模型 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal">
                    <TextBlock Text="自定义模型:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox x:Name="CustomModelTextBox" Width="200" 
                             VerticalAlignment="Center" Padding="8,5"
                             IsEnabled="False"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- 下载进度 -->
        <GroupBox Grid.Row="3" Header="下载进度">
            <ScrollViewer Margin="10" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- 当前状态 -->
                    <TextBlock x:Name="StatusTextBlock" 
                               Text="准备就绪" 
                               FontWeight="Bold" 
                               Margin="0,0,0,10"/>
                    
                    <!-- 进度条 -->
                    <ProgressBar x:Name="DownloadProgressBar" 
                                 Height="25" 
                                 Margin="0,0,0,10"
                                 Visibility="Collapsed"/>
                    
                    <!-- 详细进度文本 -->
                    <TextBlock x:Name="ProgressTextBlock" 
                               Text="" 
                               Margin="0,0,0,10"
                               TextWrapping="Wrap"/>
                    
                    <!-- 日志输出 -->
                    <TextBox x:Name="LogTextBox" 
                             Height="150" 
                             IsReadOnly="True" 
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"
                             FontFamily="Consolas"
                             FontSize="11"
                             Background="#F8F8F8"
                             Padding="5"/>
                </StackPanel>
            </ScrollViewer>
        </GroupBox>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button x:Name="DownloadButton" Content="开始下载" 
                    Style="{StaticResource ModernButtonStyle}"
                    Click="Download_Click"/>
            <Button x:Name="CancelButton" Content="取消" 
                    Style="{StaticResource CancelButtonStyle}"
                    Click="Cancel_Click"/>
            <Button x:Name="CloseButton" Content="关闭" 
                    Style="{StaticResource ModernButtonStyle}"
                    Click="Close_Click" 
                    Visibility="Collapsed"/>
        </StackPanel>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="5" Margin="0,10,0,0">
            <StatusBarItem>
                <TextBlock x:Name="StatusBarText" Text="就绪"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>

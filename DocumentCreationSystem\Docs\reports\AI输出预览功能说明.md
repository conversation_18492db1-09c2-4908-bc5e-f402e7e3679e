# AI输出预览功能说明

## 功能概述

为一键写书界面配置了AI输出内容预览窗口，采用最节省资源的方案，直接在主GUI界面的右侧AI工具面板中集成预览功能。

## 主要特性

### 1. 预览区域设计
- **位置**: 右侧AI工具面板底部，可折叠显示
- **布局**: 采用Grid分割，不影响原有功能区域
- **资源优化**: 复用现有界面组件，避免创建新窗口

### 2. 预览功能
- **实时预览**: AI生成内容后立即显示在预览区域
- **内容信息**: 显示章节标题、内容类型、字数统计、生成时间
- **操作按钮**: 
  - 应用到编辑器：将预览内容添加到文档编辑器
  - 复制内容：复制到剪贴板
  - 保存到文件：保存为.txt或.docx文件
  - 关闭预览：隐藏预览区域

### 3. 支持的AI功能
- **一键写书**: 章节创作过程中实时预览生成的章节内容
- **文本润色**: 预览润色结果，用户可选择是否应用
- **内容扩写**: 预览扩写结果，避免直接替换原文
- **章节创作**: 预览生成的章节内容
- **续写功能**: 预览续写内容，用户确认后再插入

## 技术实现

### 1. 事件驱动架构
```csharp
// AI输出事件参数
public class AIOutputEventArgs : EventArgs
{
    public string Content { get; set; }           // AI生成的内容
    public string ChapterTitle { get; set; }      // 章节标题
    public string ContentType { get; set; }       // 内容类型
    public int WordCount { get; set; }            // 字数统计
    public DateTime GeneratedTime { get; set; }   // 生成时间
}

// 事件定义
public event EventHandler<AIOutputEventArgs>? AIOutputGenerated;
```

### 2. 界面组件
- **预览区域**: 可折叠的Card组件，包含标题栏、内容区域、状态栏
- **分隔符**: GridSplitter支持用户调整预览区域大小
- **操作按钮**: Material Design风格的图标按钮

### 3. 资源管理
- **内存优化**: 预览区域按需显示/隐藏，不占用额外内存
- **UI线程**: 使用Dispatcher.Invoke确保UI更新在正确线程
- **事件管理**: 自动订阅/取消订阅事件，避免内存泄漏

## 使用方法

### 1. 一键写书预览
1. 打开项目，点击"一键写书"
2. 配置写书参数，开始创作
3. 每个章节创作完成后，内容会自动显示在预览区域
4. 用户可以查看内容，选择保存或应用到编辑器

### 2. 快速操作预览
1. 在文档编辑器中选择文本
2. 点击"润色选中文本"或"扩写内容"
3. AI处理完成后，结果显示在预览区域
4. 用户确认后点击"应用到编辑器"

### 3. 章节创作预览
1. 点击"创作章节"按钮
2. AI生成的章节内容显示在预览区域
3. 用户可以预览、编辑或保存内容

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    主窗口标题栏                              │
├─────────────────────────────────────────────────────────────┤
│                    工具栏                                   │
├─────────┬─────────────────────────┬─────────────────────────┤
│ 项目导航 │      文档编辑器          │      AI工具面板         │
│         │                        │ ┌─────────────────────┐ │
│         │                        │ │    快速操作按钮      │ │
│         │                        │ │    小说创作工具      │ │
│         │                        │ │    AI参数设置       │ │
│         │                        │ ├─────────────────────┤ │
│         │                        │ │ ═══ 预览分隔符 ═══  │ │
│         │                        │ ├─────────────────────┤ │
│         │                        │ │   AI输出预览区域     │ │
│         │                        │ │ [应用][复制][保存]   │ │
│         │                        │ └─────────────────────┘ │
└─────────┴─────────────────────────┴─────────────────────────┘
│                    状态栏                                   │
└─────────────────────────────────────────────────────────────┘
```

## 优势特点

1. **节省资源**: 不创建新窗口，复用现有界面组件
2. **用户友好**: 预览后确认，避免意外覆盖内容
3. **功能完整**: 支持查看、复制、保存、应用等操作
4. **响应式设计**: 可折叠显示，不影响主要工作区域
5. **实时更新**: AI生成内容后立即显示，提供即时反馈

## 扩展性

该预览系统设计具有良好的扩展性：
- 可以轻松添加新的AI功能预览支持
- 支持不同类型的内容预览（文本、图片、表格等）
- 可以添加更多操作按钮（如编辑、格式化等）
- 支持预览历史记录功能

## 注意事项

1. 预览区域默认隐藏，只在有内容时显示
2. 关闭预览区域会清空当前预览内容
3. 保存文件时会根据项目路径自动设置默认目录
4. 应用到编辑器会在当前内容末尾追加，不会覆盖现有内容

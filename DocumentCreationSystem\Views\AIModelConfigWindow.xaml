<Window x:Class="DocumentCreationSystem.Views.AIModelConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="AI模型配置" Height="650" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <materialDesign:PackIcon Kind="Settings" Height="24" Width="24"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="AI模型配置" VerticalAlignment="Center"
                         FontSize="18" FontWeight="Medium"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button x:Name="TestConnectionButton" Content="测试连接"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0" Click="TestConnection_Click"
                            ToolTip="测试当前配置的连接，成功后将自动保存配置"/>
                    <Button x:Name="RefreshModelsButton" Content="刷新模型"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="RefreshModels_Click"
                            ToolTip="重新检测可用的AI模型"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容 -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}" Margin="16">
            <!-- AI模型配置标签页 -->
            <TabItem Header="AI模型配置">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Brain" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="AI模型配置" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">

                <!-- 使用说明 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16"
                                   Background="{DynamicResource MaterialDesignCardBackground}">
                    <StackPanel Margin="16">
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <materialDesign:PackIcon Kind="Information" VerticalAlignment="Center"
                                                   Foreground="{DynamicResource MaterialDesignBody}" Margin="0,0,8,0"/>
                            <TextBlock Text="配置说明" FontWeight="Medium" FontSize="14"/>
                        </StackPanel>
                        <TextBlock TextWrapping="Wrap" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Text="1. 选择您要使用的AI平台&#x0A;2. 填写相应的配置信息&#x0A;3. 点击&quot;测试连接&quot;验证配置&#x0A;4. 测试成功后配置将自动保存并应用"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 平台选择 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="AI平台选择" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>

                        <!-- 使用WrapPanel来自动换行，确保所有平台都能显示 -->
                        <WrapPanel Orientation="Horizontal">
                            <RadioButton x:Name="OllamaRadio" Content="Ollama (本地)"
                                       GroupName="AIPlatform" IsChecked="True"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="本地运行的开源AI模型平台，支持多种开源模型"/>
                            <RadioButton x:Name="LMStudioRadio" Content="LM Studio"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="本地AI模型运行环境，提供OpenAI兼容的API接口"/>
                            <RadioButton x:Name="ZhipuAIRadio" Content="智谱AI"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="智谱AI云端服务，提供GLM系列模型，支持文本、图像、视频生成"/>
                            <RadioButton x:Name="DeepSeekRadio" Content="DeepSeek"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="DeepSeek云端服务，提供高质量的对话和代码生成模型"/>
                            <RadioButton x:Name="OpenAIRadio" Content="OpenAI 自定义"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="OpenAI官方API或兼容接口，支持GPT系列模型"/>
                            <RadioButton x:Name="AlibabaRadio" Content="阿里模型"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="阿里云通义千问系列模型，支持文本、图像、音频处理"/>
                            <RadioButton x:Name="RWKVRadio" Content="RWKV"
                                       GroupName="AIPlatform"
                                       Margin="8,4" MinWidth="120" Checked="Platform_Checked"
                                       ToolTip="RWKV开源大语言模型，支持本地部署和API调用"/>
                        </WrapPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 平台配置区域 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <DockPanel Margin="0,0,0,16">
                            <TextBlock x:Name="PlatformTitle" Text="Ollama 配置" FontWeight="Medium" FontSize="16"
                                     DockPanel.Dock="Left" VerticalAlignment="Center"/>
                            <TextBlock x:Name="PlatformDescription" Text="本地开源AI模型平台，免费使用，需要本地安装"
                                     FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     DockPanel.Dock="Right" VerticalAlignment="Center" TextAlignment="Right"/>
                        </DockPanel>
                        
                        <!-- Ollama 配置 -->
                        <StackPanel x:Name="OllamaConfig" Visibility="Visible">
                            <TextBox x:Name="OllamaUrlTextBox"
                                   materialDesign:HintAssist.Hint="Ollama服务地址"
                                   Text="http://localhost:11434"
                                   Margin="0,0,0,16"
                                   ToolTip="Ollama服务的地址，默认为本地11434端口。确保Ollama服务已启动。"/>

                            <DockPanel Margin="0,0,0,16">
                                <TextBlock Text="可用模型:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                    <Button x:Name="DownloadOllamaModelButton" Content="下载模型"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          Margin="0,0,8,0"
                                          Click="DownloadOllamaModel_Click"
                                          ToolTip="打开模型下载工具，支持进度显示和多种预设模型"/>
                                    <Button x:Name="DetectOllamaModelsButton" Content="检测模型"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          Click="DetectModels_Click"
                                          ToolTip="自动检测Ollama中已安装的模型"/>
                                </StackPanel>
                            </DockPanel>

                            <ComboBox x:Name="OllamaModelComboBox"
                                    materialDesign:HintAssist.Hint="选择模型"
                                    DisplayMemberPath="Name"
                                    SelectedValuePath="Id"
                                    ToolTip="选择要使用的Ollama模型。如果列表为空，请先点击'检测模型'按钮。"/>

                            <!-- 模型管理区域 -->
                            <Expander Header="模型管理" Margin="0,16,0,0">
                                <StackPanel Margin="0,8,0,0">
                                    <TextBlock Text="常用模型快速下载:" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <UniformGrid Columns="2" Margin="0,0,0,8">
                                        <Button Content="下载 Qwen2.5:7b" Margin="0,0,4,4"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="qwen2.5:7b"
                                              ToolTip="下载通义千问2.5 7B模型，适合中文对话和创作"/>
                                        <Button Content="下载 Llama3.2:3b" Margin="4,0,0,4"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="llama3.2:3b"
                                              ToolTip="下载Llama3.2 3B模型，轻量级英文模型"/>
                                        <Button Content="下载 Gemma2:9b" Margin="0,4,4,0"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="gemma2:9b"
                                              ToolTip="下载Gemma2 9B模型，Google开源模型"/>
                                        <Button Content="下载 CodeLlama:7b" Margin="4,4,0,0"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              Click="DownloadModel_Click" Tag="codellama:7b"
                                              ToolTip="下载CodeLlama 7B模型，专门用于代码生成"/>
                                    </UniformGrid>

                                    <DockPanel Margin="0,8,0,0">
                                        <TextBox x:Name="CustomModelTextBox"
                                               materialDesign:HintAssist.Hint="自定义模型名称 (如: llama3.1:8b)"
                                               DockPanel.Dock="Left" Margin="0,0,8,0"/>
                                        <Button Content="下载" DockPanel.Dock="Right"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              Click="DownloadCustomModel_Click"
                                              ToolTip="下载自定义模型"/>
                                    </DockPanel>

                                    <TextBlock x:Name="DownloadStatusText" Text=""
                                             Margin="0,8,0,0" FontSize="12"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                                    <ProgressBar x:Name="DownloadProgressBar"
                                               Margin="0,4,0,0" Height="4"
                                               Visibility="Collapsed"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>

                        <!-- LM Studio 配置 -->
                        <StackPanel x:Name="LMStudioConfig" Visibility="Collapsed">
                            <TextBox x:Name="LMStudioUrlTextBox"
                                   materialDesign:HintAssist.Hint="LM Studio服务地址"
                                   Text="http://localhost:1234"
                                   Margin="0,0,0,16"
                                   ToolTip="LM Studio服务的地址，默认为本地1234端口。确保LM Studio已启动并加载了模型。"/>

                            <DockPanel Margin="0,0,0,16">
                                <TextBlock Text="可用模型:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <Button x:Name="DetectLMStudioModelsButton" Content="检测模型"
                                      Style="{StaticResource MaterialDesignFlatButton}"
                                      DockPanel.Dock="Right"
                                      Click="DetectModels_Click"
                                      ToolTip="自动检测LM Studio中已加载的模型"/>
                            </DockPanel>

                            <ComboBox x:Name="LMStudioModelComboBox"
                                    materialDesign:HintAssist.Hint="选择模型"
                                    DisplayMemberPath="Name"
                                    SelectedValuePath="Id"
                                    ToolTip="选择要使用的LM Studio模型。如果列表为空，请确保LM Studio已加载模型并点击'检测模型'。"/>
                        </StackPanel>

                        <!-- 智谱AI 配置 -->
                        <StackPanel x:Name="ZhipuAIConfig" Visibility="Collapsed">
                            <TextBox x:Name="ZhipuAPIKeyTextBox"
                                   materialDesign:HintAssist.Hint="API Key"
                                   Margin="0,0,0,16"
                                   ToolTip="智谱AI的API密钥，可在智谱AI开放平台获取。格式通常以'sk-'开头。"/>

                            <ComboBox x:Name="ZhipuModelComboBox"
                                    materialDesign:HintAssist.Hint="选择或输入模型名称"
                                    Margin="0,0,0,16"
                                    IsEditable="True"
                                    ToolTip="选择预设模型或输入自定义模型名称。输入新模型名称后会自动保存到下拉列表中。">
                                <ComboBoxItem Content="GLM-4-Flash-250414" IsSelected="True" ToolTip="对话模型 - 主要用于文本创作"/>
                                <ComboBoxItem Content="GLM-4.1V-Thinking" ToolTip="视觉推理模型 - 支持图像理解和深度推理"/>
                                <ComboBoxItem Content="GLM-4.1V-Thinking-Flash" ToolTip="视觉推理模型(快速版) - 支持图像理解和推理"/>
                                <ComboBoxItem Content="GLM-4V-Flash" ToolTip="图像理解模型 - 专门用于图像分析"/>
                                <ComboBoxItem Content="GLM-Z1-Flash" ToolTip="推理模型 - 适合复杂逻辑推理"/>
                                <ComboBoxItem Content="Cogview-3-Flash" ToolTip="图像生成模型 - 用于生成图像"/>
                                <ComboBoxItem Content="CogVideoX-Flash" ToolTip="视频生成模型 - 用于生成视频"/>
                            </ComboBox>

                            <TextBox x:Name="ZhipuBaseUrlTextBox"
                                   materialDesign:HintAssist.Hint="Base URL (可选)"
                                   Text="https://open.bigmodel.cn/api/paas/v4"
                                   ToolTip="智谱AI的API基础地址，通常不需要修改。"/>
                        </StackPanel>

                        <!-- DeepSeek 配置 -->
                        <StackPanel x:Name="DeepSeekConfig" Visibility="Collapsed">
                            <TextBox x:Name="DeepSeekAPIKeyTextBox"
                                   materialDesign:HintAssist.Hint="API Key"
                                   Margin="0,0,0,16"
                                   ToolTip="DeepSeek的API密钥，可在DeepSeek开放平台获取。"/>

                            <ComboBox x:Name="DeepSeekModelComboBox"
                                    materialDesign:HintAssist.Hint="选择或输入模型名称"
                                    Margin="0,0,0,16"
                                    IsEditable="True"
                                    ToolTip="选择预设模型或输入自定义模型名称。输入新模型名称后会自动保存到下拉列表中。">
                                <ComboBoxItem Content="deepseek-chat" IsSelected="True" ToolTip="主要对话模型 - 适合文本创作和对话"/>
                                <ComboBoxItem Content="deepseek-coder" ToolTip="代码生成模型 - 专门用于编程"/>
                                <ComboBoxItem Content="deepseek-reasoner" ToolTip="推理模型 - 适合复杂逻辑推理"/>
                            </ComboBox>

                            <TextBox x:Name="DeepSeekBaseUrlTextBox"
                                   materialDesign:HintAssist.Hint="Base URL (可选)"
                                   Text="https://api.deepseek.com"
                                   ToolTip="DeepSeek的API基础地址，通常不需要修改。"/>
                        </StackPanel>

                        <!-- OpenAI 配置 -->
                        <StackPanel x:Name="OpenAIConfig" Visibility="Collapsed">
                            <TextBox x:Name="OpenAIAPIKeyTextBox"
                                   materialDesign:HintAssist.Hint="API Key (可选，本地模型无需填写)"
                                   Margin="0,0,0,16"
                                   ToolTip="OpenAI的API密钥，可在OpenAI平台获取。本地部署的模型可以留空。"/>

                            <ComboBox x:Name="OpenAIModelComboBox"
                                    materialDesign:HintAssist.Hint="选择或输入模型名称"
                                    Margin="0,0,0,16"
                                    IsEditable="True"
                                    ToolTip="选择预设模型或输入自定义模型名称。支持OpenAI官方模型和本地部署的兼容模型。">
                                <ComboBoxItem Content="gpt-3.5-turbo" IsSelected="True" ToolTip="GPT-3.5 Turbo - 快速响应，成本较低"/>
                                <ComboBoxItem Content="gpt-4" ToolTip="GPT-4 - 高质量推理和创作"/>
                                <ComboBoxItem Content="gpt-4-turbo" ToolTip="GPT-4 Turbo - 更快的GPT-4版本"/>
                                <ComboBoxItem Content="gpt-4o" ToolTip="GPT-4o - 多模态模型"/>
                                <ComboBoxItem Content="gpt-4o-mini" ToolTip="GPT-4o Mini - 轻量级多模态模型"/>
                                <ComboBoxItem Content="o1-preview" ToolTip="O1 Preview - 高级推理模型"/>
                                <ComboBoxItem Content="o1-mini" ToolTip="O1 Mini - 轻量级推理模型"/>
                                <ComboBoxItem Content="Qwen3-30B-A3B" ToolTip="通义千问3-30B-A3B - 本地部署模型"/>
                                <ComboBoxItem Content="Qwen3-32B" ToolTip="通义千问3-32B - 本地部署模型"/>
                                <ComboBoxItem Content="QwQ-32B" ToolTip="QwQ-32B - 本地部署模型"/>
                                <ComboBoxItem Content="QwQ-32B-AWQ" ToolTip="QwQ-32B-AWQ - 量化版本本地部署模型"/>
                            </ComboBox>

                            <TextBox x:Name="OpenAIBaseUrlTextBox"
                                   materialDesign:HintAssist.Hint="Base URL (可选)"
                                   Text="https://api.openai.com/v1"
                                   ToolTip="OpenAI的API基础地址，支持自定义兼容接口。"/>
                        </StackPanel>

                        <!-- 阿里模型 配置 -->
                        <StackPanel x:Name="AlibabaConfig" Visibility="Collapsed">
                            <TextBox x:Name="AlibabaAPIKeyTextBox"
                                   materialDesign:HintAssist.Hint="API Key"
                                   Margin="0,0,0,16"
                                   ToolTip="阿里云的API密钥，可在阿里云控制台获取。"/>

                            <ComboBox x:Name="AlibabaModelComboBox"
                                    materialDesign:HintAssist.Hint="选择或输入模型名称"
                                    Margin="0,0,0,16"
                                    IsEditable="True"
                                    ToolTip="选择预设模型或输入自定义模型名称。输入新模型名称后会自动保存到下拉列表中。">
                                <ComboBoxItem Content="qwen-turbo" IsSelected="True" ToolTip="通义千问Turbo - 快速响应"/>
                                <ComboBoxItem Content="qwen-plus" ToolTip="通义千问Plus - 平衡性能和成本"/>
                                <ComboBoxItem Content="qwen-max" ToolTip="通义千问Max - 最高性能"/>
                                <ComboBoxItem Content="qwen-max-longcontext" ToolTip="通义千问Max长文本 - 支持长文本处理"/>
                                <ComboBoxItem Content="qwen-vl-plus" ToolTip="通义千问视觉Plus - 图像理解"/>
                                <ComboBoxItem Content="qwen-vl-max" ToolTip="通义千问视觉Max - 高级图像理解"/>
                                <ComboBoxItem Content="qwen-audio-turbo" ToolTip="通义千问音频Turbo - 音频处理"/>
                                <ComboBoxItem Content="qwen-coder-turbo" ToolTip="通义千问代码Turbo - 代码生成"/>
                            </ComboBox>

                            <TextBox x:Name="AlibabaBaseUrlTextBox"
                                   materialDesign:HintAssist.Hint="Base URL (可选)"
                                   Text="https://dashscope.aliyuncs.com/api/v1"
                                   ToolTip="阿里云的API基础地址，通常不需要修改。"/>
                        </StackPanel>

                        <!-- RWKV 配置 -->
                        <StackPanel x:Name="RWKVConfig" Visibility="Collapsed">
                            <TextBox x:Name="RWKVBaseUrlTextBox"
                                   materialDesign:HintAssist.Hint="RWKV API地址"
                                   Text="http://localhost:8000"
                                   Margin="0,0,0,16"
                                   ToolTip="RWKV API服务地址"/>

                            <TextBox x:Name="RWKVApiKeyTextBox"
                                   materialDesign:HintAssist.Hint="API Key (可选)"
                                   Margin="0,0,0,16"
                                   ToolTip="API调用时的密钥（可选）"/>



                            <ComboBox x:Name="RWKVModelComboBox"
                                    materialDesign:HintAssist.Hint="选择或输入模型名称"
                                    Margin="0,0,0,16"
                                    IsEditable="True"
                                    ToolTip="选择预设模型或输入自定义模型名称。输入新模型名称后会自动保存到下拉列表中。">
                                <ComboBoxItem Content="RWKV-v7-2.9B-G1-GGUF" ToolTip="RWKV-v7 2.9B G1 GGUF - 最新版本，支持本地GPU推理"/>
                                <ComboBoxItem Content="RWKV-6-World-1B6" ToolTip="RWKV-6 World 1.6B - 轻量级模型"/>
                                <ComboBoxItem Content="RWKV-6-World-3B" ToolTip="RWKV-6 World 3B - 平衡性能"/>
                                <ComboBoxItem Content="RWKV-6-World-7B" ToolTip="RWKV-6 World 7B - 高性能模型"/>
                                <ComboBoxItem Content="RWKV-6-World-14B" ToolTip="RWKV-6 World 14B - 大型模型"/>
                                <ComboBoxItem Content="RWKV-5-World-1B5" ToolTip="RWKV-5 World 1.5B - 经典轻量级"/>
                                <ComboBoxItem Content="RWKV-5-World-3B" ToolTip="RWKV-5 World 3B - 经典中型"/>
                                <ComboBoxItem Content="RWKV-4-Raven-1B5" ToolTip="RWKV-4 Raven 1.5B - 对话优化"/>
                                <ComboBoxItem Content="RWKV-4-Raven-3B" ToolTip="RWKV-4 Raven 3B - 对话优化"/>
                                <ComboBoxItem Content="RWKV-4-Raven-7B" ToolTip="RWKV-4 Raven 7B - 高质量对话"/>
                                <ComboBoxItem Content="RWKV-4-Raven-14B" ToolTip="RWKV-4 Raven 14B - 顶级对话"/>
                            </ComboBox>



                            <!-- 模型管理 -->
                            <Expander Header="模型管理" Margin="0,16,0,0">
                                <StackPanel Margin="16">
                                    <TextBlock Text="RWKV API服务信息：" FontWeight="Bold" Margin="0,0,0,8"/>
                                    <TextBlock Text="• 官方仓库: https://github.com/BlinkDL/RWKV-LM" Margin="0,0,0,4"/>
                                    <TextBlock Text="• API文档: 兼容OpenAI格式" Margin="0,0,0,16"/>

                                    <Button x:Name="RWKVDetectModelsButton"
                                          Content="检测可用模型"
                                          Click="RWKVDetectModels_Click"
                                          ToolTip="从RWKV API服务检测可用模型"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 高级设置 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16">
                        <TextBlock Text="高级设置" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="TemperatureTextBox"
                                       materialDesign:HintAssist.Hint="Temperature (0.0-2.0)"
                                       Text="0.7"
                                       Margin="0,0,0,16"
                                       ToolTip="控制生成文本的随机性。值越高越随机，值越低越确定。建议范围：0.1-1.5"/>

                                <TextBox x:Name="MaxTokensTextBox"
                                       materialDesign:HintAssist.Hint="最大Token数"
                                       Text="2000"
                                       ToolTip="单次生成的最大令牌数量。1个中文字符约等于2-3个Token。建议范围：100-8000"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <CheckBox x:Name="EnableThinkingChainCheckBox"
                                        Content="启用思维链处理"
                                        IsChecked="True"
                                        Margin="0,0,0,16"
                                        ToolTip="启用后将处理AI的思考过程。支持格式：&#10;1. &lt;think&gt;...&lt;/think&gt; + &lt;output&gt;...&lt;/output&gt;（推荐）&#10;2. &lt;thinking&gt;...&lt;/thinking&gt;（兼容）&#10;最终只输出&lt;output&gt;标签中的内容"/>

                                <CheckBox x:Name="EnableSegmentedGenerationCheckBox"
                                        Content="启用分段生成"
                                        IsChecked="True"
                                        Margin="0,0,0,16"
                                        ToolTip="启用后将长文本分段生成，避免生成失败。推荐启用，特别是生成长章节时"/>

                                <TextBox x:Name="SegmentationThresholdTextBox"
                                       materialDesign:HintAssist.Hint="分段生成阈值(字数)"
                                       Text="3000"
                                       Margin="0,0,0,16"
                                       ToolTip="超过此字数时启用分段生成。建议范围：2000-5000字"/>

                                <TextBox x:Name="SegmentWordCountTextBox"
                                       materialDesign:HintAssist.Hint="每段字数"
                                       Text="1500"
                                       Margin="0,0,0,16"
                                       ToolTip="分段生成时每段的目标字数。建议范围：1000-2000字"/>

                                <TextBox x:Name="TimeoutTextBox"
                                       materialDesign:HintAssist.Hint="请求超时(秒)"
                                       Text="30"
                                       ToolTip="API请求的超时时间。建议范围：10-120秒"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 工具API配置标签页 -->
            <TabItem Header="工具API配置">
                <TabItem.HeaderTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Tools" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="工具API配置" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </TabItem.HeaderTemplate>
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">

                        <!-- 使用说明 -->
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16"
                                           Background="{DynamicResource MaterialDesignCardBackground}">
                            <StackPanel Margin="16">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <materialDesign:PackIcon Kind="Information" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="工具API配置说明" FontWeight="Medium" FontSize="14"/>
                                </StackPanel>
                                <TextBlock TextWrapping="Wrap" Opacity="0.8" Margin="0,0,0,8">
                                    配置各种工具的API密钥和设置，包括联网搜索、翻译服务等。这些工具将增强AI的功能。
                                </TextBlock>
                                <Border Background="{DynamicResource MaterialDesignDivider}" Height="1" Margin="0,8"/>
                                <TextBlock Text="工具状态" FontWeight="Medium" FontSize="12" Margin="0,8,0,4"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <StackPanel Grid.Column="0">
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <Ellipse x:Name="SearchStatusIndicator" Width="8" Height="8" Fill="Red" Margin="0,0,8,0"/>
                                            <TextBlock Text="搜索工具" FontSize="11"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <Ellipse x:Name="TranslateStatusIndicator" Width="8" Height="8" Fill="Red" Margin="0,0,8,0"/>
                                            <TextBlock Text="翻译工具" FontSize="11"/>
                                        </StackPanel>
                                    </StackPanel>
                                    <StackPanel Grid.Column="1">
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <Ellipse x:Name="WeatherStatusIndicator" Width="8" Height="8" Fill="Red" Margin="0,0,8,0"/>
                                            <TextBlock Text="天气工具" FontSize="11"/>
                                        </StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <Ellipse x:Name="NewsStatusIndicator" Width="8" Height="8" Fill="Red" Margin="0,0,8,0"/>
                                            <TextBlock Text="新闻工具" FontSize="11"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Grid>
                                <TextBlock x:Name="ToolStatusText" Text="未填写APIkey的工具将被禁用"
                                          FontSize="10" Opacity="0.6" Margin="0,4,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 联网搜索API配置 -->
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="Web" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="联网搜索配置" FontWeight="Medium" FontSize="16"/>
                                </StackPanel>

                                <!-- 搜索引擎选择 -->
                                <TextBlock Text="搜索引擎" FontWeight="Medium" Margin="0,0,0,8"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <RadioButton x:Name="GoogleSearchRadio" Content="Google Search API"
                                               GroupName="SearchEngine" IsChecked="True"
                                               Margin="0,0,16,0"
                                               ToolTip="使用Google自定义搜索API进行联网搜索"/>
                                    <RadioButton x:Name="BingSearchRadio" Content="Bing Search API"
                                               GroupName="SearchEngine"
                                               Margin="0,0,16,0"
                                               ToolTip="使用微软Bing搜索API进行联网搜索"/>
                                    <RadioButton x:Name="SerperSearchRadio" Content="Serper API"
                                               GroupName="SearchEngine"
                                               ToolTip="使用Serper.dev提供的搜索API"/>
                                </StackPanel>

                                <!-- Google Search API配置 -->
                                <StackPanel x:Name="GoogleSearchConfig">
                                    <TextBox x:Name="GoogleApiKeyTextBox"
                                           materialDesign:HintAssist.Hint="Google API Key"
                                           Margin="0,0,0,16"
                                           ToolTip="Google自定义搜索API密钥，可在Google Cloud Console获取"/>

                                    <TextBox x:Name="GoogleSearchEngineIdTextBox"
                                           materialDesign:HintAssist.Hint="搜索引擎ID (CX)"
                                           Margin="0,0,0,16"
                                           ToolTip="Google自定义搜索引擎ID，在自定义搜索控制台获取"/>
                                </StackPanel>

                                <!-- Bing Search API配置 -->
                                <StackPanel x:Name="BingSearchConfig" Visibility="Collapsed">
                                    <TextBox x:Name="BingApiKeyTextBox"
                                           materialDesign:HintAssist.Hint="Bing Search API Key"
                                           Margin="0,0,0,16"
                                           ToolTip="微软Bing搜索API密钥，可在Azure门户获取"/>
                                </StackPanel>

                                <!-- Serper API配置 -->
                                <StackPanel x:Name="SerperSearchConfig" Visibility="Collapsed">
                                    <TextBox x:Name="SerperApiKeyTextBox"
                                           materialDesign:HintAssist.Hint="Serper API Key"
                                           Margin="0,0,0,16"
                                           ToolTip="Serper.dev API密钥，可在serper.dev网站获取"/>
                                </StackPanel>

                                <!-- 搜索设置 -->
                                <Expander Header="搜索设置" Margin="0,8,0,0">
                                    <StackPanel Margin="0,8,0,0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBox x:Name="SearchResultCountTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="搜索结果数量"
                                                   Text="10"
                                                   Margin="0,0,8,16"
                                                   ToolTip="每次搜索返回的结果数量，建议5-20"/>

                                            <TextBox x:Name="SearchLanguageTextBox"
                                                   Grid.Column="1"
                                                   materialDesign:HintAssist.Hint="搜索语言"
                                                   Text="zh-CN"
                                                   Margin="8,0,0,16"
                                                   ToolTip="搜索结果的语言代码，如zh-CN、en-US"/>
                                        </Grid>

                                        <CheckBox x:Name="SafeSearchCheckBox"
                                                Content="启用安全搜索"
                                                IsChecked="True"
                                                ToolTip="过滤不适宜的搜索结果"/>
                                    </StackPanel>
                                </Expander>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 翻译API配置 -->
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="Translate" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="翻译服务配置" FontWeight="Medium" FontSize="16"/>
                                </StackPanel>

                                <!-- 翻译服务选择 -->
                                <TextBlock Text="翻译服务" FontWeight="Medium" Margin="0,0,0,8"/>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <RadioButton x:Name="GoogleTranslateRadio" Content="Google Translate"
                                               GroupName="TranslateService" IsChecked="True"
                                               Margin="0,0,16,0"
                                               ToolTip="使用Google翻译API"/>
                                    <RadioButton x:Name="BaiduTranslateRadio" Content="百度翻译"
                                               GroupName="TranslateService"
                                               Margin="0,0,16,0"
                                               ToolTip="使用百度翻译API"/>
                                    <RadioButton x:Name="TencentTranslateRadio" Content="腾讯翻译"
                                               GroupName="TranslateService"
                                               ToolTip="使用腾讯云翻译API"/>
                                </StackPanel>

                                <!-- Google Translate配置 -->
                                <StackPanel x:Name="GoogleTranslateConfig">
                                    <TextBox x:Name="GoogleTranslateApiKeyTextBox"
                                           materialDesign:HintAssist.Hint="Google Translate API Key"
                                           Margin="0,0,0,16"
                                           ToolTip="Google翻译API密钥"/>
                                </StackPanel>

                                <!-- 百度翻译配置 -->
                                <StackPanel x:Name="BaiduTranslateConfig" Visibility="Collapsed">
                                    <TextBox x:Name="BaiduTranslateAppIdTextBox"
                                           materialDesign:HintAssist.Hint="百度翻译APP ID"
                                           Margin="0,0,0,16"
                                           ToolTip="百度翻译API的APP ID"/>

                                    <TextBox x:Name="BaiduTranslateSecretTextBox"
                                           materialDesign:HintAssist.Hint="百度翻译密钥"
                                           Margin="0,0,0,16"
                                           ToolTip="百度翻译API的密钥"/>
                                </StackPanel>

                                <!-- 腾讯翻译配置 -->
                                <StackPanel x:Name="TencentTranslateConfig" Visibility="Collapsed">
                                    <TextBox x:Name="TencentTranslateSecretIdTextBox"
                                           materialDesign:HintAssist.Hint="腾讯云SecretId"
                                           Margin="0,0,0,16"
                                           ToolTip="腾讯云API的SecretId"/>

                                    <TextBox x:Name="TencentTranslateSecretKeyTextBox"
                                           materialDesign:HintAssist.Hint="腾讯云SecretKey"
                                           Margin="0,0,0,16"
                                           ToolTip="腾讯云API的SecretKey"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 其他工具API配置 -->
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="Api" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="其他工具API" FontWeight="Medium" FontSize="16"/>
                                </StackPanel>

                                <!-- 天气API -->
                                <Expander Header="天气API配置" Margin="0,0,0,8">
                                    <StackPanel Margin="0,8,0,0">
                                        <TextBox x:Name="WeatherApiKeyTextBox"
                                               materialDesign:HintAssist.Hint="OpenWeatherMap API Key"
                                               Margin="0,0,0,16"
                                               ToolTip="OpenWeatherMap API密钥，用于获取天气信息"/>
                                    </StackPanel>
                                </Expander>

                                <!-- 新闻API -->
                                <Expander Header="新闻API配置" Margin="0,0,0,8">
                                    <StackPanel Margin="0,8,0,0">
                                        <TextBox x:Name="NewsApiKeyTextBox"
                                               materialDesign:HintAssist.Hint="NewsAPI Key"
                                               Margin="0,0,0,16"
                                               ToolTip="NewsAPI密钥，用于获取新闻信息"/>
                                    </StackPanel>
                                </Expander>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 测试区域 -->
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                            <StackPanel Margin="16">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <materialDesign:PackIcon Kind="TestTube" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock Text="API测试" FontWeight="Medium" FontSize="16"/>
                                </StackPanel>

                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox x:Name="TestQueryTextBox"
                                           Grid.Column="0"
                                           materialDesign:HintAssist.Hint="输入测试查询"
                                           Text="人工智能的发展历史"
                                           Margin="0,0,8,0"/>

                                    <Button x:Name="TestToolApiButton"
                                          Grid.Column="1"
                                          Content="测试搜索"
                                          Style="{StaticResource MaterialDesignRaisedButton}"
                                          Click="TestToolApi_Click"
                                          ToolTip="测试当前配置的搜索API"/>
                                </Grid>

                                <TextBox x:Name="TestResultTextBox"
                                       materialDesign:HintAssist.Hint="测试结果将显示在这里"
                                       IsReadOnly="True"
                                       AcceptsReturn="True"
                                       TextWrapping="Wrap"
                                       MinHeight="100"
                                       MaxHeight="200"
                                       VerticalScrollBarVisibility="Auto"
                                       Margin="0,16,0,0"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <!-- 底部按钮 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <ProgressBar x:Name="StatusProgressBar"
                               Width="20" Height="20"
                               Style="{StaticResource MaterialDesignCircularProgressBar}"
                               IsIndeterminate="False"
                               Visibility="Collapsed"
                               Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="就绪" VerticalAlignment="Center"
                             Foreground="White"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Content="取消" Style="{StaticResource MaterialDesignFlatButton}"
                          Margin="0,0,8,0" Click="Cancel_Click"/>
                    <Button x:Name="SaveButton" Content="保存配置"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="Save_Click"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>

<Window x:Class="DocumentCreationSystem.Views.AgentChatDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="AI Agent 助手"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}"
        Topmost="True"
        ShowInTaskbar="False">

    <Window.Resources>
        <!-- 用户消息样式 -->
        <Style x:Key="UserMessageStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="CornerRadius" Value="12,12,4,12"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="50,4,8,4"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
        
        <!-- AI消息样式 -->
        <Style x:Key="AIMessageStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="12,12,12,4"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="8,4,50,4"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>
        
        <!-- 工具调用消息样式 -->
        <Style x:Key="ToolMessageStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="20,2,20,2"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" Padding="16" Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行：标题和状态 -->
                <DockPanel Grid.Row="0">
                    <materialDesign:PackIcon Kind="Robot" DockPanel.Dock="Left"
                                           VerticalAlignment="Center" Margin="0,0,12,0" Width="32" Height="32"/>
                    <StackPanel DockPanel.Dock="Left">
                        <TextBlock Text="AI Agent 助手"
                                 Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="ProjectInfoText"
                                     Text="当前项目: 未选择"
                                     Style="{StaticResource MaterialDesignBody2TextBlock}"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <!-- 项目同步状态指示器 -->
                            <materialDesign:PackIcon x:Name="ProjectSyncStatusIcon"
                                                   Kind="CheckCircle"
                                                   Width="16" Height="16"
                                                   Margin="8,0,0,0"
                                                   VerticalAlignment="Center"
                                                   Foreground="Green"
                                                   Visibility="Collapsed"
                                                   ToolTip="项目同步状态"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 工具状态指示器 -->
                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                        <materialDesign:PackIcon x:Name="ToolStatusIcon" Kind="Tools"
                                               Width="20" Height="20" Margin="0,0,8,0"/>
                        <TextBlock x:Name="ToolStatusText" Text="工具就绪"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                    </StackPanel>
                </DockPanel>

                <!-- 第二行：项目选择 -->
                <Grid Grid.Row="1" Margin="44,12,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                             Text="选择项目:"
                             VerticalAlignment="Center"
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Margin="0,0,8,0"/>

                    <ComboBox x:Name="ProjectSelectionComboBox"
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="选择要操作的项目"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                            MinWidth="200"
                            MaxWidth="300"
                            SelectionChanged="ProjectSelectionComboBox_SelectionChanged"
                            ToolTip="选择要在AI助手中操作的项目">
                        <ComboBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Folder"
                                                           Width="16" Height="16"
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding Type, StringFormat=' ({0})'}"
                                             VerticalAlignment="Center"
                                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                                             FontSize="11"/>
                                </StackPanel>
                            </DataTemplate>
                        </ComboBox.ItemTemplate>
                    </ComboBox>

                    <Button x:Name="RefreshProjectsButton"
                          Grid.Column="2"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="刷新项目列表"
                          Click="RefreshProjects_Click"
                          Margin="8,0,0,0">
                        <materialDesign:PackIcon Kind="Refresh" Width="18" Height="18"/>
                    </Button>

                    <Button x:Name="BrowseDirectoryButton"
                          Grid.Column="3"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="浏览自定义目录"
                          Click="BrowseDirectory_Click"
                          Margin="4,0,0,0">
                        <materialDesign:PackIcon Kind="FolderOpen" Width="18" Height="18"/>
                    </Button>

                    <Button x:Name="ClearProjectButton"
                          Grid.Column="4"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="清除项目选择"
                          Click="ClearProject_Click"
                          Margin="4,0,0,0">
                        <materialDesign:PackIcon Kind="Close" Width="18" Height="18"/>
                    </Button>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- 对话区域 -->
        <materialDesign:Card Grid.Row="1" Padding="8">
            <ScrollViewer x:Name="ChatScrollViewer" VerticalScrollBarVisibility="Auto">
                <StackPanel x:Name="ChatPanel" Margin="8">
                    <!-- 欢迎消息 -->
                    <Border Style="{StaticResource AIMessageStyle}">
                        <StackPanel>
                            <TextBlock Text="AI Agent" FontWeight="Bold"
                                     Foreground="{DynamicResource PrimaryHueMidBrush}" Margin="0,0,0,4"/>
                            <TextBlock TextWrapping="Wrap">
                                <Run Text="您好！我是AI Agent助手。我可以帮您："/>
                                <LineBreak/>
                                <Run Text="📁 智能查找和管理项目文件"/>
                                <LineBreak/>
                                <Run Text="✏️ 自动更新和修改文件内容"/>
                                <LineBreak/>
                                <Run Text="🔍 深度搜索文件和内容"/>
                                <LineBreak/>
                                <Run Text="📊 分析项目结构和文件关系"/>
                                <LineBreak/>
                                <Run Text="📝 生成内容摘要和统计信息"/>
                                <LineBreak/>
                                <Run Text="🎯 提取关键信息（角色、地点、事件）"/>
                                <LineBreak/>
                                <Run Text="🔄 批量处理和比较文件"/>
                                <LineBreak/>
                                <Run Text="📈 生成项目概览和分析报告"/>
                                <LineBreak/>
                                <LineBreak/>
                                <Run Text="示例用法："/>
                                <LineBreak/>
                                <Run Text="• '扫描整个项目结构'"/>
                                <LineBreak/>
                                <Run Text="• '分析文件之间的关联关系'"/>
                                <LineBreak/>
                                <Run Text="• '提取第一章中的角色信息'"/>
                                <LineBreak/>
                                <Run Text="• '生成项目内容概览'"/>
                                <LineBreak/>
                                <Run Text="• '将所有章节中的张三改为李四'"/>
                                <LineBreak/>
                                <Run Text="• '分析第一章的内容'"/>
                                <LineBreak/>
                                <LineBreak/>
                                <Run Text="请告诉我您需要什么帮助！"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
        </materialDesign:Card>

        <!-- 输入区域 -->
        <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,16,0,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="MessageTextBox" 
                       Grid.Column="0"
                       materialDesign:HintAssist.Hint="输入您的消息..."
                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                       AcceptsReturn="True"
                       TextWrapping="Wrap"
                       MaxHeight="120"
                       VerticalScrollBarVisibility="Auto"
                       KeyDown="MessageTextBox_KeyDown"/>

                <Button x:Name="AttachFileButton"
                      Grid.Column="1"
                      Style="{StaticResource MaterialDesignIconButton}"
                      Margin="8,0,0,0"
                      ToolTip="附加文件"
                      Click="AttachFile_Click">
                    <materialDesign:PackIcon Kind="Attachment"/>
                </Button>

                <Button x:Name="SendButton"
                      Grid.Column="2"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Margin="8,0,0,0"
                      Content="发送"
                      Click="Send_Click"
                      IsDefault="True"/>
            </Grid>
        </materialDesign:Card>

        <!-- 状态栏 -->
        <Grid Grid.Row="3">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <materialDesign:PackIcon x:Name="StatusIcon" Kind="Circle" 
                                       Width="12" Height="12" Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusText" Text="就绪" 
                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="ClearChatButton"
                      Content="清空对话"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Margin="0,0,8,0"
                      Click="ClearChat_Click"/>
                <Button x:Name="SettingsButton"
                      Content="设置"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Margin="0,0,8,0"
                      Click="Settings_Click"/>
                <Button x:Name="CloseButton"
                      Content="关闭"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Click="Close_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>

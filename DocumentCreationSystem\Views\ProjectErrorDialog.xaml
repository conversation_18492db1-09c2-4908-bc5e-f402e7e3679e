<Window x:Class="DocumentCreationSystem.Views.ProjectErrorDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="项目错误修复" Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 错误信息 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="检测到项目配置问题" FontSize="18" FontWeight="Bold" Foreground="Red"/>
            <TextBlock x:Name="ErrorMessageTextBlock" Text="小说项目不存在" FontSize="14" Margin="0,10,0,0" TextWrapping="Wrap"/>
        </StackPanel>

        <!-- 解决方案选项 -->
        <GroupBox Grid.Row="1" Header="解决方案" Margin="0,0,0,20">
            <StackPanel>
                <RadioButton x:Name="CreateNovelProjectRadio" Content="为当前项目创建小说项目记录" IsChecked="True" Margin="0,10,0,10"/>
                <StackPanel x:Name="NovelProjectPanel" Margin="20,0,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="小说标题：" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="NovelTitleTextBox" Grid.Row="0" Grid.Column="1" Text="我的小说" Margin="10,5,0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="创作方向：" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="CreativeDirectionTextBox" Grid.Row="1" Grid.Column="1" Text="科幻冒险小说" Margin="10,5,0,5"/>
                    </Grid>
                </StackPanel>

                <RadioButton x:Name="CreateNewProjectRadio" Content="创建新的正式项目" Margin="0,10,0,10"/>
                <StackPanel x:Name="NewProjectPanel" Margin="20,0,0,0" IsEnabled="False">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="项目名称：" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="ProjectNameTextBox" Grid.Row="0" Grid.Column="1" Margin="10,5,0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="项目路径：" VerticalAlignment="Center" Margin="0,5"/>
                        <TextBox x:Name="ProjectPathTextBox" Grid.Row="1" Grid.Column="1" Margin="10,5,0,5" IsReadOnly="True"/>
                    </Grid>
                </StackPanel>

                <RadioButton x:Name="CancelOperationRadio" Content="取消操作，稍后手动处理" Margin="0,10,0,0"/>
            </StackPanel>
        </GroupBox>

        <!-- 说明信息 -->
        <TextBlock Grid.Row="2" Text="说明：此问题通常发生在通过文件夹方式打开项目时。选择一个解决方案来继续使用写书功能。" 
                   FontSize="12" Foreground="Gray" TextWrapping="Wrap" Margin="0,0,0,20"/>

        <!-- 按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button x:Name="FixButton" Content="修复" Width="80" Height="30" Margin="0,0,10,0" Click="Fix_Click"/>
            <Button x:Name="CancelButton" Content="取消" Width="80" Height="30" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>

using System;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 配置更新通知服务
    /// 用于在不同窗口之间传递配置更新通知
    /// </summary>
    public static class ConfigurationNotificationService
    {
        /// <summary>
        /// AI模型配置更新事件
        /// </summary>
        public static event EventHandler? AIModelConfigurationUpdated;

        /// <summary>
        /// Agent配置更新事件
        /// </summary>
        public static event EventHandler? AgentConfigurationUpdated;

        /// <summary>
        /// 工具配置更新事件
        /// </summary>
        public static event EventHandler? ToolConfigurationUpdated;

        /// <summary>
        /// 通知AI模型配置已更新
        /// </summary>
        public static void NotifyAIModelConfigurationUpdated()
        {
            AIModelConfigurationUpdated?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// 通知Agent配置已更新
        /// </summary>
        public static void NotifyAgentConfigurationUpdated()
        {
            AgentConfigurationUpdated?.Invoke(null, EventArgs.Empty);
        }

        /// <summary>
        /// 通知工具配置已更新
        /// </summary>
        public static void NotifyToolConfigurationUpdated()
        {
            ToolConfigurationUpdated?.Invoke(null, EventArgs.Empty);
        }
    }
}

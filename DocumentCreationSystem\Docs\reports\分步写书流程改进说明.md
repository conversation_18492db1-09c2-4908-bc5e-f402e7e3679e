# 分步写书流程改进说明

## 改进概述

本次改进对分步写书功能进行了全面优化，主要包括以下方面：

1. **步骤顺序重组**：调整为更合理的创作流程
2. **界面交互优化**：添加滚动条支持和批量生成功能
3. **文件格式选择**：支持.docx和.txt格式保存
4. **时间线管理**：自动更新时间线和章节关联
5. **章节连贯性**：根据已生成内容调整下一章细纲

## 新的写书流程

### 1. 生成全书大纲
- 基于用户输入的创作方向和基本设定
- 确立小说的整体框架和主线发展

### 2. 生成世界设定
- 基于全书大纲生成详细的世界设定
- 包括世界观、角色、势力、修炼体系等23个设定文件
- 为后续创作提供完整的世界背景

### 3. 生成卷宗大纲
- 基于全书大纲和世界设定
- 将小说分为多个卷宗，每卷有明确的主题和发展方向
- 合理分配章节数量

### 4. 逐卷生成章节细纲和时间线
- 基于卷宗大纲和世界设定
- 为每个章节创建详细的细纲
- 同时生成时间线规划，确保情节发展的连贯性

### 5. 生成章节正文并更新时间线
- 基于章节细纲和时间线规划生成正文
- 生成完成后自动更新时间线
- 根据已生成内容调整下一章的细纲和规划
- 支持单章生成和批量生成

## 技术实现细节

### 1. 界面改进

#### 滚动条支持
```xml
<ScrollViewer VerticalScrollBarVisibility="Auto" 
              MaxHeight="600" 
              Padding="0,0,8,0">
    <StackPanel>
        <!-- 步骤卡片 -->
    </StackPanel>
</ScrollViewer>
```

#### 文件格式选择
```xml
<ComboBox x:Name="FileFormatComboBox" 
        materialDesign:HintAssist.Hint="选择保存格式"
        Style="{StaticResource MaterialDesignFloatingHintComboBox}">
    <ComboBoxItem Content=".docx (Word文档)" Tag="docx"/>
    <ComboBoxItem Content=".txt (纯文本)" Tag="txt"/>
</ComboBox>
```

#### 批量生成功能
```xml
<Button x:Name="BatchGenerateChaptersButton" 
      Content="批量生成章节正文"
      Style="{StaticResource MaterialDesignOutlinedButton}"
      HorizontalAlignment="Stretch"
      Margin="0,4,0,0"
      IsEnabled="False"
      Click="BatchGenerateChapters_Click"/>
```

### 2. 文件保存逻辑

#### 文件命名规则
```csharp
// 构建文件名
string fileName;
var volumeInfo = _currentState.VolumeOutlines.FirstOrDefault(v => 
    chapterNumber >= v.StartChapter && chapterNumber <= v.EndChapter);

if (volumeInfo != null)
{
    fileName = $"{_currentState.BookTitle}_卷{volumeInfo.VolumeNumber:D2}_{volumeInfo.Title}_章{chapterNumber:D4}_{chapterTitle}";
}
else
{
    fileName = $"{_currentState.BookTitle}_章{chapterNumber:D4}_{chapterTitle}";
}
```

#### 保存为不同格式
```csharp
if (fileExtension.Equals(".docx", StringComparison.OrdinalIgnoreCase))
{
    // 保存为Word文档
    await SaveAsDocxAsync(filePath, chapterTitle, content);
}
else
{
    // 保存为纯文本
    await System.IO.File.WriteAllTextAsync(filePath, content);
}
```

### 3. 时间线管理

#### 更新时间线
```csharp
// 更新时间线文件
var timelinePath = System.IO.Path.Combine(_currentState.ProjectPath, "Settings", "时间线管理.md");

// 读取现有时间线
string existingTimeline = "";
if (System.IO.File.Exists(timelinePath))
{
    existingTimeline = await System.IO.File.ReadAllTextAsync(timelinePath);
}

// 提取章节中的时间线信息并更新
var timelineUpdate = $"\n## 第{chapterNumber}章时间线更新\n\n";
timelineUpdate += $"- 章节标题: {_currentState.ChapterTitles.GetValueOrDefault(chapterNumber, $"第{chapterNumber}章")}\n";
timelineUpdate += $"- 更新时间: {DateTime.Now}\n";
timelineUpdate += $"- 内容摘要: {(chapterContent.Length > 100 ? chapterContent.Substring(0, 100) + "..." : chapterContent)}\n";

// 更新时间线文件
await System.IO.File.AppendAllTextAsync(timelinePath, timelineUpdate);
```

### 4. 章节连贯性

#### 调整下一章细纲
```csharp
private async Task AdjustNextChapterOutlineAsync(int currentChapter, string currentContent)
{
    try
    {
        if (_currentState == null)
            return;
        
        var nextChapter = currentChapter + 1;
        if (!_currentState.ChapterOutlines.ContainsKey(nextChapter))
            return;
        
        // 使用AI服务根据当前章节内容调整下一章的细纲
        // ...
        
        _logger.LogInformation($"已调整第{nextChapter}章细纲");
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, $"调整下一章细纲失败: {ex.Message}");
    }
}
```

## 使用说明

### 1. 基本信息设置
- 填写书籍标题、创作方向
- 设置卷数（支持1-50之间的任意数值）
- 设置总章节数和每章字数

### 2. 分步执行流程
1. **生成全书大纲**：点击"生成全书大纲"按钮
2. **生成世界设定**：点击"生成世界设定"按钮
3. **生成卷宗大纲**：点击"生成卷宗大纲"按钮
4. **生成章节细纲**：选择卷宗，点击"生成选定卷的章节细纲和时间线"按钮
5. **生成章节正文**：
   - 单章生成：输入章节号，点击"生成指定章节正文"按钮
   - 批量生成：点击"批量生成章节正文"按钮，选择章节范围

### 3. 文件格式选择
- 在界面底部选择保存格式：.docx或.txt
- 所有生成的内容将按照选定格式保存到项目文件夹

### 4. 文件组织结构
```
项目文件夹/
├── Outlines/                    # 大纲文件夹
│   ├── overall_outline.txt      # 全书大纲
│   ├── Volumes/                 # 卷宗大纲
│   │   ├── volume_01_outline.txt
│   │   └── ...
│   └── Chapters/                # 章节细纲
│       ├── chapter_001_outline.txt
│       └── ...
├── Chapters/                    # 章节正文
│   ├── 书名_卷01_卷名_章0001_章名.docx
│   └── ...
├── Settings/                    # 世界设定
│   ├── 世界观设定管理.md
│   ├── 时间线管理.md
│   └── ...
└── step_execution_state.json   # 执行状态文件
```

## 后续优化方向

1. **完善Word文档生成**：使用DocumentFormat.OpenXml实现更完善的Word文档生成
2. **AI时间线提取**：使用AI服务从章节内容中提取时间线信息
3. **角色关系图更新**：根据章节内容自动更新角色关系图
4. **多线程批量生成**：优化批量生成性能
5. **进度可视化**：添加更直观的创作进度可视化界面

## 注意事项

1. 确保按照流程顺序执行，不要跳过步骤
2. 批量生成可能需要较长时间，请耐心等待
3. 生成过程中可以随时点击"停止执行"按钮中断操作
4. 所有生成的内容都会自动保存到项目文件夹，可以随时查看和编辑

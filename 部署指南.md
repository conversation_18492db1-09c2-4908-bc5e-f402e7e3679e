# 部署指南

## 系统要求

### 最低配置
- **操作系统**: Windows 10 (1903) 或更高版本
- **处理器**: Intel Core i3 或 AMD 同等级别
- **内存**: 4GB RAM
- **存储**: 2GB 可用磁盘空间
- **网络**: 可选（用于在线AI服务）

### 推荐配置
- **操作系统**: Windows 11
- **处理器**: Intel Core i5 或 AMD Ryzen 5
- **内存**: 8GB RAM 或更多
- **存储**: 10GB 可用磁盘空间（SSD推荐）
- **网络**: 稳定的互联网连接

## 环境准备

### 1. 安装 .NET 8.0 Runtime

#### 方法一：从官网下载
1. 访问 [.NET 官网](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 下载 ".NET 8.0 Runtime" (不是SDK)
3. 运行安装程序并按提示完成安装

#### 方法二：使用包管理器
```powershell
# 使用 Chocolatey
choco install dotnet-8.0-runtime

# 使用 Winget
winget install Microsoft.DotNet.Runtime.8
```

### 2. 验证安装
```bash
dotnet --version
```
应该显示 8.0.x 版本号。

## 应用程序部署

### 方法一：从源码编译

#### 1. 获取源码
```bash
git clone <repository-url>
cd 文档管理及创作系统
```

#### 2. 编译应用程序
```bash
# 编译Debug版本
dotnet build DocumentCreationSystem

# 编译Release版本
dotnet build DocumentCreationSystem -c Release
```

#### 3. 发布应用程序
```bash
# 发布为自包含应用程序
dotnet publish DocumentCreationSystem -c Release -r win-x64 --self-contained true

# 发布为框架依赖应用程序
dotnet publish DocumentCreationSystem -c Release -r win-x64 --self-contained false
```

### 方法二：使用预编译版本

1. 下载最新的Release版本
2. 解压到目标目录
3. 确保目录结构完整

## 配置设置

### 1. 基本配置

编辑 `appsettings.json` 文件：

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=app.db"
  },
  "Application": {
    "Name": "文档管理及AI创作系统",
    "Version": "1.0.0",
    "DefaultProjectPath": "C:\\DocumentCreation\\Projects",
    "MaxRecentProjects": 10,
    "AutoBackup": true,
    "Theme": "Light"
  }
}
```

### 2. AI服务配置

#### 智谱AI配置
```json
{
  "AI": {
    "ZhipuAI": {
      "ApiKey": "your-zhipu-api-key",
      "BaseUrl": "https://open.bigmodel.cn/api/paas/v4"
    },
    "DefaultProvider": "ZhipuAI",
    "DefaultModel": "glm-4"
  }
}
```

#### Ollama配置
```json
{
  "AI": {
    "Ollama": {
      "BaseUrl": "http://localhost:11434"
    }
  }
}
```

#### LM Studio配置
```json
{
  "AI": {
    "LMStudio": {
      "BaseUrl": "http://localhost:1234"
    }
  }
}
```

### 3. 向量数据库配置

#### Qdrant配置
```json
{
  "Vector": {
    "Qdrant": {
      "Url": "http://localhost:6333",
      "CollectionName": "documents",
      "VectorSize": 1024
    }
  }
}
```

## 依赖服务部署

### 1. Qdrant向量数据库

#### Docker部署（推荐）
```bash
# 拉取Qdrant镜像
docker pull qdrant/qdrant

# 运行Qdrant容器
docker run -p 6333:6333 -v $(pwd)/qdrant_storage:/qdrant/storage qdrant/qdrant
```

#### 本地安装
1. 从 [Qdrant官网](https://qdrant.tech/) 下载适合的版本
2. 按照官方文档进行安装和配置

### 2. Ollama（可选）

#### 安装Ollama
1. 访问 [Ollama官网](https://ollama.ai/)
2. 下载Windows版本安装程序
3. 安装并启动服务

#### 下载模型
```bash
# 下载推荐的中文模型
ollama pull qwen2:7b
ollama pull llama3:8b
```

### 3. LM Studio（可选）

1. 访问 [LM Studio官网](https://lmstudio.ai/)
2. 下载并安装LM Studio
3. 在应用内下载所需的模型
4. 启动本地服务器

## 数据库初始化

### 自动初始化
应用程序首次运行时会自动创建SQLite数据库和必要的表结构。

### 手动初始化
如果需要手动初始化数据库：

```bash
# 进入项目目录
cd DocumentCreationSystem

# 运行数据库迁移
dotnet ef database update
```

## 运行应用程序

### 开发环境运行
```bash
dotnet run --project DocumentCreationSystem
```

### 生产环境运行
```bash
# 进入发布目录
cd bin/Release/net8.0/publish

# 运行应用程序
./DocumentCreationSystem.exe
```

### 作为Windows服务运行

#### 1. 安装服务
```powershell
# 使用sc命令创建服务
sc create "DocumentCreationSystem" binPath="C:\Path\To\DocumentCreationSystem.exe"

# 设置服务启动类型
sc config "DocumentCreationSystem" start=auto
```

#### 2. 启动服务
```powershell
sc start "DocumentCreationSystem"
```

## 性能优化

### 1. 内存优化
- 设置合适的垃圾回收模式
- 限制并发操作数量
- 定期清理缓存

### 2. 磁盘优化
- 使用SSD存储
- 定期清理临时文件
- 配置合适的日志级别

### 3. 网络优化
- 配置连接池
- 设置合适的超时时间
- 使用CDN加速（如适用）

## 监控和日志

### 1. 日志配置
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "DocumentCreationSystem": "Debug",
      "Microsoft": "Warning",
      "System": "Warning"
    },
    "Console": {
      "IncludeScopes": true
    },
    "File": {
      "Path": "logs/app-.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30
    }
  }
}
```

### 2. 性能监控
- 监控CPU和内存使用率
- 跟踪API响应时间
- 监控数据库连接状态

## 备份和恢复

### 1. 数据备份
```bash
# 备份SQLite数据库
copy app.db app_backup_$(date +%Y%m%d).db

# 备份配置文件
copy appsettings.json appsettings_backup.json

# 备份项目文件
xcopy /E /I "Projects" "Projects_Backup"
```

### 2. 自动备份脚本
创建PowerShell脚本进行定期备份：

```powershell
# backup.ps1
$backupDir = "C:\Backups\DocumentCreation\$(Get-Date -Format 'yyyyMMdd')"
New-Item -ItemType Directory -Path $backupDir -Force

Copy-Item "app.db" "$backupDir\app.db"
Copy-Item "appsettings.json" "$backupDir\appsettings.json"
Copy-Item "Projects" "$backupDir\Projects" -Recurse
```

### 3. 恢复数据
```bash
# 恢复数据库
copy app_backup_20240101.db app.db

# 恢复配置
copy appsettings_backup.json appsettings.json

# 恢复项目文件
xcopy /E /I "Projects_Backup" "Projects"
```

## 故障排除

### 常见问题

#### 1. 应用程序无法启动
- 检查.NET Runtime是否正确安装
- 验证配置文件格式是否正确
- 查看错误日志获取详细信息

#### 2. AI服务连接失败
- 检查网络连接
- 验证API密钥是否正确
- 确认服务端点URL是否可访问

#### 3. 数据库连接问题
- 检查数据库文件权限
- 验证连接字符串配置
- 确认磁盘空间是否充足

#### 4. 向量搜索不工作
- 检查Qdrant服务是否运行
- 验证向量数据库连接配置
- 确认集合是否正确创建

### 日志分析
```bash
# 查看最新日志
tail -f logs/app-$(date +%Y%m%d).log

# 搜索错误信息
grep "ERROR" logs/app-*.log

# 分析性能问题
grep "Performance" logs/app-*.log
```

## 安全考虑

### 1. 数据安全
- 定期备份重要数据
- 使用强密码保护API密钥
- 限制文件系统访问权限

### 2. 网络安全
- 使用HTTPS连接外部服务
- 配置防火墙规则
- 定期更新依赖组件

### 3. 访问控制
- 限制应用程序运行权限
- 配置用户访问控制
- 审计重要操作

## 更新和维护

### 1. 应用程序更新
```bash
# 停止应用程序
# 备份当前版本
# 部署新版本
# 运行数据库迁移（如需要）
# 重启应用程序
```

### 2. 依赖更新
- 定期检查.NET Runtime更新
- 更新AI服务SDK
- 升级数据库版本

### 3. 维护计划
- 每周检查系统状态
- 每月清理日志文件
- 每季度进行完整备份
- 每年进行安全审计

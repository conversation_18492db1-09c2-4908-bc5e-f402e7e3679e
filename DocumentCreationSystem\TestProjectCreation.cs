using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;
using System.IO;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试项目创建功能的辅助类
    /// </summary>
    public class TestProjectCreation
    {
        private readonly ILogger<TestProjectCreation> _logger;
        private readonly IProjectService _projectService;
        private readonly IDataStorageService _dataStorage;

        public TestProjectCreation(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetRequiredService<ILogger<TestProjectCreation>>();
            _projectService = serviceProvider.GetRequiredService<IProjectService>();
            _dataStorage = serviceProvider.GetRequiredService<IDataStorageService>();
        }

        /// <summary>
        /// 测试项目创建和小说项目创建流程
        /// </summary>
        public async Task<bool> TestProjectCreationFlowAsync()
        {
            try
            {
                _logger.LogInformation("开始测试项目创建流程...");

                // 1. 创建测试项目路径
                var testProjectPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "测试小说项目");
                if (!Directory.Exists(testProjectPath))
                {
                    Directory.CreateDirectory(testProjectPath);
                }

                // 2. 创建基础项目
                _logger.LogInformation("创建基础项目...");
                var project = await _projectService.CreateProjectAsync(
                    "测试小说项目",
                    "Novel",
                    testProjectPath,
                    "这是一个测试小说项目");

                _logger.LogInformation($"基础项目创建成功，ID: {project.Id}");

                // 3. 创建小说项目
                _logger.LogInformation("创建小说项目...");
                var novelProject = await _projectService.CreateNovelProjectAsync(
                    "测试小说项目",
                    "测试小说标题",
                    "科幻冒险小说",
                    testProjectPath);

                _logger.LogInformation($"小说项目创建成功，ID: {novelProject.Id}, 关联项目ID: {novelProject.ProjectId}");

                // 4. 验证项目是否正确创建
                _logger.LogInformation("验证项目创建结果...");
                var retrievedProject = await _projectService.GetProjectAsync(project.Id);
                var retrievedNovelProject = await _projectService.GetNovelProjectAsync(project.Id);

                if (retrievedProject == null)
                {
                    _logger.LogError("无法检索到创建的基础项目");
                    return false;
                }

                if (retrievedNovelProject == null)
                {
                    _logger.LogError("无法检索到创建的小说项目");
                    return false;
                }

                _logger.LogInformation("项目创建流程测试成功！");
                _logger.LogInformation($"基础项目: ID={retrievedProject.Id}, 名称={retrievedProject.Name}, 类型={retrievedProject.Type}");
                _logger.LogInformation($"小说项目: ID={retrievedNovelProject.Id}, 标题={retrievedNovelProject.Title}, 状态={retrievedNovelProject.Status}");

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "项目创建流程测试失败");
                return false;
            }
        }

        /// <summary>
        /// 显示当前数据存储状态
        /// </summary>
        public async Task ShowDataStorageStatusAsync()
        {
            try
            {
                _logger.LogInformation("=== 数据存储状态 ===");

                var allProjects = await _projectService.GetAllProjectsAsync();
                _logger.LogInformation($"总项目数: {allProjects.Count}");

                foreach (var project in allProjects)
                {
                    _logger.LogInformation($"项目: ID={project.Id}, 名称={project.Name}, 类型={project.Type}, 路径={project.RootPath}");
                    
                    // 检查对应的小说项目
                    if (project.Type == "Novel")
                    {
                        var novelProject = await _projectService.GetNovelProjectAsync(project.Id);
                        if (novelProject != null)
                        {
                            _logger.LogInformation($"  -> 小说项目: ID={novelProject.Id}, 标题={novelProject.Title}, 状态={novelProject.Status}");
                        }
                        else
                        {
                            _logger.LogWarning($"  -> 小说项目记录缺失！");
                        }
                    }
                }

                _logger.LogInformation("=== 数据存储状态结束 ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "显示数据存储状态时发生错误");
            }
        }

        /// <summary>
        /// 清理测试数据
        /// </summary>
        public async Task CleanupTestDataAsync()
        {
            try
            {
                _logger.LogInformation("清理测试数据...");

                var testProjectPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "测试小说项目");
                if (Directory.Exists(testProjectPath))
                {
                    Directory.Delete(testProjectPath, true);
                    _logger.LogInformation("测试项目文件夹已删除");
                }

                _logger.LogInformation("测试数据清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理测试数据时发生错误");
            }
        }
    }
}

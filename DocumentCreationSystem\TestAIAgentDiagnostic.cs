using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem
{
    /// <summary>
    /// AI助手诊断工具 - 专门诊断AI助手中的模型配置问题
    /// </summary>
    public class TestAIAgentDiagnostic
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== AI助手模型配置诊断工具 ===");
            Console.WriteLine();
            
            try
            {
                // 获取服务提供者
                var serviceProvider = App.ServiceProvider;
                if (serviceProvider == null)
                {
                    Console.WriteLine("❌ 服务提供者未初始化");
                    Console.WriteLine("   请确保应用程序已正确启动");
                    return;
                }

                Console.WriteLine("✅ 服务提供者已获取");

                // 获取AI服务
                var aiService = serviceProvider.GetService<IAIService>();
                if (aiService == null)
                {
                    Console.WriteLine("❌ AI服务未注册");
                    Console.WriteLine("   请检查依赖注入配置");
                    return;
                }

                Console.WriteLine("✅ AI服务已获取");
                Console.WriteLine($"   服务类型: {aiService.GetType().Name}");

                // 检查AI服务管理器
                if (aiService is AIServiceManager aiServiceManager)
                {
                    Console.WriteLine("✅ AI服务管理器类型正确");
                    
                    // 1. 检查配置文件
                    Console.WriteLine("\n1. 检查AI配置文件...");
                    var configService = serviceProvider.GetService<IAIModelConfigService>();
                    if (configService != null)
                    {
                        try
                        {
                            var config = await configService.GetConfigAsync();
                            Console.WriteLine($"   ✅ 配置文件加载成功");
                            Console.WriteLine($"   📋 平台: {config.Platform}");
                            Console.WriteLine($"   📋 温度: {config.Temperature}");
                            Console.WriteLine($"   📋 最大令牌: {config.MaxTokens}");
                            
                            // 检查智谱AI配置
                            if (config.ZhipuAIConfig != null)
                            {
                                Console.WriteLine($"   📋 智谱AI配置:");
                                Console.WriteLine($"      - API密钥: {(!string.IsNullOrEmpty(config.ZhipuAIConfig.ApiKey) ? "已设置" : "❌ 未设置")}");
                                Console.WriteLine($"      - 基础URL: {config.ZhipuAIConfig.BaseUrl}");
                                Console.WriteLine($"      - 模型: {config.ZhipuAIConfig.Model}");
                            }
                            else
                            {
                                Console.WriteLine("   ❌ 智谱AI配置不存在");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"   ❌ 配置文件加载失败: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("   ❌ AI配置服务未注册");
                    }
                    
                    // 2. 强制重新加载配置
                    Console.WriteLine("\n2. 重新加载AI服务配置...");
                    try
                    {
                        await aiServiceManager.ReloadConfigurationAsync();
                        Console.WriteLine("   ✅ 配置重新加载完成");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ 配置重新加载失败: {ex.Message}");
                    }
                    
                    // 3. 检查当前提供者
                    Console.WriteLine("\n3. 检查当前AI提供者...");
                    var providerName = aiServiceManager.GetCurrentProviderName();
                    Console.WriteLine($"   当前提供者: {providerName}");
                    
                    if (providerName == "无")
                    {
                        Console.WriteLine("   ❌ 没有可用的AI服务提供者");
                        Console.WriteLine("   这是导致'AI模型未配置'错误的根本原因");
                    }
                    else
                    {
                        Console.WriteLine("   ✅ AI提供者已设置");
                    }
                    
                    // 4. 检查当前模型
                    Console.WriteLine("\n4. 检查当前模型...");
                    var currentModel = aiServiceManager.GetCurrentModel();
                    if (currentModel != null)
                    {
                        Console.WriteLine($"   ✅ 当前模型: {currentModel.Name} (ID: {currentModel.Id})");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ 当前模型为空");
                    }
                    
                    // 5. 获取可用模型
                    Console.WriteLine("\n5. 检查可用模型...");
                    try
                    {
                        var availableModels = await aiServiceManager.GetAvailableModelsAsync();
                        Console.WriteLine($"   可用模型数量: {availableModels.Count}");
                        if (availableModels.Count > 0)
                        {
                            Console.WriteLine("   可用模型列表:");
                            foreach (var model in availableModels)
                            {
                                Console.WriteLine($"     - {model.Name} (ID: {model.Id})");
                            }
                        }
                        else
                        {
                            Console.WriteLine("   ❌ 没有可用模型");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ 获取可用模型失败: {ex.Message}");
                        Console.WriteLine($"   异常类型: {ex.GetType().Name}");
                    }
                    
                    // 6. 测试文本生成
                    Console.WriteLine("\n6. 测试文本生成...");
                    try
                    {
                        var result = await aiServiceManager.GenerateTextAsync("你好，请回复'测试成功'。", 50, 0.7f);
                        Console.WriteLine($"   ✅ 文本生成成功");
                        Console.WriteLine($"   生成内容: {result.Substring(0, Math.Min(100, result.Length))}...");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ 文本生成失败: {ex.Message}");
                        Console.WriteLine($"   异常类型: {ex.GetType().Name}");
                        if (ex.InnerException != null)
                        {
                            Console.WriteLine($"   内部异常: {ex.InnerException.Message}");
                        }
                        
                        // 这里是关键 - 如果文本生成失败，说明AI助手也会失败
                        Console.WriteLine("\n   🔍 这个错误解释了为什么AI助手提示'AI模型未配置'");
                    }
                }
                else
                {
                    Console.WriteLine($"❌ AI服务类型不正确: {aiService.GetType().Name}");
                }
                
                // 7. 模拟AI助手调用
                Console.WriteLine("\n7. 模拟AI助手调用...");
                try
                {
                    // 模拟AI助手中的调用方式
                    var testPrompt = "你是一个专业的AI写作助手。用户说：你好";
                    var response = await aiService.GenerateTextAsync(testPrompt, 2000, 0.7f);
                    Console.WriteLine($"   ✅ AI助手模拟调用成功");
                    Console.WriteLine($"   响应: {response.Substring(0, Math.Min(100, response.Length))}...");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ AI助手模拟调用失败: {ex.Message}");
                    Console.WriteLine($"   这就是AI助手中显示的错误信息");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 诊断过程中发生错误: {ex.Message}");
                Console.WriteLine($"   异常类型: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"   内部异常: {ex.InnerException.Message}");
                }
            }
            
            Console.WriteLine("\n=== 诊断完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

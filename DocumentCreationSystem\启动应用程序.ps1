# 文档管理及AI创作系统 - 正式版本启动脚本
# PowerShell版本

Write-Host "===================================" -ForegroundColor Green
Write-Host "文档管理及AI创作系统 - 正式版本" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""

try {
    Write-Host "检查.NET SDK..." -ForegroundColor Yellow
    $dotnetVersion = dotnet --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 未找到.NET SDK，请先安装.NET 8.0 SDK" -ForegroundColor Red
        Write-Host "下载地址: https://dotnet.microsoft.com/download/dotnet/8.0" -ForegroundColor Cyan
        Read-Host "按任意键退出"
        exit 1
    }
    Write-Host "✓ .NET SDK版本: $dotnetVersion" -ForegroundColor Green
    Write-Host ""

    Write-Host "启动应用程序..." -ForegroundColor Yellow
    dotnet run --project .
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: 应用程序启动失败" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }

    Write-Host ""
    Write-Host "应用程序已关闭" -ForegroundColor Green
}
catch {
    Write-Host "发生未预期的错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host $_.Exception.StackTrace -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Read-Host "按任意键退出"

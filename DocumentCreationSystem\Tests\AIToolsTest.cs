using DocumentCreationSystem.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DocumentCreationSystem.Tests;

/// <summary>
/// AI工具系统测试
/// </summary>
public class AIToolsTest
{
    private readonly IAIToolsService _aiToolsService;
    private readonly ILogger<AIToolsTest> _logger;

    public AIToolsTest(IAIToolsService aiToolsService, ILogger<AIToolsTest> logger)
    {
        _aiToolsService = aiToolsService;
        _logger = logger;
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public async Task RunAllTestsAsync()
    {
        _logger.LogInformation("开始AI工具系统测试...");

        try
        {
            await TestGetAvailableTools();
            await TestSearchTools();
            await TestToolExecution();
            await TestParameterValidation();
            await TestToolStatistics();

            _logger.LogInformation("所有AI工具测试完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "AI工具测试失败");
            throw;
        }
    }

    /// <summary>
    /// 测试获取可用工具
    /// </summary>
    private async Task TestGetAvailableTools()
    {
        _logger.LogInformation("测试获取可用工具...");

        var tools = await _aiToolsService.GetAvailableToolsAsync();
        
        if (tools.Count == 0)
        {
            throw new Exception("未找到任何可用工具");
        }

        _logger.LogInformation($"找到 {tools.Count} 个可用工具");

        // 验证工具表单中的工具是否都存在
        var expectedTools = new[]
        {
            "search-codebase",
            "search-by-regex", 
            "view-files",
            "list-dir",
            "write-to-file",
            "update-file",
            "run-command",
            "open-preview",
            "web-search",
            "excel-automation",
            "blender-automation",
            "browser-automation"
        };

        foreach (var expectedTool in expectedTools)
        {
            var tool = tools.FirstOrDefault(t => t.Id == expectedTool);
            if (tool == null)
            {
                throw new Exception($"缺少预期工具: {expectedTool}");
            }
            _logger.LogInformation($"✓ 工具 {expectedTool} 存在");
        }

        _logger.LogInformation("获取可用工具测试通过");
    }

    /// <summary>
    /// 测试搜索工具
    /// </summary>
    private async Task TestSearchTools()
    {
        _logger.LogInformation("测试搜索工具...");

        // 测试搜索代码相关工具
        var codeTools = await _aiToolsService.SearchToolsAsync("代码");
        if (codeTools.Count == 0)
        {
            throw new Exception("搜索'代码'未找到任何工具");
        }
        _logger.LogInformation($"搜索'代码'找到 {codeTools.Count} 个工具");

        // 测试搜索文件相关工具
        var fileTools = await _aiToolsService.SearchToolsAsync("文件");
        if (fileTools.Count == 0)
        {
            throw new Exception("搜索'文件'未找到任何工具");
        }
        _logger.LogInformation($"搜索'文件'找到 {fileTools.Count} 个工具");

        // 测试空搜索
        var allTools = await _aiToolsService.SearchToolsAsync("");
        if (allTools.Count == 0)
        {
            throw new Exception("空搜索未返回任何工具");
        }
        _logger.LogInformation($"空搜索返回 {allTools.Count} 个工具");

        _logger.LogInformation("搜索工具测试通过");
    }

    /// <summary>
    /// 测试工具执行
    /// </summary>
    private async Task TestToolExecution()
    {
        _logger.LogInformation("测试工具执行...");

        // 测试代码库搜索工具
        var searchParameters = new Dictionary<string, object>
        {
            ["query"] = "测试查询",
            ["fileTypes"] = "cs",
            ["maxResults"] = 5
        };

        var searchResult = await _aiToolsService.ExecuteToolAsync("search-codebase", searchParameters);
        if (!searchResult.IsSuccess)
        {
            _logger.LogWarning($"代码库搜索工具执行失败: {searchResult.ErrorDetails}");
        }
        else
        {
            _logger.LogInformation($"✓ 代码库搜索工具执行成功，耗时 {searchResult.ExecutionTimeMs}ms");
        }

        // 测试目录浏览工具
        var listDirParameters = new Dictionary<string, object>
        {
            ["path"] = ".",
            ["recursive"] = false,
            ["showHidden"] = false
        };

        var listDirResult = await _aiToolsService.ExecuteToolAsync("list-dir", listDirParameters);
        if (!listDirResult.IsSuccess)
        {
            _logger.LogWarning($"目录浏览工具执行失败: {listDirResult.ErrorDetails}");
        }
        else
        {
            _logger.LogInformation($"✓ 目录浏览工具执行成功，耗时 {listDirResult.ExecutionTimeMs}ms");
        }

        // 测试网络搜索工具
        var webSearchParameters = new Dictionary<string, object>
        {
            ["query"] = "AI工具",
            ["maxResults"] = 3,
            ["language"] = "zh-CN"
        };

        var webSearchResult = await _aiToolsService.ExecuteToolAsync("web-search", webSearchParameters);
        if (!webSearchResult.IsSuccess)
        {
            _logger.LogWarning($"网络搜索工具执行失败: {webSearchResult.ErrorDetails}");
        }
        else
        {
            _logger.LogInformation($"✓ 网络搜索工具执行成功，耗时 {webSearchResult.ExecutionTimeMs}ms");
        }

        _logger.LogInformation("工具执行测试完成");
    }

    /// <summary>
    /// 测试参数验证
    /// </summary>
    private async Task TestParameterValidation()
    {
        _logger.LogInformation("测试参数验证...");

        // 测试缺少必需参数
        var invalidParameters = new Dictionary<string, object>();
        var validation = await _aiToolsService.ValidateParametersAsync("search-codebase", invalidParameters);
        
        if (validation.IsValid)
        {
            throw new Exception("参数验证应该失败但却通过了");
        }
        _logger.LogInformation($"✓ 缺少必需参数验证失败，错误: {string.Join(", ", validation.Errors)}");

        // 测试有效参数
        var validParameters = new Dictionary<string, object>
        {
            ["query"] = "测试查询"
        };
        var validValidation = await _aiToolsService.ValidateParametersAsync("search-codebase", validParameters);
        
        if (!validValidation.IsValid)
        {
            throw new Exception($"有效参数验证失败: {string.Join(", ", validValidation.Errors)}");
        }
        _logger.LogInformation("✓ 有效参数验证通过");

        _logger.LogInformation("参数验证测试通过");
    }

    /// <summary>
    /// 测试工具统计
    /// </summary>
    private async Task TestToolStatistics()
    {
        _logger.LogInformation("测试工具统计...");

        var statistics = await _aiToolsService.GetToolStatisticsAsync();
        
        if (statistics.TotalTools == 0)
        {
            throw new Exception("工具统计显示总工具数为0");
        }

        _logger.LogInformation($"工具统计信息:");
        _logger.LogInformation($"  总工具数: {statistics.TotalTools}");
        _logger.LogInformation($"  启用工具: {statistics.EnabledTools}");
        _logger.LogInformation($"  总使用次数: {statistics.TotalUsageCount}");
        _logger.LogInformation($"  平均成功率: {statistics.AverageSuccessRate:F1}%");
        _logger.LogInformation($"  类别统计: {string.Join(", ", statistics.CategoryCounts.Select(kvp => $"{kvp.Key}:{kvp.Value}"))}");

        _logger.LogInformation("工具统计测试通过");
    }

    /// <summary>
    /// 测试工具类别
    /// </summary>
    public async Task TestToolCategories()
    {
        _logger.LogInformation("测试工具类别...");

        var categories = new[] { "代码搜索", "文件操作", "开发辅助", "网络工具", "MCP集成" };

        foreach (var category in categories)
        {
            var tools = await _aiToolsService.GetToolsByCategoryAsync(category);
            _logger.LogInformation($"类别 '{category}' 包含 {tools.Count} 个工具");
        }

        _logger.LogInformation("工具类别测试完成");
    }

    /// <summary>
    /// 创建测试实例
    /// </summary>
    public static async Task<AIToolsTest> CreateAsync()
    {
        // 使用应用程序的服务提供者
        if (App.ServiceProvider == null)
        {
            throw new InvalidOperationException("应用程序服务提供者未初始化");
        }

        var aiToolsService = App.ServiceProvider.GetRequiredService<IAIToolsService>();
        var logger = App.ServiceProvider.GetRequiredService<ILogger<AIToolsTest>>();

        return new AIToolsTest(aiToolsService, logger);
    }

    /// <summary>
    /// 运行快速测试
    /// </summary>
    public static async Task RunQuickTestAsync()
    {
        try
        {
            var test = await CreateAsync();
            await test.TestGetAvailableTools();
            await test.TestToolCategories();
            
            Console.WriteLine("AI工具系统快速测试通过！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"AI工具系统测试失败: {ex.Message}");
            throw;
        }
    }
}

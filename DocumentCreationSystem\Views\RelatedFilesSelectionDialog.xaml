<Window x:Class="DocumentCreationSystem.Views.RelatedFilesSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="选择关联参考文件" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和说明 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,16">
            <TextBlock Text="选择章节重写参考文件" 
                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                       Margin="0,0,0,8"/>
            <TextBlock Text="请选择用于章节重写的参考文件。系统将基于这些文件和章节细纲重新生成章节内容。"
                       Style="{StaticResource MaterialDesignBody2TextBlock}"
                       TextWrapping="Wrap"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>

        <!-- 文件选择区域 -->
        <materialDesign:Card Grid.Row="1" Padding="16" Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 文件类型选择 -->
                <materialDesign:Card Grid.Row="0" Padding="12" Margin="0,0,0,16"
                                   Background="{DynamicResource MaterialDesignCardBackground}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Filter" VerticalAlignment="Center"
                                               Margin="0,0,8,0" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        <TextBlock Text="文件类型筛选：" VerticalAlignment="Center"
                                 FontWeight="Medium" Margin="0,0,16,0"/>
                        <CheckBox x:Name="OutlineFilesCheckBox" Content="大纲文件" IsChecked="True"
                                Margin="0,0,20,0" Style="{StaticResource MaterialDesignCheckBox}"/>
                        <CheckBox x:Name="ChapterFilesCheckBox" Content="章节文件" IsChecked="True"
                                Margin="0,0,20,0" Style="{StaticResource MaterialDesignCheckBox}"/>
                        <CheckBox x:Name="SettingFilesCheckBox" Content="设定文件" IsChecked="True"
                                Margin="0,0,20,0" Style="{StaticResource MaterialDesignCheckBox}"/>
                        <CheckBox x:Name="AllFilesCheckBox" Content="所有文件"
                                Style="{StaticResource MaterialDesignCheckBox}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 文件选择主区域 -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" MinWidth="300"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="1.5*" MinWidth="250"/>
                    </Grid.ColumnDefinitions>

                    <!-- 可选文件列表 -->
                    <materialDesign:Card Grid.Column="0" Padding="8" Margin="0,0,8,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <DockPanel Grid.Row="0" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="FileMultiple" DockPanel.Dock="Left"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"
                                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                <TextBlock Text="可选文件" DockPanel.Dock="Left"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                                <TextBlock x:Name="AvailableCountText" Text="(0 个文件)" DockPanel.Dock="Right"
                                         VerticalAlignment="Center" HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </DockPanel>

                            <ListView Grid.Row="1" x:Name="AvailableFilesListView"
                                      SelectionMode="Multiple"
                                      MinHeight="280">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="文件名" Width="180" DisplayMemberBinding="{Binding FileName}"/>
                                        <GridViewColumn Header="类型" Width="70" DisplayMemberBinding="{Binding FileType}"/>
                                        <GridViewColumn Header="大小" Width="70" DisplayMemberBinding="{Binding FileSize}"/>
                                        <GridViewColumn Header="修改时间" Width="110" DisplayMemberBinding="{Binding LastModified}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </Grid>
                    </materialDesign:Card>

                    <!-- 操作按钮区域 -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="12,0">
                        <Button x:Name="AddButton"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                ToolTip="添加选中文件"
                                Margin="0,0,0,12" Width="40" Height="40"
                                Click="AddButton_Click">
                            <materialDesign:PackIcon Kind="ArrowRight" Width="20" Height="20"/>
                        </Button>
                        <Button x:Name="RemoveButton"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                ToolTip="移除选中文件"
                                Margin="0,0,0,12" Width="40" Height="40"
                                Click="RemoveButton_Click">
                            <materialDesign:PackIcon Kind="ArrowLeft" Width="20" Height="20"/>
                        </Button>
                        <Button x:Name="AddAllButton"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                ToolTip="添加全部文件"
                                Margin="0,0,0,12" Width="40" Height="40"
                                Click="AddAllButton_Click">
                            <materialDesign:PackIcon Kind="ArrowRightBold" Width="20" Height="20"/>
                        </Button>
                        <Button x:Name="RemoveAllButton"
                                Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                                ToolTip="移除全部文件"
                                Width="40" Height="40"
                                Click="RemoveAllButton_Click">
                            <materialDesign:PackIcon Kind="ArrowLeftBold" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>

                    <!-- 已选文件列表 -->
                    <materialDesign:Card Grid.Column="2" Padding="8" Margin="8,0,0,0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <DockPanel Grid.Row="0" Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="FileCheck" DockPanel.Dock="Left"
                                                       VerticalAlignment="Center" Margin="0,0,8,0"
                                                       Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                                <TextBlock Text="已选文件" DockPanel.Dock="Left"
                                         Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                         VerticalAlignment="Center"/>
                                <TextBlock x:Name="SelectedCountText" Text="(0 个文件)" DockPanel.Dock="Right"
                                         VerticalAlignment="Center" HorizontalAlignment="Right"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </DockPanel>

                            <ListView Grid.Row="1" x:Name="SelectedFilesListView"
                                      SelectionMode="Multiple"
                                      MinHeight="280">
                                <ListView.View>
                                    <GridView>
                                        <GridViewColumn Header="文件名" Width="140" DisplayMemberBinding="{Binding FileName}"/>
                                        <GridViewColumn Header="类型" Width="60" DisplayMemberBinding="{Binding FileType}"/>
                                        <GridViewColumn Header="大小" Width="60" DisplayMemberBinding="{Binding FileSize}"/>
                                    </GridView>
                                </ListView.View>
                            </ListView>
                        </Grid>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- 选项设置 -->
        <materialDesign:Card Grid.Row="2" Padding="16" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="重写选项" Style="{StaticResource MaterialDesignSubtitle1TextBlock}" Margin="0,0,0,8"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,8,0">
                        <CheckBox x:Name="AutoSaveCheckBox" Content="自动保存重写结果" IsChecked="True" Margin="0,0,0,8"/>
                        <CheckBox x:Name="BackupOriginalCheckBox" Content="备份原始文件" IsChecked="True" Margin="0,0,0,8"/>
                        <CheckBox x:Name="EnableResumeCheckBox" Content="启用断点续写" IsChecked="True"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="8,0,0,0">
                        <TextBox x:Name="MaxContextLengthTextBox" 
                                 materialDesign:HintAssist.Hint="最大上下文长度"
                                 Text="4000"
                                 Margin="0,0,0,8"
                                 ToolTip="设置参考文件内容的最大字符数，避免上下文过长"/>
                        <ComboBox x:Name="RewriteStyleComboBox"
                                  materialDesign:HintAssist.Hint="重写风格"
                                  SelectedIndex="0"
                                  ToolTip="选择章节重写的风格倾向">
                            <ComboBoxItem Content="保持原风格"/>
                            <ComboBoxItem Content="优化表达"/>
                            <ComboBoxItem Content="增强细节"/>
                            <ComboBoxItem Content="紧凑叙述"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <TextBlock x:Name="StatusText" Text="请选择参考文件" 
                       VerticalAlignment="Center" 
                       Margin="0,0,16,0"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            <Button x:Name="CancelButton" Content="取消" 
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="0,0,8,0" 
                    Click="CancelButton_Click"/>
            <Button x:Name="ConfirmButton" Content="开始重写" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Click="ConfirmButton_Click"/>
        </StackPanel>
    </Grid>
</Window>

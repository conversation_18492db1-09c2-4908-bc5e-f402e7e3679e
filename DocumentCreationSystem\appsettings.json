{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Data Source=app.db"}, "AI": {"DefaultProvider": "Ollama", "DefaultModel": "llama3.2", "DefaultTemperature": 0.7, "DefaultMaxTokens": 2000, "EnableThinkingChain": true, "ZhipuAI": {"ApiKey": "", "BaseUrl": "https://open.bigmodel.cn/api/paas/v4", "DefaultModel": "glm-4"}, "Ollama": {"BaseUrl": "http://localhost:11434"}, "LMStudio": {"BaseUrl": "http://localhost:1234"}}, "Vector": {"DefaultProvider": "Ollama", "Timeout": 30, "Ollama": {"BaseUrl": "http://localhost:11434", "DefaultModel": "text-embedding-bge-m3"}, "LMStudio": {"BaseUrl": "http://localhost:1234", "DefaultModel": ""}, "Qdrant": {"Url": "http://localhost:6333", "CollectionName": "documents", "VectorSize": 1024}, "EmbeddingModel": {"Name": "BGE-M3", "Url": "http://localhost:8000/embeddings", "MaxTokens": 512}}, "FileMonitor": {"SupportedExtensions": [".docx", ".txt", ".md"], "DebounceInterval": 1000, "MaxFileSize": 104857600}, "Novel": {"DefaultTargetChapterCount": 1000, "DefaultTargetWordsPerChapter": 6500, "ConsistencyCheckInterval": 1000, "AutoSaveInterval": 30000, "BackupInterval": 300000}, "Application": {"Name": "文档管理及AI创作系统", "Version": "1.0.0", "DefaultProjectPath": "C:\\DocumentCreation\\Projects", "MaxRecentProjects": 10, "AutoBackup": true, "Theme": "Light"}}
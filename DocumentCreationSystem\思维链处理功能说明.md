# 思维链处理功能说明

## 功能概述

本系统现已支持AI模型的思维链（Chain of Thought）处理功能，能够智能识别和处理AI输出中的思考过程，确保最终用户只看到纯净的输出结果。

## 支持的格式

### 1. 推荐格式：`<think>` + `<output>`

```
<think>
这里是AI的思考过程...
分析问题的各个方面...
考虑不同的解决方案...
</think>

<output>
这里是最终的答案内容
</output>
```

### 2. 兼容格式：`<thinking>`

```
<thinking>
这里是AI的思考过程...
</thinking>

这里是最终的答案内容
```

### 3. Qwen 格式：`...</think>`

```
这里是AI的思考过程...
分析问题的各个方面...
考虑不同的解决方案...
得出最终结论...
</think>

这里是最终的答案内容
```

## 功能特性

### 1. 自动格式指导
- 系统会自动在提示词中添加格式指导
- 指导AI使用正确的思维链格式
- 确保输出符合预期格式

### 2. 智能内容提取
- 自动识别思维链标记
- 提取纯净的输出内容
- 过滤掉思考过程

### 3. 思维链开关控制
- 可在AI模型配置中启用/禁用思维链处理
- 支持动态配置更新
- 配置保存到文件中

### 4. 多种处理模式
- **完全移除**：只保留最终输出
- **隐藏标记**：保留内容但标记为隐藏
- **压缩摘要**：将思维链压缩为摘要
- **关键步骤**：只保留关键思考步骤

## 配置说明

### 在AI模型配置窗口中：

1. 打开"系统菜单" → "AI模型配置"
2. 在"通用配置"选项卡中找到"启用思维链处理"选项
3. 勾选该选项以启用思维链处理功能
4. 保存配置

### 配置文件格式：

```json
{
  "enableThinkingChain": true,
  "platform": "Ollama",
  "temperature": 0.7,
  "maxTokens": 2000
}
```

## 使用场景

### 1. 一键写书功能
- 在生成章节内容时自动处理思维链
- 确保生成的文本内容纯净
- 提高内容质量

### 2. 分步写书功能
- 在生成大纲、章节细纲时处理思维链
- 保持创作流程的连贯性
- 避免思考过程干扰最终内容

### 3. 文本润色和扩写
- 在润色文本时过滤思考过程
- 在扩写内容时保持内容纯净
- 提供更好的用户体验

## 技术实现

### 1. 正则表达式匹配
- 使用多种正则表达式模式识别思维链
- 支持大小写不敏感匹配
- 处理多行内容

### 2. 内容解析
- 解析思维链步骤
- 识别关键思考点
- 生成统计信息

### 3. 过滤处理
- 根据配置选择处理模式
- 应用相应的过滤规则
- 返回处理后的内容

## 注意事项

1. **模型兼容性**：不同AI模型对思维链格式的支持程度不同
2. **性能影响**：启用思维链处理会增加少量处理时间
3. **内容质量**：建议使用支持思维链的模型以获得更好的效果
4. **格式一致性**：推荐使用 `<think>` + `<output>` 格式以获得最佳体验

## 故障排除

### 问题：思维链内容仍然显示
**解决方案**：
1. 检查AI模型配置中是否启用了思维链处理
2. 确认AI模型输出使用了正确的格式
3. 重新加载配置或重启应用程序

### 问题：输出内容为空
**解决方案**：
1. 检查AI模型输出是否包含 `<output>` 标签
2. 确认思维链格式是否正确
3. 尝试禁用思维链处理进行测试

### 问题：配置不生效
**解决方案**：
1. 保存配置后重新加载AI服务
2. 检查配置文件是否正确保存
3. 重启应用程序以确保配置生效

using System.Windows;

namespace DocumentCreationSystem.Views
{
    /// <summary>
    /// TestCreateProjectDialog.xaml 的交互逻辑
    /// </summary>
    public partial class TestCreateProjectDialog : Window
    {
        public bool IsConfirmed { get; private set; }
        public string ProjectName => ProjectNameTextBox.Text;
        public string ProjectDescription => ProjectDescriptionTextBox.Text;

        public TestCreateProjectDialog()
        {
            InitializeComponent();
        }

        private void CreateButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(ProjectNameTextBox.Text))
            {
                MessageBox.Show("请输入项目名称", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            IsConfirmed = true;
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            IsConfirmed = false;
            DialogResult = false;
            Close();
        }
    }
}

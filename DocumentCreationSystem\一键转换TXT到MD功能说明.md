# 一键转换.txt到.md功能说明

## 功能概述

在项目导航页面新增了一键转换.txt文件到.md文件的功能，可以快速创建一个新的MD项目，保持原项目结构不变，只将其中的.txt文件转换为.md文件。

## 功能特点

### 🔄 智能转换
- **文件格式转换**：自动将所有.txt文件转换为.md文件
- **内容保持**：文件内容完全保持不变，只改变文件扩展名
- **结构保持**：完全保持原项目的目录结构
- **其他文件保持**：非.txt文件（如.docx、图片等）直接复制，保持原格式

### 📁 项目管理
- **新项目创建**：在原项目目录同级创建新的MD项目
- **命名规则**：新项目名称为"原项目名.MD"
- **路径管理**：新项目路径为"原项目路径.MD"
- **覆盖保护**：如果目标目录已存在，会询问是否覆盖

### 🛡️ 安全保护
- **确认机制**：转换前会显示详细信息并要求用户确认
- **覆盖警告**：目标目录存在时会警告并询问是否覆盖
- **错误处理**：完善的异常处理和错误提示
- **日志记录**：详细的操作日志记录

## 使用方法

### 1. 打开项目
首先需要在项目导航中打开一个项目。

### 2. 点击转换按钮
在项目导航页面的工具栏中，点击新增的转换按钮（文件替换图标）。

### 3. 确认转换信息
系统会显示转换确认对话框，包含：
- 原项目名称和路径
- 新MD项目名称和路径
- 转换说明

### 4. 等待转换完成
点击"是"确认后，系统会：
- 创建新的MD项目目录
- 递归复制所有文件和目录
- 将.txt文件转换为.md文件
- 显示转换完成提示

## 界面位置

转换按钮位于项目导航页面的工具栏中，具体位置：
```
[创建项目文件夹] [创建.docx文件] [打开项目文件夹] [刷新项目] [转换.txt到.md]
```

按钮特征：
- **图标**：文件替换图标（FileReplace）
- **提示文本**：一键转换.txt到.md (创建MD项目)
- **位置**：工具栏最右侧

## 转换示例

### 原项目结构
```
我的小说项目/
├── 第一章.txt
├── 第二章.txt
├── 角色设定/
│   ├── 主角.txt
│   └── 配角.txt
├── 世界观.docx
└── 封面.jpg
```

### 转换后的MD项目结构
```
我的小说项目.MD/
├── 第一章.md
├── 第二章.md
├── 角色设定/
│   ├── 主角.md
│   └── 配角.md
├── 世界观.docx
└── 封面.jpg
```

## 技术实现

### 核心方法
- `ConvertTxtToMd_Click`：按钮点击事件处理
- `ConvertProjectTxtToMdAsync`：主转换逻辑
- `CopyAndConvertDirectoryAsync`：递归目录复制和转换

### 转换逻辑
1. **验证项目**：检查当前是否有打开的项目
2. **用户确认**：显示转换信息并要求确认
3. **目录处理**：创建目标目录，处理已存在情况
4. **文件处理**：
   - .txt文件：读取内容，以.md扩展名保存
   - 其他文件：直接复制
5. **递归处理**：对所有子目录重复上述过程

### 编码处理
- 使用UTF-8编码读写文件，确保中文内容正确处理
- 保持原文件的换行符和格式

## 使用场景

### 1. Markdown写作
- 将现有的.txt文档转换为Markdown格式
- 便于使用Markdown编辑器进行编辑
- 支持Markdown语法高亮和预览

### 2. 文档发布
- 准备用于网站发布的Markdown文档
- 适配静态网站生成器（如Jekyll、Hugo等）
- 便于版本控制和协作

### 3. 格式统一
- 统一项目中的文档格式
- 便于后续的批量处理
- 提高文档的可读性和可维护性

## 注意事项

### ⚠️ 重要提醒
1. **备份原项目**：转换前建议备份原项目，虽然原项目不会被修改
2. **检查目标路径**：确认目标路径有足够的磁盘空间
3. **文件权限**：确保对目标路径有写入权限
4. **大项目处理**：大型项目转换可能需要较长时间

### 💡 使用建议
1. **先测试小项目**：建议先在小项目上测试功能
2. **检查转换结果**：转换完成后检查文件内容是否正确
3. **利用版本控制**：如果使用Git等版本控制，可以更安全地进行转换

## 错误处理

### 常见错误及解决方案

#### 1. 目录不存在
- **错误**：源项目目录不存在
- **解决**：确认项目路径正确，重新打开项目

#### 2. 权限不足
- **错误**：无法创建目标目录或写入文件
- **解决**：以管理员身份运行程序，或选择有权限的目标路径

#### 3. 磁盘空间不足
- **错误**：目标磁盘空间不足
- **解决**：清理磁盘空间或选择其他磁盘

#### 4. 文件被占用
- **错误**：某些文件正在被其他程序使用
- **解决**：关闭相关程序后重试

## 日志记录

系统会记录详细的转换日志，包括：
- 转换开始和结束时间
- 处理的文件数量
- 转换的文件列表
- 错误信息（如果有）

日志级别：
- **Info**：转换开始、完成等关键信息
- **Debug**：每个文件的处理详情
- **Error**：错误信息和异常详情

## 总结

一键转换.txt到.md功能为用户提供了便捷的文档格式转换工具，特别适合需要将现有文本文档转换为Markdown格式的场景。功能设计考虑了用户体验、数据安全和错误处理，确保转换过程的可靠性和易用性。

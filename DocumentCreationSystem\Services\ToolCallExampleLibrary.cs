using System;
using System.Collections.Generic;
using System.Linq;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 工具调用示例库 - 为AI模型提供丰富的工具使用示例
    /// </summary>
    public class ToolCallExampleLibrary
    {
        private readonly Dictionary<string, List<ToolCallExample>> _examples;

        public ToolCallExampleLibrary()
        {
            _examples = new Dictionary<string, List<ToolCallExample>>();
            InitializeExamples();
        }

        /// <summary>
        /// 获取指定工具的使用示例
        /// </summary>
        public List<ToolCallExample> GetExamplesForTool(string toolName)
        {
            return _examples.GetValueOrDefault(toolName, new List<ToolCallExample>());
        }

        /// <summary>
        /// 获取所有示例
        /// </summary>
        public Dictionary<string, List<ToolCallExample>> GetAllExamples()
        {
            return _examples;
        }

        /// <summary>
        /// 根据用户意图获取相关示例
        /// </summary>
        public List<ToolCallExample> GetExamplesByIntent(string userIntent)
        {
            var relevantExamples = new List<ToolCallExample>();
            var lowerIntent = userIntent.ToLower();

            foreach (var toolExamples in _examples.Values)
            {
                foreach (var example in toolExamples)
                {
                    if (example.UserIntent.ToLower().Contains(lowerIntent) ||
                        lowerIntent.Contains(example.UserIntent.ToLower()) ||
                        example.Tags.Any(tag => lowerIntent.Contains(tag.ToLower())))
                    {
                        relevantExamples.Add(example);
                    }
                }
            }

            return relevantExamples.OrderByDescending(e => e.Confidence).ToList();
        }

        private void InitializeExamples()
        {
            // 文件操作示例
            InitializeFileOperationExamples();
            
            // 搜索操作示例
            InitializeSearchExamples();
            
            // 代码分析示例
            InitializeCodeAnalysisExamples();
            
            // 网络操作示例
            InitializeWebOperationExamples();
            
            // 内容创作示例
            InitializeContentCreationExamples();
        }

        private void InitializeFileOperationExamples()
        {
            _examples["read_file"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "读取配置文件",
                    ToolCall = "[TOOL:read_file]config.json[/TOOL]",
                    ExpectedResult = "返回config.json文件的完整内容",
                    Tags = new[] { "文件", "读取", "配置" },
                    Confidence = 0.95
                },
                new ToolCallExample
                {
                    UserIntent = "查看README文件",
                    ToolCall = "[TOOL:read_file]README.md[/TOOL]",
                    ExpectedResult = "显示README.md文件内容",
                    Tags = new[] { "文件", "查看", "文档" },
                    Confidence = 0.9
                }
            };

            _examples["write_file"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "创建新的文本文件",
                    ToolCall = "[TOOL:write_file]test.txt|这是测试内容[/TOOL]",
                    ExpectedResult = "创建名为test.txt的文件，包含指定内容",
                    Tags = new[] { "文件", "创建", "写入" },
                    Confidence = 0.95
                },
                new ToolCallExample
                {
                    UserIntent = "保存配置信息到JSON文件",
                    ToolCall = "[TOOL:write_file]settings.json|{\"theme\":\"dark\",\"language\":\"zh-CN\"}[/TOOL]",
                    ExpectedResult = "创建包含JSON配置的settings.json文件",
                    Tags = new[] { "文件", "保存", "JSON", "配置" },
                    Confidence = 0.9
                }
            };

            _examples["list_files"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "查看当前目录的文件",
                    ToolCall = "[TOOL:list_files].[/TOOL]",
                    ExpectedResult = "列出当前目录下的所有文件和文件夹",
                    Tags = new[] { "目录", "列表", "文件" },
                    Confidence = 0.95
                },
                new ToolCallExample
                {
                    UserIntent = "查看项目文件夹内容",
                    ToolCall = "[TOOL:list_files]./src[/TOOL]",
                    ExpectedResult = "显示src目录下的文件列表",
                    Tags = new[] { "目录", "项目", "源码" },
                    Confidence = 0.9
                }
            };
        }

        private void InitializeSearchExamples()
        {
            _examples["smart_search"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "搜索包含AI的文件内容",
                    ToolCall = "[TOOL:smart_search]AI[/TOOL]",
                    ExpectedResult = "返回所有包含'AI'关键词的文件和相关内容",
                    Tags = new[] { "搜索", "内容", "关键词" },
                    Confidence = 0.9
                },
                new ToolCallExample
                {
                    UserIntent = "查找配置相关的内容",
                    ToolCall = "[TOOL:smart_search]配置[/TOOL]",
                    ExpectedResult = "搜索所有与配置相关的文件内容",
                    Tags = new[] { "搜索", "配置", "查找" },
                    Confidence = 0.85
                }
            };

            _examples["search-codebase"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "搜索代码库中的服务类",
                    ToolCall = "[AITOOL:search-codebase]{\"query\":\"service class\",\"fileTypes\":\"cs\"}[/AITOOL]",
                    ExpectedResult = "返回代码库中所有服务类的定义和位置",
                    Tags = new[] { "代码", "搜索", "类", "服务" },
                    Confidence = 0.95
                },
                new ToolCallExample
                {
                    UserIntent = "查找AI相关的方法",
                    ToolCall = "[AITOOL:search-codebase]{\"query\":\"AI method\",\"maxResults\":20}[/AITOOL]",
                    ExpectedResult = "搜索包含AI相关功能的方法",
                    Tags = new[] { "代码", "方法", "AI", "搜索" },
                    Confidence = 0.9
                }
            };
        }

        private void InitializeCodeAnalysisExamples()
        {
            _examples["analyze_story"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "分析故事内容的结构",
                    ToolCall = "[TOOL:analyze_story]这是一个关于勇敢少年的冒险故事...[/TOOL]",
                    ExpectedResult = "返回故事的结构分析，包括主题、角色、情节等",
                    Tags = new[] { "分析", "故事", "内容", "结构" },
                    Confidence = 0.9
                }
            };

            _examples["content-analyzer"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "分析文档内容质量",
                    ToolCall = "[AITOOL:content-analyzer]{\"type\":\"quality\",\"content\":\"文档内容...\"}[/AITOOL]",
                    ExpectedResult = "返回内容质量评估报告",
                    Tags = new[] { "分析", "质量", "文档", "评估" },
                    Confidence = 0.85
                }
            };
        }

        private void InitializeWebOperationExamples()
        {
            _examples["web-search"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "搜索C#编程教程",
                    ToolCall = "[AITOOL:web-search]{\"query\":\"C# 编程教程\",\"language\":\"zh-CN\"}[/AITOOL]",
                    ExpectedResult = "返回相关的C#编程教程链接和摘要",
                    Tags = new[] { "网络", "搜索", "教程", "编程" },
                    Confidence = 0.9
                },
                new ToolCallExample
                {
                    UserIntent = "查找最新技术资讯",
                    ToolCall = "[AITOOL:web-search]{\"query\":\"AI 技术 2024\",\"maxResults\":10}[/AITOOL]",
                    ExpectedResult = "获取最新的AI技术相关资讯",
                    Tags = new[] { "网络", "技术", "资讯", "AI" },
                    Confidence = 0.85
                }
            };
        }

        private void InitializeContentCreationExamples()
        {
            _examples["generate_content"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "生成技术文档",
                    ToolCall = "[TOOL:generate_content]技术文档|API使用说明|正式|2000[/TOOL]",
                    ExpectedResult = "生成专业的API使用说明文档",
                    Tags = new[] { "生成", "文档", "技术", "API" },
                    Confidence = 0.9
                }
            };

            _examples["ai-content-generator"] = new List<ToolCallExample>
            {
                new ToolCallExample
                {
                    UserIntent = "创作科幻小说片段",
                    ToolCall = "[AITOOL:ai-content-generator]{\"contentType\":\"小说\",\"theme\":\"科幻\",\"length\":1500}[/AITOOL]",
                    ExpectedResult = "生成科幻主题的小说内容",
                    Tags = new[] { "创作", "小说", "科幻", "生成" },
                    Confidence = 0.85
                }
            };
        }

        /// <summary>
        /// 获取工具调用的最佳实践建议
        /// </summary>
        public string GetBestPractices(string toolName)
        {
            var practices = new Dictionary<string, string>
            {
                ["read_file"] = "确保文件路径正确，使用相对路径时注意当前工作目录",
                ["write_file"] = "使用|分隔文件路径和内容，确保内容格式正确",
                ["search-codebase"] = "使用具体的搜索关键词，可以指定文件类型提高搜索精度",
                ["web-search"] = "提供清晰的搜索关键词，指定语言可以获得更相关的结果"
            };

            return practices.GetValueOrDefault(toolName, "遵循工具的参数格式要求，确保所有必需参数都已提供");
        }
    }

    /// <summary>
    /// 工具调用示例
    /// </summary>
    public class ToolCallExample
    {
        /// <summary>
        /// 用户意图描述
        /// </summary>
        public string UserIntent { get; set; } = string.Empty;

        /// <summary>
        /// 工具调用格式
        /// </summary>
        public string ToolCall { get; set; } = string.Empty;

        /// <summary>
        /// 预期结果
        /// </summary>
        public string ExpectedResult { get; set; } = string.Empty;

        /// <summary>
        /// 标签
        /// </summary>
        public string[] Tags { get; set; } = Array.Empty<string>();

        /// <summary>
        /// 置信度
        /// </summary>
        public double Confidence { get; set; } = 0.8;
    }
}

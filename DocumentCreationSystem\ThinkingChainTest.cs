using DocumentCreationSystem.Models;
using DocumentCreationSystem.Services;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem;

/// <summary>
/// 思维链处理功能测试类
/// </summary>
public class ThinkingChainTest
{
    private readonly ILogger<ThinkingChainService> _logger;
    private readonly ThinkingChainService _thinkingChainService;

    public ThinkingChainTest()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        _logger = loggerFactory.CreateLogger<ThinkingChainService>();
        _thinkingChainService = new ThinkingChainService(_logger);
    }

    /// <summary>
    /// 运行所有测试
    /// </summary>
    public void RunAllTests()
    {
        Console.WriteLine("=== 思维链处理功能测试 ===\n");

        TestThinkAndOutputFormat();
        TestThinkingFormat();
        TestOutputOnlyFormat();
        TestLegacyFormat();
        TestNoSpecialTags();
        TestFilterProcessing();
        TestQwenThinkingChainFormat();

        Console.WriteLine("=== 所有测试完成 ===");
    }

    private void TestThinkAndOutputFormat()
    {
        Console.WriteLine("测试1: <think> + <output> 格式");
        
        var input = @"<think>
我需要分析这个问题...
首先考虑用户的需求
然后制定解决方案
</think>

<output>
这是最终的答案内容，经过深思熟虑后得出的结论。
</output>";

        var result = _thinkingChainService.ParseThinkingChainResponse(input);
        
        Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
        Console.WriteLine($"思维链内容: {result.ThinkingChain.Substring(0, Math.Min(50, result.ThinkingChain.Length))}...");
        Console.WriteLine($"最终输出: {result.FinalOutput.Trim()}");
        Console.WriteLine($"思维链步骤数: {result.ThinkingSteps.Count}");
        Console.WriteLine();
    }

    private void TestThinkingFormat()
    {
        Console.WriteLine("测试2: <thinking> 格式");
        
        var input = @"<thinking>
这是传统的思考过程
分析问题的各个方面
</thinking>

<output>
这是最终的答案内容
</output>";

        var result = _thinkingChainService.ParseThinkingChainResponse(input);
        
        Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
        Console.WriteLine($"最终输出: {result.FinalOutput.Trim()}");
        Console.WriteLine();
    }

    private void TestOutputOnlyFormat()
    {
        Console.WriteLine("测试3: 仅 <output> 格式");
        
        var input = @"<output>
这是没有思维链的直接答案
</output>";

        var result = _thinkingChainService.ParseThinkingChainResponse(input);
        
        Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
        Console.WriteLine($"最终输出: {result.FinalOutput.Trim()}");
        Console.WriteLine();
    }

    private void TestLegacyFormat()
    {
        Console.WriteLine("测试4: 传统格式（无output标签）");
        
        var input = @"<thinking>
这是传统格式的思考过程
</thinking>

这是没有output标签的答案内容";

        var result = _thinkingChainService.ParseThinkingChainResponse(input);
        
        Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
        Console.WriteLine($"最终输出: {result.FinalOutput.Trim()}");
        Console.WriteLine();
    }

    private void TestNoSpecialTags()
    {
        Console.WriteLine("测试5: 无特殊标签");
        
        var input = "这是普通的文本内容，没有任何特殊标签";

        var result = _thinkingChainService.ParseThinkingChainResponse(input);
        
        Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
        Console.WriteLine($"最终输出: {result.FinalOutput}");
        Console.WriteLine();
    }

    private void TestFilterProcessing()
    {
        Console.WriteLine("测试6: 过滤处理");
        
        var input = @"<think>
这是思考过程
分析问题
制定方案
</think>

<output>
这是最终答案
</output>";

        var filter = new ThinkingChainFilter
        {
            EnableFilter = true,
            Mode = FilterMode.RemoveThinkingChain
        };

        var result = _thinkingChainService.ProcessThinkingChainResponse(input, filter);
        
        Console.WriteLine($"处理状态: {result.Status}");
        Console.WriteLine($"包含思维链: {result.ContainsThinkingChain}");
        Console.WriteLine($"处理后内容: {result.ProcessedContent.Trim()}");
        Console.WriteLine($"思维链步骤数: {result.ThinkingStepsCount}");
        Console.WriteLine();
    }

    /// <summary>
    /// 测试 Qwen 思维链格式 (...</think>)
    /// </summary>
    private void TestQwenThinkingChainFormat()
    {
        Console.WriteLine("=== 测试 Qwen 思维链格式 (...</think>) ===");

        var input = @"我需要分析这个问题。

首先，让我理解用户的需求。用户想要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。

然后，我需要查看当前的实现，看看如何添加这种新格式的支持。

最后，我需要修改正则表达式和相关的处理逻辑。
</think>

根据您的需求，我需要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。这是 Qwen 模型使用的思维链格式。";

        var filter = new ThinkingChainFilter
        {
            EnableFilter = true,
            Mode = FilterMode.RemoveThinkingChain
        };

        var result = _thinkingChainService.ProcessThinkingChainResponse(input, filter);

        Console.WriteLine($"处理状态: {result.Status}");
        Console.WriteLine($"包含思维链: {result.ContainsThinkingChain}");
        Console.WriteLine($"处理后内容: {result.ProcessedContent.Trim()}");
        Console.WriteLine($"思维链步骤数: {result.ThinkingStepsCount}");
        Console.WriteLine();

        // 测试思维链内容提取
        if (result.OriginalResponse.HasThinkingChain)
        {
            Console.WriteLine("提取的思维链内容:");
            Console.WriteLine(result.OriginalResponse.ThinkingChain);
            Console.WriteLine();
        }
    }

    /// <summary>
    /// 测试思维链检测功能
    /// </summary>
    public void TestContainsThinkingChain()
    {
        Console.WriteLine("=== 思维链检测测试 ===\n");

        var testCases = new[]
        {
            ("<think>思考内容</think><output>答案</output>", true),
            ("<thinking>思考内容</thinking>", true),
            ("普通文本内容", false),
            ("让我想想这个问题...", true),
            ("首先分析，其次考虑，最后总结", true)
        };

        foreach (var (input, expected) in testCases)
        {
            var result = _thinkingChainService.ContainsThinkingChain(input);
            var status = result == expected ? "✓" : "✗";
            Console.WriteLine($"{status} 输入: {input.Substring(0, Math.Min(30, input.Length))}...");
            Console.WriteLine($"   预期: {expected}, 实际: {result}");
            Console.WriteLine();
        }
    }
}

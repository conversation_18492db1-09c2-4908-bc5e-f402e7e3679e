-- 创建Projects表
CREATE TABLE IF NOT EXISTS "Projects" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Projects" PRIMARY KEY AUTOINCREMENT,
    "Name" TEXT NOT NULL,
    "Type" TEXT NOT NULL,
    "Description" TEXT NULL,
    "RootPath" TEXT NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "Configuration" TEXT NULL,
    "VectorIndexed" INTEGER NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'Active'
);

-- 创建Documents表
CREATE TABLE IF NOT EXISTS "Documents" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Documents" PRIMARY KEY AUTOINCREMENT,
    "ProjectId" INTEGER NOT NULL,
    "FileName" TEXT NOT NULL,
    "RelativePath" TEXT NOT NULL,
    "Format" TEXT NOT NULL,
    "FileSize" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "FileModifiedAt" TEXT NOT NULL,
    "Summary" TEXT NULL,
    "VectorId" TEXT NULL,
    "IsVectorized" INTEGER NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'Active',
    "WordCount" INTEGER NOT NULL,
    "Tags" TEXT NULL,
    CONSTRAINT "FK_Documents_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE
);

-- 创建NovelProjects表
CREATE TABLE IF NOT EXISTS "NovelProjects" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_NovelProjects" PRIMARY KEY AUTOINCREMENT,
    "ProjectId" INTEGER NOT NULL,
    "Title" TEXT NOT NULL,
    "CreativeDirection" TEXT NULL,
    "WorldSetting" TEXT NULL,
    "TargetChapterCount" INTEGER NOT NULL,
    "CurrentChapterCount" INTEGER NOT NULL,
    "TargetWordsPerChapter" INTEGER NOT NULL,
    "TotalWordCount" INTEGER NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'Planning',
    "OverallOutline" TEXT NULL,
    "VolumeOutlines" TEXT NULL,
    "Characters" TEXT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "LastWritingTime" TEXT NULL,
    CONSTRAINT "FK_NovelProjects_Projects_ProjectId" FOREIGN KEY ("ProjectId") REFERENCES "Projects" ("Id") ON DELETE CASCADE
);

-- 创建VectorRecords表
CREATE TABLE IF NOT EXISTS "VectorRecords" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_VectorRecords" PRIMARY KEY AUTOINCREMENT,
    "DocumentId" INTEGER NOT NULL,
    "VectorId" TEXT NOT NULL,
    "TextContent" TEXT NOT NULL,
    "StartPosition" INTEGER NOT NULL,
    "EndPosition" INTEGER NOT NULL,
    "VectorDimension" INTEGER NOT NULL,
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "IndexStatus" TEXT NOT NULL DEFAULT 'Pending',
    "ErrorMessage" TEXT NULL,
    "ContentHash" TEXT NULL,
    "Metadata" TEXT NULL,
    CONSTRAINT "FK_VectorRecords_Documents_DocumentId" FOREIGN KEY ("DocumentId") REFERENCES "Documents" ("Id") ON DELETE CASCADE
);

-- 创建Chapters表
CREATE TABLE IF NOT EXISTS "Chapters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Chapters" PRIMARY KEY AUTOINCREMENT,
    "NovelProjectId" INTEGER NOT NULL,
    "DocumentId" INTEGER NULL,
    "ChapterNumber" INTEGER NOT NULL,
    "Title" TEXT NOT NULL,
    "Outline" TEXT NULL,
    "Summary" TEXT NULL,
    "Content" TEXT NULL,
    "ContentPath" TEXT NULL,
    "WordCount" INTEGER NOT NULL,
    "TargetWordCount" INTEGER NOT NULL,
    "Status" TEXT NOT NULL DEFAULT 'Planning',
    "CreatedAt" TEXT NOT NULL,
    "StartedAt" TEXT NULL,
    "CompletedAt" TEXT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "ConsistencyCheckLog" TEXT NULL,
    "Progress" INTEGER NOT NULL,
    "IsAutoGenerated" INTEGER NOT NULL,
    "SortOrder" INTEGER NOT NULL,
    CONSTRAINT "FK_Chapters_Documents_DocumentId" FOREIGN KEY ("DocumentId") REFERENCES "Documents" ("Id") ON DELETE SET NULL,
    CONSTRAINT "FK_Chapters_NovelProjects_NovelProjectId" FOREIGN KEY ("NovelProjectId") REFERENCES "NovelProjects" ("Id") ON DELETE CASCADE
);

-- 创建Characters表
CREATE TABLE IF NOT EXISTS "Characters" (
    "Id" INTEGER NOT NULL CONSTRAINT "PK_Characters" PRIMARY KEY AUTOINCREMENT,
    "NovelProjectId" INTEGER NOT NULL,
    "Name" TEXT NOT NULL,
    "Description" TEXT NULL,
    "Attributes" TEXT NULL,
    "Skills" TEXT NULL,
    "Equipment" TEXT NULL,
    "Type" TEXT NOT NULL DEFAULT 'Minor',
    "Importance" INTEGER NOT NULL,
    "FirstAppearanceChapter" INTEGER NULL,
    "LastAppearanceChapter" INTEGER NULL,
    "LastUpdatedChapter" INTEGER NULL,
    "Status" TEXT NOT NULL DEFAULT 'Active',
    "CreatedAt" TEXT NOT NULL,
    "UpdatedAt" TEXT NOT NULL,
    "UpdateHistory" TEXT NULL,
    CONSTRAINT "FK_Characters_NovelProjects_NovelProjectId" FOREIGN KEY ("NovelProjectId") REFERENCES "NovelProjects" ("Id") ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS "IX_Chapters_ChapterNumber" ON "Chapters" ("ChapterNumber");
CREATE INDEX IF NOT EXISTS "IX_Chapters_DocumentId" ON "Chapters" ("DocumentId");
CREATE INDEX IF NOT EXISTS "IX_Chapters_NovelProjectId" ON "Chapters" ("NovelProjectId");
CREATE UNIQUE INDEX IF NOT EXISTS "IX_Chapters_NovelProjectId_ChapterNumber" ON "Chapters" ("NovelProjectId", "ChapterNumber");
CREATE INDEX IF NOT EXISTS "IX_Characters_Name" ON "Characters" ("Name");
CREATE INDEX IF NOT EXISTS "IX_Characters_NovelProjectId" ON "Characters" ("NovelProjectId");
CREATE INDEX IF NOT EXISTS "IX_Documents_FileName" ON "Documents" ("FileName");
CREATE INDEX IF NOT EXISTS "IX_Documents_ProjectId" ON "Documents" ("ProjectId");
CREATE INDEX IF NOT EXISTS "IX_Documents_VectorId" ON "Documents" ("VectorId");
CREATE UNIQUE INDEX IF NOT EXISTS "IX_NovelProjects_ProjectId" ON "NovelProjects" ("ProjectId");
CREATE INDEX IF NOT EXISTS "IX_Projects_Name" ON "Projects" ("Name");
CREATE INDEX IF NOT EXISTS "IX_Projects_Type" ON "Projects" ("Type");
CREATE INDEX IF NOT EXISTS "IX_VectorRecords_DocumentId" ON "VectorRecords" ("DocumentId");
CREATE UNIQUE INDEX IF NOT EXISTS "IX_VectorRecords_VectorId" ON "VectorRecords" ("VectorId");

using System;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 运行内容过滤测试的控制台程序
    /// </summary>
    public class RunContentFilterTest
    {
        public static void Main(string[] args)
        {
            try
            {
                var tester = new TestChapterContentFilter();
                tester.RunAllTests();
                
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}

using System;
using System.Collections.Generic;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 论文生成请求
    /// </summary>
    public class PaperGenerationRequest
    {
        /// <summary>
        /// 论文标题（可为空，由AI生成）
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 论文类型
        /// </summary>
        public string PaperType { get; set; } = string.Empty;

        /// <summary>
        /// 学科领域
        /// </summary>
        public string Field { get; set; } = string.Empty;

        /// <summary>
        /// 目标字数
        /// </summary>
        public int TargetWordCount { get; set; } = 8000;

        /// <summary>
        /// 写作风格
        /// </summary>
        public string WritingStyle { get; set; } = "学术严谨";

        /// <summary>
        /// 引用风格
        /// </summary>
        public string CitationStyle { get; set; } = "APA";

        /// <summary>
        /// 创新度 (0.1-1.0)
        /// </summary>
        public float Creativity { get; set; } = 0.7f;

        /// <summary>
        /// 素材文件路径列表
        /// </summary>
        public List<string> MaterialFiles { get; set; } = new();

        /// <summary>
        /// 论文结构设置
        /// </summary>
        public PaperStructure Structure { get; set; } = new();

        /// <summary>
        /// 输出格式
        /// </summary>
        public string OutputFormat { get; set; } = "docx";

        /// <summary>
        /// 是否保存到项目文件夹
        /// </summary>
        public bool SaveToProject { get; set; } = true;

        /// <summary>
        /// 用户附加要求
        /// </summary>
        public string? UserRequirements { get; set; }
    }

    /// <summary>
    /// 论文结构设置
    /// </summary>
    public class PaperStructure
    {
        /// <summary>
        /// 包含摘要
        /// </summary>
        public bool IncludeAbstract { get; set; } = true;

        /// <summary>
        /// 包含关键词
        /// </summary>
        public bool IncludeKeywords { get; set; } = true;

        /// <summary>
        /// 包含引言
        /// </summary>
        public bool IncludeIntroduction { get; set; } = true;

        /// <summary>
        /// 包含文献综述
        /// </summary>
        public bool IncludeLiteratureReview { get; set; } = true;

        /// <summary>
        /// 包含研究方法
        /// </summary>
        public bool IncludeMethodology { get; set; } = true;

        /// <summary>
        /// 包含结果分析
        /// </summary>
        public bool IncludeResults { get; set; } = true;

        /// <summary>
        /// 包含讨论
        /// </summary>
        public bool IncludeDiscussion { get; set; } = true;

        /// <summary>
        /// 包含结论
        /// </summary>
        public bool IncludeConclusion { get; set; } = true;

        /// <summary>
        /// 包含参考文献
        /// </summary>
        public bool IncludeReferences { get; set; } = true;
    }

    /// <summary>
    /// 论文生成结果
    /// </summary>
    public class PaperGenerationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 论文标题
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// 论文内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 生成的章节列表
        /// </summary>
        public List<PaperSection> Sections { get; set; } = new();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 生成统计信息
        /// </summary>
        public PaperGenerationStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// 论文章节
    /// </summary>
    public class PaperSection
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 字数
        /// </summary>
        public int WordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// 论文生成统计信息
    /// </summary>
    public class PaperGenerationStatistics
    {
        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public TimeSpan GenerationTime { get; set; }

        /// <summary>
        /// 使用的素材文件数量
        /// </summary>
        public int MaterialFilesCount { get; set; }

        /// <summary>
        /// 生成的章节数量
        /// </summary>
        public int SectionsCount { get; set; }

        /// <summary>
        /// 素材利用率
        /// </summary>
        public float MaterialUtilizationRate { get; set; }
    }

    /// <summary>
    /// 素材分析结果
    /// </summary>
    public class MaterialAnalysisResult
    {
        /// <summary>
        /// 主要主题
        /// </summary>
        public List<string> MainTopics { get; set; } = new();

        /// <summary>
        /// 关键概念
        /// </summary>
        public List<string> KeyConcepts { get; set; } = new();

        /// <summary>
        /// 研究方法
        /// </summary>
        public List<string> ResearchMethods { get; set; } = new();

        /// <summary>
        /// 重要发现
        /// </summary>
        public List<string> KeyFindings { get; set; } = new();

        /// <summary>
        /// 参考文献信息
        /// </summary>
        public List<string> References { get; set; } = new();

        /// <summary>
        /// 素材摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// 建议的论文结构
        /// </summary>
        public List<string> SuggestedStructure { get; set; } = new();
    }

    /// <summary>
    /// 论文大纲
    /// </summary>
    public class PaperOutline
    {
        /// <summary>
        /// 论文标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 摘要大纲
        /// </summary>
        public string AbstractOutline { get; set; } = string.Empty;

        /// <summary>
        /// 章节大纲列表
        /// </summary>
        public List<SectionOutline> SectionOutlines { get; set; } = new();

        /// <summary>
        /// 预估字数分配
        /// </summary>
        public Dictionary<string, int> WordCountAllocation { get; set; } = new();
    }

    /// <summary>
    /// 章节大纲
    /// </summary>
    public class SectionOutline
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 章节要点
        /// </summary>
        public List<string> KeyPoints { get; set; } = new();

        /// <summary>
        /// 预估字数
        /// </summary>
        public int EstimatedWordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 相关素材引用
        /// </summary>
        public List<string> RelatedMaterials { get; set; } = new();
    }
}

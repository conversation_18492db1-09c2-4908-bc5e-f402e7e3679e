# 分步执行写书功能说明

## 功能概述

分步执行写书功能是对原有一键写书功能的重要增强，允许用户完全控制书籍创作的每个阶段，实现更精细化的创作管理。

## 主要特性

### 1. 分步骤控制
- **步骤1：生成全书大纲** - 根据用户输入的书籍基本信息生成完整的全书大纲
- **步骤2：生成卷宗大纲** - 基于全书大纲生成各卷的详细大纲
- **步骤3：生成章节细纲** - 支持逐卷生成章节细纲，用户可选择特定卷宗
- **步骤4：生成章节正文** - 基于章节细纲和前文内容生成具体章节正文
- **额外功能：世界设定生成** - 独立的世界设定生成功能

### 2. 状态管理
- **执行状态持久化** - 自动保存执行状态到项目文件夹
- **状态加载** - 支持从项目文件夹加载之前的执行状态
- **进度跟踪** - 实时显示各步骤的完成状态
- **依赖检查** - 确保后续步骤只有在前置步骤完成后才能执行

### 3. 智能内容管理
- **上下文关联** - 章节正文生成时自动引用前面3章的内容
- **细纲更新** - 生成章节正文后自动更新下一章的细纲
- **文件组织** - 自动创建规范的文件夹结构保存各类内容

## 使用方法

### 1. 启动功能
1. 在主界面右侧AI助手功能区域点击"分步执行写书"按钮
2. 系统会打开分步执行写书窗口

### 2. 设置书籍基本信息
在窗口上方的"书籍基本信息设置"区域填写：
- **书籍标题**：小说的名称
- **卷数**：计划分为多少卷（5/10/15/20卷可选）
- **总章节数**：整本书的章节总数（默认1000章）
- **每章字数**：每章的目标字数（默认6500字）
- **创作方向和基本设定**：详细描述小说的类型、背景、主要设定等

### 3. 分步执行创作

#### 步骤1：生成全书大纲
1. 点击"生成全书大纲"按钮
2. 系统会基于书籍基本信息生成完整的全书大纲
3. 大纲会显示在右侧预览区域，并自动保存到项目的`Outlines/overall_outline.txt`文件

#### 步骤2：生成卷宗大纲
1. 完成全书大纲后，"生成卷宗大纲"按钮会变为可用
2. 点击按钮开始生成各卷的详细大纲
3. 系统会根据设定的卷数逐一生成每卷的大纲
4. 卷宗大纲保存到`Outlines/Volumes/`文件夹中

#### 步骤3：生成章节细纲
1. 完成卷宗大纲后，章节细纲功能变为可用
2. 在下拉框中选择要生成细纲的卷宗
3. 点击"生成选定卷的章节细纲"按钮
4. 系统会为该卷的所有章节生成详细细纲
5. 章节细纲保存到`Outlines/Chapters/`文件夹中

#### 步骤4：生成章节正文
1. 完成章节细纲后，正文生成功能变为可用
2. 在文本框中输入要生成正文的章节号
3. 点击"生成指定章节正文"按钮
4. 系统会基于该章节的细纲和前文内容生成正文
5. 正文保存到`Chapters/`文件夹中

#### 世界设定生成
- 可在任何时候点击"生成世界设定"按钮
- 系统会生成详细的世界设定并保存到`Settings/`文件夹

### 4. 状态管理
- **自动保存**：每完成一个步骤，系统会自动保存执行状态
- **手动保存**：点击"保存状态"按钮手动保存当前状态
- **加载状态**：点击"加载状态"按钮从项目文件夹加载之前的状态

## 文件结构

分步执行功能会在项目文件夹中创建以下结构：

```
项目文件夹/
├── Outlines/                    # 大纲文件夹
│   ├── overall_outline.txt      # 全书大纲
│   ├── Volumes/                 # 卷宗大纲
│   │   ├── volume_01_outline.txt
│   │   ├── volume_02_outline.txt
│   │   └── ...
│   └── Chapters/                # 章节细纲
│       ├── chapter_001_outline.txt
│       ├── chapter_002_outline.txt
│       └── ...
├── Chapters/                    # 章节正文
│   ├── 书名_第001章.txt
│   ├── 书名_第002章.txt
│   └── ...
├── Settings/                    # 世界设定
│   └── world_setting.json
└── step_execution_state.json   # 执行状态文件
```

## 优势特点

### 1. 灵活控制
- 用户可以在任何步骤暂停，检查结果后再继续
- 支持重复执行某个步骤以优化结果
- 可以跳过某些步骤或调整执行顺序

### 2. 质量保证
- 每个步骤都有明确的输入和输出
- 后续步骤会参考前面步骤的结果，确保一致性
- 支持人工干预和调整

### 3. 进度可视化
- 清晰的步骤状态指示器
- 实时进度显示
- 完整的状态摘要

### 4. 数据安全
- 所有内容都保存到本地文件
- 支持状态恢复，避免意外丢失
- 规范的文件组织便于管理

## 注意事项

1. **项目依赖**：使用前需要先创建或打开一个项目
2. **AI模型配置**：确保已正确配置AI模型，功能依赖AI服务
3. **步骤顺序**：建议按照设计的步骤顺序执行，以获得最佳效果
4. **文件管理**：生成的文件会自动保存，注意项目文件夹的磁盘空间
5. **网络连接**：功能需要网络连接以访问AI服务

## 与一键写书的区别

| 特性 | 一键写书 | 分步执行写书 |
|------|----------|-------------|
| 控制粒度 | 全自动 | 分步可控 |
| 中途干预 | 不支持 | 完全支持 |
| 状态保存 | 临时 | 持久化 |
| 结果检查 | 最终检查 | 每步检查 |
| 灵活性 | 低 | 高 |
| 适用场景 | 快速原型 | 精细创作 |

分步执行写书功能为用户提供了更专业、更灵活的创作工具，适合对创作质量有较高要求的用户使用。

using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 插件接口 - 所有插件必须实现的基础接口
    /// </summary>
    public interface IPlugin : IAsyncDisposable
    {
        /// <summary>
        /// 插件唯一标识符
        /// </summary>
        string Id { get; }

        /// <summary>
        /// 插件名称
        /// </summary>
        string Name { get; }

        /// <summary>
        /// 插件版本
        /// </summary>
        string Version { get; }

        /// <summary>
        /// 插件描述
        /// </summary>
        string Description { get; }

        /// <summary>
        /// 插件作者
        /// </summary>
        string Author { get; }

        /// <summary>
        /// 插件状态
        /// </summary>
        PluginStatus Status { get; }

        /// <summary>
        /// 初始化插件
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// 启用插件
        /// </summary>
        Task EnableAsync();

        /// <summary>
        /// 禁用插件
        /// </summary>
        Task DisableAsync();

        /// <summary>
        /// 执行插件功能
        /// </summary>
        /// <param name="action">动作名称</param>
        /// <param name="parameters">参数</param>
        /// <returns>执行结果</returns>
        Task<PluginExecutionResult> ExecuteAsync(string action, Dictionary<string, object> parameters);

        /// <summary>
        /// 获取插件支持的动作列表
        /// </summary>
        Task<List<PluginActionInfo>> GetSupportedActionsAsync();

        /// <summary>
        /// 配置更新时的回调
        /// </summary>
        Task OnConfigurationUpdatedAsync(PluginConfiguration configuration);

        /// <summary>
        /// 获取插件健康状态
        /// </summary>
        Task<PluginHealthStatus> GetHealthStatusAsync();
    }

    /// <summary>
    /// 插件健康状态
    /// </summary>
    public class PluginHealthStatus
    {
        public bool IsHealthy { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
        public DateTime LastChecked { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 高级插件接口 - 提供更多高级功能
    /// </summary>
    public interface IAdvancedPlugin : IPlugin
    {
        /// <summary>
        /// 注册插件钩子
        /// </summary>
        Task RegisterHooksAsync(IPluginEcosystemService pluginService);

        /// <summary>
        /// 处理系统事件
        /// </summary>
        Task OnSystemEventAsync(string eventType, Dictionary<string, object> eventData);

        /// <summary>
        /// 获取插件统计信息
        /// </summary>
        Task<PluginStatistics> GetStatisticsAsync();

        /// <summary>
        /// 备份插件数据
        /// </summary>
        Task<string> BackupDataAsync();

        /// <summary>
        /// 恢复插件数据
        /// </summary>
        Task RestoreDataAsync(string backupData);
    }

    /// <summary>
    /// 插件统计信息
    /// </summary>
    public class PluginStatistics
    {
        public int TotalExecutions { get; set; }
        public int SuccessfulExecutions { get; set; }
        public int FailedExecutions { get; set; }
        public TimeSpan TotalExecutionTime { get; set; }
        public TimeSpan AverageExecutionTime { get; set; }
        public DateTime LastExecution { get; set; }
        public Dictionary<string, int> ActionCounts { get; set; } = new();
        public Dictionary<string, object> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// UI插件接口 - 提供用户界面扩展功能
    /// </summary>
    public interface IUIPlugin : IPlugin
    {
        /// <summary>
        /// 获取UI组件
        /// </summary>
        Task<List<UIComponent>> GetUIComponentsAsync();

        /// <summary>
        /// 处理UI事件
        /// </summary>
        Task OnUIEventAsync(string eventType, Dictionary<string, object> eventData);

        /// <summary>
        /// 获取菜单项
        /// </summary>
        Task<List<MenuItem>> GetMenuItemsAsync();

        /// <summary>
        /// 获取工具栏按钮
        /// </summary>
        Task<List<ToolbarButton>> GetToolbarButtonsAsync();
    }

    /// <summary>
    /// UI组件
    /// </summary>
    public class UIComponent
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public Dictionary<string, object> Properties { get; set; } = new();
        public string? Template { get; set; }
        public object? DataContext { get; set; }
    }

    /// <summary>
    /// 菜单项
    /// </summary>
    public class MenuItem
    {
        public string Id { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public string? Command { get; set; }
        public string? Shortcut { get; set; }
        public List<MenuItem> SubItems { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
        public bool IsVisible { get; set; } = true;
    }

    /// <summary>
    /// 工具栏按钮
    /// </summary>
    public class ToolbarButton
    {
        public string Id { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;
        public string? Icon { get; set; }
        public string? Command { get; set; }
        public string? Tooltip { get; set; }
        public bool IsEnabled { get; set; } = true;
        public bool IsVisible { get; set; } = true;
        public int Order { get; set; } = 0;
    }

    /// <summary>
    /// AI插件接口 - 提供AI功能扩展
    /// </summary>
    public interface IAIPlugin : IPlugin
    {
        /// <summary>
        /// 处理AI请求
        /// </summary>
        Task<AIPluginResponse> ProcessAIRequestAsync(AIPluginRequest request);

        /// <summary>
        /// 获取支持的AI功能
        /// </summary>
        Task<List<AICapability>> GetAICapabilitiesAsync();

        /// <summary>
        /// 训练或微调模型
        /// </summary>
        Task<TrainingResult> TrainModelAsync(TrainingData trainingData);

        /// <summary>
        /// 评估模型性能
        /// </summary>
        Task<ModelEvaluation> EvaluateModelAsync(EvaluationData evaluationData);
    }

    /// <summary>
    /// AI插件请求
    /// </summary>
    public class AIPluginRequest
    {
        public string RequestType { get; set; } = string.Empty;
        public string Input { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public string? Context { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// AI插件响应
    /// </summary>
    public class AIPluginResponse
    {
        public bool Success { get; set; } = false;
        public string Output { get; set; } = string.Empty;
        public float Confidence { get; set; } = 0f;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
    }

    /// <summary>
    /// AI能力
    /// </summary>
    public class AICapability
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public List<string> SupportedInputTypes { get; set; } = new();
        public List<string> SupportedOutputTypes { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 训练数据
    /// </summary>
    public class TrainingData
    {
        public List<TrainingExample> Examples { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public string? ValidationData { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 训练示例
    /// </summary>
    public class TrainingExample
    {
        public string Input { get; set; } = string.Empty;
        public string ExpectedOutput { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
        public float Weight { get; set; } = 1.0f;
    }

    /// <summary>
    /// 训练结果
    /// </summary>
    public class TrainingResult
    {
        public bool Success { get; set; } = false;
        public string? ModelId { get; set; }
        public float FinalLoss { get; set; }
        public float Accuracy { get; set; }
        public TimeSpan TrainingTime { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    /// <summary>
    /// 评估数据
    /// </summary>
    public class EvaluationData
    {
        public List<EvaluationExample> Examples { get; set; } = new();
        public List<string> Metrics { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// 评估示例
    /// </summary>
    public class EvaluationExample
    {
        public string Input { get; set; } = string.Empty;
        public string ExpectedOutput { get; set; } = string.Empty;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 模型评估结果
    /// </summary>
    public class ModelEvaluation
    {
        public float OverallScore { get; set; }
        public Dictionary<string, float> MetricScores { get; set; } = new();
        public List<EvaluationResult> DetailedResults { get; set; } = new();
        public Dictionary<string, object> Summary { get; set; } = new();
        public DateTime EvaluatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 评估结果
    /// </summary>
    public class EvaluationResult
    {
        public string Input { get; set; } = string.Empty;
        public string ExpectedOutput { get; set; } = string.Empty;
        public string ActualOutput { get; set; } = string.Empty;
        public float Score { get; set; }
        public Dictionary<string, float> MetricScores { get; set; } = new();
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 数据插件接口 - 提供数据处理功能
    /// </summary>
    public interface IDataPlugin : IPlugin
    {
        /// <summary>
        /// 导入数据
        /// </summary>
        Task<DataImportResult> ImportDataAsync(DataImportRequest request);

        /// <summary>
        /// 导出数据
        /// </summary>
        Task<DataExportResult> ExportDataAsync(DataExportRequest request);

        /// <summary>
        /// 转换数据格式
        /// </summary>
        Task<DataTransformResult> TransformDataAsync(DataTransformRequest request);

        /// <summary>
        /// 验证数据
        /// </summary>
        Task<DataValidationResult> ValidateDataAsync(DataValidationRequest request);
    }

    /// <summary>
    /// 数据导入请求
    /// </summary>
    public class DataImportRequest
    {
        public string Source { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public Dictionary<string, object> Options { get; set; } = new();
        public string? TargetLocation { get; set; }
    }

    /// <summary>
    /// 数据导入结果
    /// </summary>
    public class DataImportResult
    {
        public bool Success { get; set; } = false;
        public int ImportedRecords { get; set; }
        public int SkippedRecords { get; set; }
        public int ErrorRecords { get; set; }
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public string? ImportedDataLocation { get; set; }
    }

    /// <summary>
    /// 数据导出请求
    /// </summary>
    public class DataExportRequest
    {
        public string Source { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public string Target { get; set; } = string.Empty;
        public Dictionary<string, object> Options { get; set; } = new();
        public List<string> Fields { get; set; } = new();
        public Dictionary<string, object> Filters { get; set; } = new();
    }

    /// <summary>
    /// 数据导出结果
    /// </summary>
    public class DataExportResult
    {
        public bool Success { get; set; } = false;
        public int ExportedRecords { get; set; }
        public string? ExportedFilePath { get; set; }
        public long FileSize { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 数据转换请求
    /// </summary>
    public class DataTransformRequest
    {
        public string SourceFormat { get; set; } = string.Empty;
        public string TargetFormat { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public Dictionary<string, object> TransformOptions { get; set; } = new();
        public List<TransformRule> Rules { get; set; } = new();
    }

    /// <summary>
    /// 转换规则
    /// </summary>
    public class TransformRule
    {
        public string SourceField { get; set; } = string.Empty;
        public string TargetField { get; set; } = string.Empty;
        public string? TransformFunction { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 数据转换结果
    /// </summary>
    public class DataTransformResult
    {
        public bool Success { get; set; } = false;
        public string TransformedData { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    /// <summary>
    /// 数据验证请求
    /// </summary>
    public class DataValidationRequest
    {
        public string Data { get; set; } = string.Empty;
        public string Format { get; set; } = string.Empty;
        public List<ValidationRule> Rules { get; set; } = new();
        public Dictionary<string, object> Options { get; set; } = new();
    }

    /// <summary>
    /// 验证规则
    /// </summary>
    public class ValidationRule
    {
        public string Field { get; set; } = string.Empty;
        public string RuleType { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 数据验证结果
    /// </summary>
    public class DataValidationResult
    {
        public bool IsValid { get; set; } = true;
        public List<ValidationError> Errors { get; set; } = new();
        public List<ValidationWarning> Warnings { get; set; } = new();
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    /// <summary>
    /// 验证错误
    /// </summary>
    public class ValidationError
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string RuleType { get; set; } = string.Empty;
        public object? Value { get; set; }
        public int? LineNumber { get; set; }
    }

    /// <summary>
    /// 验证警告
    /// </summary>
    public class ValidationWarning
    {
        public string Field { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public object? Value { get; set; }
        public int? LineNumber { get; set; }
    }
}

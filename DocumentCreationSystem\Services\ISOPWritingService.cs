using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// SOP（标准作业程序）写作服务接口
    /// </summary>
    public interface ISOPWritingService
    {
        /// <summary>
        /// 分析素材文件，提取业务流程和操作要点
        /// </summary>
        /// <param name="materialFiles">素材文件路径列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>素材分析结果</returns>
        Task<SOPMaterialAnalysisResult> AnalyzeMaterialsAsync(
            List<string> materialFiles,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成SOP大纲
        /// </summary>
        /// <param name="request">SOP生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>SOP大纲</returns>
        Task<SOPOutline> GenerateOutlineAsync(
            SOPGenerationRequest request,
            SOPMaterialAnalysisResult materialAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成SOP标题
        /// </summary>
        /// <param name="request">SOP生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>SOP标题</returns>
        Task<string> GenerateTitleAsync(
            SOPGenerationRequest request,
            SOPMaterialAnalysisResult materialAnalysis,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成SOP章节内容
        /// </summary>
        /// <param name="sectionOutline">章节大纲</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="previousSections">前面已生成的章节</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>章节内容</returns>
        Task<string> GenerateSectionAsync(
            SOPSectionOutline sectionOutline,
            SOPGenerationRequest request,
            SOPMaterialAnalysisResult materialAnalysis,
            List<SOPSection> previousSections,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成完整SOP文档
        /// </summary>
        /// <param name="request">SOP生成请求</param>
        /// <param name="progress">进度报告</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>SOP生成结果</returns>
        Task<SOPGenerationResult> GenerateSOPAsync(
            SOPGenerationRequest request,
            IProgress<string>? progress = null,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 格式化SOP内容
        /// </summary>
        /// <param name="sections">章节列表</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="title">SOP标题</param>
        /// <returns>格式化后的SOP内容</returns>
        string FormatSOPContent(List<SOPSection> sections, SOPGenerationRequest request, string title);

        /// <summary>
        /// 验证SOP质量
        /// </summary>
        /// <param name="content">SOP内容</param>
        /// <param name="request">SOP生成请求</param>
        /// <returns>质量评分（0-100）</returns>
        Task<int> ValidateSOPQualityAsync(string content, SOPGenerationRequest request);

        /// <summary>
        /// 生成流程图描述
        /// </summary>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>流程图描述</returns>
        Task<string> GenerateFlowchartDescriptionAsync(
            SOPMaterialAnalysisResult materialAnalysis,
            SOPGenerationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成检查清单
        /// </summary>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>检查清单内容</returns>
        Task<string> GenerateChecklistAsync(
            SOPMaterialAnalysisResult materialAnalysis,
            SOPGenerationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成风险评估表
        /// </summary>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>风险评估表内容</returns>
        Task<string> GenerateRiskAssessmentAsync(
            SOPMaterialAnalysisResult materialAnalysis,
            SOPGenerationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成培训计划
        /// </summary>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>培训计划内容</returns>
        Task<string> GenerateTrainingPlanAsync(
            SOPMaterialAnalysisResult materialAnalysis,
            SOPGenerationRequest request,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成绩效指标
        /// </summary>
        /// <param name="materialAnalysis">素材分析结果</param>
        /// <param name="request">SOP生成请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>绩效指标内容</returns>
        Task<string> GeneratePerformanceIndicatorsAsync(
            SOPMaterialAnalysisResult materialAnalysis,
            SOPGenerationRequest request,
            CancellationToken cancellationToken = default);
    }
}

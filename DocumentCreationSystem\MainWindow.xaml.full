<Window x:Class="DocumentCreationSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:controls="clr-namespace:DocumentCreationSystem.Controls"
        xmlns:viewModels="clr-namespace:DocumentCreationSystem.ViewModels"
        xmlns:converters="clr-namespace:DocumentCreationSystem.Converters"
        Title="文档管理及AI创作系统"
        Height="900" Width="1400"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <converters:BoolToFavoriteIconConverter x:Key="BoolToFavoriteIconConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}">
            <MenuItem Header="文件(_F)">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="File"/>
                </MenuItem.Icon>
                <MenuItem Header="新建项目" Click="NewProject_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="FolderPlus"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="打开项目" Click="OpenProject_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="FolderOpen"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="打开项目文件夹" Click="OpenProjectFolder_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="FolderOpenOutline"/>
                    </MenuItem.Icon>
                </MenuItem>
                <Separator/>
                <MenuItem Header="保存文档" Click="SaveDocument_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="ContentSave"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
            <MenuItem Header="AI模型(_A)">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="Brain"/>
                </MenuItem.Icon>
                <MenuItem Header="模型配置" Click="AIModelConfig_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="Settings"/>
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="测试连接" Click="TestAIConnection_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="Connection"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
            <MenuItem Header="工具(_T)">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="Tools"/>
                </MenuItem.Icon>
                <MenuItem Header="项目工具" Click="ProjectTools_Click">
                    <MenuItem.Icon>
                        <materialDesign:PackIcon Kind="Toolbox"/>
                    </MenuItem.Icon>
                </MenuItem>
            </MenuItem>
        </Menu>

        <!-- 顶部工具栏 -->
        <materialDesign:ColorZone Grid.Row="1" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon Kind="BookOpenPageVariant" Height="24" Width="24"
                                           VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock Text="文档管理及AI创作系统" VerticalAlignment="Center"
                             FontSize="18" FontWeight="Medium"/>
                </StackPanel>

                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <!-- 功能按钮 -->
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="AI模型配置" Margin="4,0"
                          Click="AIModelConfig_Click">
                        <materialDesign:PackIcon Kind="Settings"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="新建项目" Margin="4,0"
                          Click="NewProject_Click">
                        <materialDesign:PackIcon Kind="FolderPlus"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="打开项目" Margin="4,0"
                          Click="OpenProject_Click">
                        <materialDesign:PackIcon Kind="FolderOpen"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="保存" Margin="4,0"
                          Click="SaveDocument_Click">
                        <materialDesign:PackIcon Kind="ContentSave"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="项目工具" Margin="4,0"
                          Click="ProjectTools_Click">
                        <materialDesign:PackIcon Kind="Tools"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="设置" Margin="4,0"
                          Click="Settings_Click">
                        <materialDesign:PackIcon Kind="Settings"/>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="350"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧项目导航 -->
            <materialDesign:Card Grid.Column="0" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 项目导航标题 -->
                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
                        <DockPanel>
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                                <materialDesign:PackIcon Kind="FolderOutline" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="项目导航" VerticalAlignment="Center" FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="创建项目文件夹 (按住Ctrl键创建简单文件夹)" Margin="4,0"
                                      Click="CreateProjectFolder_Click">
                                    <materialDesign:PackIcon Kind="FolderPlus"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="创建.docx文件" Margin="4,0"
                                      Click="CreateDocxFile_Click">
                                    <materialDesign:PackIcon Kind="FileDocumentPlus"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="打开项目文件夹" Margin="4,0"
                                      Click="OpenProjectFolder_Click">
                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="刷新项目" Margin="4,0"
                                      Click="RefreshProject_Click">
                                    <materialDesign:PackIcon Kind="Refresh"/>
                                </Button>
                            </StackPanel>
                        </DockPanel>
                    </materialDesign:ColorZone>

                    <!-- 项目导航选项卡 -->
                    <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                        <!-- 当前项目选项卡 -->
                        <TabItem Header="当前项目">
                            <TabItem.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FolderOutline" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="当前项目" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </TabItem.HeaderTemplate>

                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <TreeView x:Name="ProjectTreeView" Margin="8"
                                        SelectedItemChanged="ProjectTreeView_SelectedItemChanged"
                                        MouseDoubleClick="ProjectTreeView_MouseDoubleClick">
                                    <TreeView.Resources>
                                        <HierarchicalDataTemplate DataType="{x:Type viewModels:ProjectTreeItem}"
                                                                ItemsSource="{Binding Children}">
                                            <StackPanel Orientation="Horizontal">
                                                <materialDesign:PackIcon Kind="{Binding IconKind}"
                                                                       Margin="0,0,4,0"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding Name}"
                                                         VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </HierarchicalDataTemplate>
                                    </TreeView.Resources>
                                    <!-- 右键菜单 -->
                                    <TreeView.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Header="复制" Click="CopyItem_Click">
                                                <MenuItem.Icon>
                                                    <materialDesign:PackIcon Kind="ContentCopy"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="粘贴" Click="PasteItem_Click">
                                                <MenuItem.Icon>
                                                    <materialDesign:PackIcon Kind="ContentPaste"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <Separator/>
                                            <MenuItem Header="查看路径" Click="ShowPath_Click">
                                                <MenuItem.Icon>
                                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <MenuItem Header="在资源管理器中打开" Click="OpenInExplorer_Click">
                                                <MenuItem.Icon>
                                                    <materialDesign:PackIcon Kind="FolderOpenOutline"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                            <Separator/>
                                            <MenuItem Header="删除" Click="DeleteItem_Click">
                                                <MenuItem.Icon>
                                                    <materialDesign:PackIcon Kind="Delete"/>
                                                </MenuItem.Icon>
                                            </MenuItem>
                                        </ContextMenu>
                                    </TreeView.ContextMenu>
                                </TreeView>
                            </ScrollViewer>
                        </TabItem>

                        <!-- 历史项目选项卡 -->
                        <TabItem Header="历史项目">
                            <TabItem.HeaderTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="History" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                        <TextBlock Text="历史项目" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </TabItem.HeaderTemplate>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- 历史项目工具栏 -->
                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="8,8,8,4">
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                          ToolTip="刷新历史项目"
                                          Click="RefreshHistory_Click">
                                        <materialDesign:PackIcon Kind="Refresh"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                          ToolTip="清理无效记录"
                                          Click="CleanupInvalidHistory_Click">
                                        <materialDesign:PackIcon Kind="Broom"/>
                                    </Button>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                          ToolTip="清空历史记录"
                                          Click="ClearHistory_Click">
                                        <materialDesign:PackIcon Kind="DeleteSweep"/>
                                    </Button>
                                </StackPanel>

                                <!-- 历史项目列表 -->
                                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                                    <ListBox x:Name="HistoryProjectListBox" Margin="8,4,8,8"
                                           SelectionChanged="HistoryProjectListBox_SelectionChanged">
                                        <ListBox.ItemTemplate>
                                            <DataTemplate>
                                                <Border Padding="8" Margin="2"
                                                      Background="Transparent"
                                                      BorderBrush="{DynamicResource MaterialDesignDivider}"
                                                      BorderThickness="0,0,0,1">
                                                    <Grid>
                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="Auto"/>
                                                            <ColumnDefinition Width="*"/>
                                                            <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- 项目图标 -->
                                                        <materialDesign:PackIcon Grid.Column="0"
                                                                               Kind="Folder"
                                                                               VerticalAlignment="Top"
                                                                               Margin="0,2,8,0"/>

                                                        <!-- 项目信息 -->
                                                        <StackPanel Grid.Column="1">
                                                            <TextBlock Text="{Binding Name}"
                                                                     FontWeight="Medium"
                                                                     TextTrimming="CharacterEllipsis"/>
                                                            <TextBlock Text="{Binding RootPath}"
                                                                     FontSize="11"
                                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                     TextTrimming="CharacterEllipsis"/>
                                                            <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                                <TextBlock Text="{Binding Type}"
                                                                         FontSize="10"
                                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                                <TextBlock Text=" • "
                                                                         FontSize="10"
                                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                                <TextBlock Text="{Binding LastAccessTime, StringFormat='yyyy-MM-dd HH:mm'}"
                                                                         FontSize="10"
                                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                            </StackPanel>
                                                        </StackPanel>

                                                        <!-- 收藏按钮 -->
                                                        <Button Grid.Column="2"
                                                              Style="{StaticResource MaterialDesignIconButton}"
                                                              ToolTip="收藏/取消收藏"
                                                              Click="ToggleFavorite_Click"
                                                              Tag="{Binding}">
                                                            <materialDesign:PackIcon Kind="{Binding IsFavorite, Converter={StaticResource BoolToFavoriteIconConverter}}"/>
                                                        </Button>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ListBox.ItemTemplate>

                                        <!-- 右键菜单 -->
                                        <ListBox.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Header="打开项目" Click="OpenHistoryProject_Click">
                                                    <MenuItem.Icon>
                                                        <materialDesign:PackIcon Kind="FolderOpen"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <MenuItem Header="在资源管理器中打开" Click="OpenHistoryInExplorer_Click">
                                                    <MenuItem.Icon>
                                                        <materialDesign:PackIcon Kind="FolderOpenOutline"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                                <Separator/>
                                                <MenuItem Header="从历史记录中移除" Click="RemoveFromHistory_Click">
                                                    <MenuItem.Icon>
                                                        <materialDesign:PackIcon Kind="Delete"/>
                                                    </MenuItem.Icon>
                                                </MenuItem>
                                            </ContextMenu>
                                        </ListBox.ContextMenu>
                                    </ListBox>
                                </ScrollViewer>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </materialDesign:Card>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="LightGray"/>

            <!-- 中央编辑区域 -->
            <materialDesign:Card Grid.Column="2" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 编辑器标题栏 -->
                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
                        <DockPanel>
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                                <materialDesign:PackIcon Kind="FileDocumentEdit" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="文档编辑器" VerticalAlignment="Center" FontWeight="Medium"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                      ToolTip="全屏编辑" Click="FullScreen_Click">
                                    <materialDesign:PackIcon Kind="Fullscreen"/>
                                </Button>
                            </StackPanel>
                        </DockPanel>
                    </materialDesign:ColorZone>

                    <!-- 文档编辑器 -->
                    <controls:DocumentEditor x:Name="DocumentEditorControl"
                                           Grid.Row="1"/>

                    <!-- 编辑器状态栏 -->
                    <materialDesign:ColorZone Grid.Row="2" Mode="Light" Padding="16,4">
                        <DockPanel>
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                                <TextBlock x:Name="WordCountText" Text="字数: 0" Margin="0,0,16,0"/>
                                <TextBlock x:Name="CurrentLineText" Text="行: 1" Margin="0,0,16,0"/>
                                <TextBlock x:Name="CurrentColumnText" Text="列: 1" Margin="0,0,16,0"/>
                            </StackPanel>

                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                <materialDesign:PackIcon Kind="CheckCircle" Foreground="Green"
                                                       VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="已保存" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DockPanel>
                    </materialDesign:ColorZone>
                </Grid>
            </materialDesign:Card>

            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" Background="LightGray"/>

            <!-- 右侧AI工具面板 -->
            <materialDesign:Card Grid.Column="4" Margin="8" materialDesign:ElevationAssist.Elevation="Dp2">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- AI工具标题 -->
                    <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16,8">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Robot" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="AI助手" VerticalAlignment="Center" FontWeight="Medium"/>
                        </StackPanel>
                    </materialDesign:ColorZone>

                    <!-- AI工具内容 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="16">
                            <!-- 快速操作 -->
                            <TextBlock Text="快速操作" FontWeight="Medium" Margin="0,0,0,8"/>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="PolishText_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AutoFix" Margin="0,0,8,0"/>
                                        <TextBlock Text="润色选中文本"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="ExpandText_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="TextBoxPlus" Margin="0,0,8,0"/>
                                        <TextBlock Text="扩写内容"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="CreativeRequest_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Creation" Margin="0,0,8,0"/>
                                        <TextBlock Text="按要求创作"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="ContinueWriting_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="PlayArrow" Margin="0,0,8,0"/>
                                        <TextBlock Text="从光标处续写"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Separator Margin="0,16"/>

                            <Separator Margin="0,16"/>

                            <!-- 小说创作工具 -->
                            <TextBlock Text="小说创作" FontWeight="Medium" Margin="0,0,0,8"/>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="GenerateOutline_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FormatListBulleted" Margin="0,0,8,0"/>
                                        <TextBlock Text="生成大纲"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="CreateChapter_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="BookPlus" Margin="0,0,8,0"/>
                                        <TextBlock Text="创作章节"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="OneClickWriting_Click"
                                  Background="{DynamicResource PrimaryHueMidBrush}">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AutoFix" Margin="0,0,8,0"/>
                                        <TextBlock Text="一键写书"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Margin="0,4" HorizontalAlignment="Stretch"
                                  Click="CheckConsistency_Click">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="CheckboxMarkedCircle" Margin="0,0,8,0"/>
                                        <TextBlock Text="一致性检查"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Separator Margin="0,16"/>

                            <!-- AI参数设置 -->
                            <TextBlock Text="AI参数" FontWeight="Medium" Margin="0,0,0,8"/>

                            <TextBlock Text="创意度" Margin="0,8,0,4"/>
                            <Slider x:Name="TemperatureSlider" Minimum="0" Maximum="1"
                                  Value="0.7"
                                  TickFrequency="0.1" IsSnapToTickEnabled="True"/>

                            <TextBlock Text="最大字数" Margin="0,8,0,4"/>
                            <Slider x:Name="MaxTokensSlider" Minimum="100" Maximum="4000"
                                  Value="2000"
                                  TickFrequency="100" IsSnapToTickEnabled="True"/>

                            <Separator Margin="0,16"/>

                            <!-- 进度信息 -->
                            <TextBlock Text="创作进度" FontWeight="Medium" Margin="0,0,0,8"/>

                            <StackPanel>
                                <TextBlock x:Name="CurrentChapterText" Text="当前章节: 第1章" Margin="0,4"/>
                                <TextBlock x:Name="ChapterProgressText" Text="字数: 0 / 6500" Margin="0,4"/>
                                <ProgressBar x:Name="ChapterProgressBar" Value="0" Maximum="6500" Height="8" Margin="0,4"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- 底部状态栏 -->
        <materialDesign:ColorZone Grid.Row="3" Mode="PrimaryDark" Padding="16,8">
            <DockPanel>
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                    <materialDesign:PackIcon x:Name="StatusIcon" Kind="CheckCircle"
                                           Foreground="LightGreen" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText" Text="系统就绪" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="ProjectInfoText" Text="未打开项目" VerticalAlignment="Center"/>
                </StackPanel>

                <TextBlock x:Name="TimeText" DockPanel.Dock="Right" VerticalAlignment="Center"/>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>

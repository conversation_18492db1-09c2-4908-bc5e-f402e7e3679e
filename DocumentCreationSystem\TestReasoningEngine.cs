using DocumentCreationSystem.Models.Reasoning;
using DocumentCreationSystem.Services.ReasoningEngine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 推理引擎测试程序
    /// </summary>
    public class TestReasoningEngine
    {
        private readonly ILogger<TestReasoningEngine> _logger;
        private readonly IReasoningEngine _reasoningEngine;
        private readonly IReasoningEngineInitializer _initializer;

        public TestReasoningEngine()
        {
            // 配置服务
            var services = new ServiceCollection();
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            services.AddReasoningEngine();

            var serviceProvider = services.BuildServiceProvider();
            
            _logger = serviceProvider.GetRequiredService<ILogger<TestReasoningEngine>>();
            _reasoningEngine = serviceProvider.GetRequiredService<IReasoningEngine>();
            _initializer = serviceProvider.GetRequiredService<IReasoningEngineInitializer>();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTestsAsync()
        {
            try
            {
                _logger.LogInformation("=== 推理引擎测试开始 ===");

                // 初始化推理引擎
                await InitializeReasoningEngineAsync();

                // 运行基础功能测试
                await TestBasicFunctionalityAsync();

                // 运行前向推理测试
                await TestForwardReasoningAsync();

                // 运行后向推理测试
                await TestBackwardReasoningAsync();

                // 运行一致性检查测试
                await TestConsistencyCheckAsync();

                // 运行查询功能测试
                await TestQueryFunctionalityAsync();

                // 运行统计信息测试
                await TestStatisticsAsync();

                // 运行解释功能测试
                await TestExplanationAsync();

                _logger.LogInformation("=== 推理引擎测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推理引擎测试失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化推理引擎
        /// </summary>
        private async Task InitializeReasoningEngineAsync()
        {
            _logger.LogInformation("--- 初始化推理引擎 ---");
            
            await _initializer.InitializeAsync(_reasoningEngine);
            
            var stats = await _reasoningEngine.GetStatisticsAsync();
            _logger.LogInformation($"初始化完成 - 事实数量: {stats.TotalFacts}, 规则数量: {stats.TotalRules}");
        }

        /// <summary>
        /// 测试基础功能
        /// </summary>
        private async Task TestBasicFunctionalityAsync()
        {
            _logger.LogInformation("--- 测试基础功能 ---");

            // 测试添加事实
            var testFact = new ReasoningFact
            {
                Subject = "测试对象",
                Predicate = "具有",
                Object = "测试属性",
                Confidence = 0.9,
                Type = FactType.Assertion,
                Source = "测试",
                Tags = new List<string> { "测试", "基础功能" }
            };

            await _reasoningEngine.AddFactAsync(testFact);
            _logger.LogInformation($"已添加测试事实: {testFact}");

            // 测试查询事实
            var query = new FactQuery
            {
                Subject = "测试对象",
                MaxResults = 10
            };

            var results = await _reasoningEngine.QueryFactsAsync(query);
            _logger.LogInformation($"查询到 {results.Count} 个匹配的事实");

            // 测试搜索功能
            var searchResults = await _reasoningEngine.SearchFactsAsync("测试", 5);
            _logger.LogInformation($"搜索到 {searchResults.Count} 个相关事实");

            // 测试删除事实
            await _reasoningEngine.RemoveFactAsync(testFact.Id);
            _logger.LogInformation($"已删除测试事实: {testFact.Id}");
        }

        /// <summary>
        /// 测试前向推理
        /// </summary>
        private async Task TestForwardReasoningAsync()
        {
            _logger.LogInformation("--- 测试前向推理 ---");

            // 添加测试事实
            var facts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "张三",
                    Predicate = "是",
                    Object = "程序员",
                    Confidence = 1.0,
                    Type = FactType.Assertion,
                    Source = "测试数据"
                },
                new ReasoningFact
                {
                    Subject = "程序员",
                    Predicate = "具有",
                    Object = "编程技能",
                    Confidence = 0.95,
                    Type = FactType.Assertion,
                    Source = "测试数据"
                }
            };

            await _reasoningEngine.AddFactsAsync(facts);

            // 添加推理规则
            var rule = new ReasoningRule
            {
                Id = "test_rule_programmer",
                Name = "程序员技能推理",
                Description = "如果某人是程序员，且程序员具有某技能，则该人具有该技能",
                Confidence = 0.9,
                Type = RuleType.Deductive,
                Conditions = new List<RuleCondition>
                {
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            PredicatePattern = "是",
                            ObjectPattern = "程序员"
                        }
                    },
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            SubjectPattern = "程序员",
                            PredicatePattern = "具有"
                        }
                    }
                },
                Conclusions = new List<RuleConclusion>
                {
                    new RuleConclusion
                    {
                        Type = ConclusionType.AddFact,
                        NewFact = new ReasoningFact
                        {
                            Subject = "张三",
                            Predicate = "具有",
                            Object = "编程技能",
                            Type = FactType.Derived
                        },
                        Confidence = 0.85
                    }
                }
            };

            await _reasoningEngine.AddRuleAsync(rule);

            // 执行前向推理
            var reasoningQuery = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 5,
                MinConfidence = 0.5,
                IncludeReasoningChain = true
            };

            var result = await _reasoningEngine.ReasonAsync(reasoningQuery);
            
            _logger.LogInformation($"前向推理结果:");
            _logger.LogInformation($"  成功: {result.IsSuccess}");
            _logger.LogInformation($"  推导事实数量: {result.DerivedFacts.Count}");
            _logger.LogInformation($"  使用规则数量: {result.RulesUsed}");
            _logger.LogInformation($"  置信度: {result.Confidence:F2}");
            _logger.LogInformation($"  推理时间: {result.ReasoningTimeMs}ms");

            if (result.ReasoningChain != null)
            {
                _logger.LogInformation($"  推理链长度: {result.ReasoningChain.Length}");
                foreach (var step in result.ReasoningChain.Steps)
                {
                    _logger.LogInformation($"    步骤 {step.StepNumber}: {step.Description}");
                }
            }

            foreach (var fact in result.DerivedFacts)
            {
                _logger.LogInformation($"  推导事实: {fact}");
            }

            // 清理测试数据
            await _reasoningEngine.RemoveRuleAsync(rule.Id);
            foreach (var fact in facts)
            {
                await _reasoningEngine.RemoveFactAsync(fact.Id);
            }
        }

        /// <summary>
        /// 测试后向推理
        /// </summary>
        private async Task TestBackwardReasoningAsync()
        {
            _logger.LogInformation("--- 测试后向推理 ---");

            // 定义目标事实
            var goal = new ReasoningFact
            {
                Subject = "苏格拉底",
                Predicate = "具有",
                Object = "思考能力",
                Type = FactType.Hypothesis
            };

            // 执行后向推理
            var chain = await _reasoningEngine.BackwardReasoningAsync(goal);
            
            _logger.LogInformation($"后向推理结果:");
            _logger.LogInformation($"  目标: {goal}");
            _logger.LogInformation($"  推理链长度: {chain.Length}");
            _logger.LogInformation($"  推理完整: {chain.IsComplete}");
            _logger.LogInformation($"  置信度: {chain.Confidence:F2}");

            foreach (var step in chain.Steps)
            {
                _logger.LogInformation($"  步骤 {step.StepNumber}: {step.Description}");
                _logger.LogInformation($"    输入事实: {string.Join(", ", step.InputFacts.Select(f => f.ToString()))}");
                _logger.LogInformation($"    输出事实: {string.Join(", ", step.OutputFacts.Select(f => f.ToString()))}");
            }
        }

        /// <summary>
        /// 测试一致性检查
        /// </summary>
        private async Task TestConsistencyCheckAsync()
        {
            _logger.LogInformation("--- 测试一致性检查 ---");

            // 创建包含冲突的事实集合
            var conflictingFacts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "天空",
                    Predicate = "是",
                    Object = "蓝色",
                    Confidence = 0.9,
                    Type = FactType.Assertion
                },
                new ReasoningFact
                {
                    Subject = "天空",
                    Predicate = "是",
                    Object = "红色",
                    Confidence = 0.8,
                    Type = FactType.Assertion
                },
                new ReasoningFact
                {
                    Subject = "水",
                    Predicate = "是",
                    Object = "液体",
                    Confidence = 1.0,
                    Type = FactType.Assertion
                }
            };

            var consistencyResult = await _reasoningEngine.CheckConsistencyAsync(conflictingFacts);
            
            _logger.LogInformation($"一致性检查结果:");
            _logger.LogInformation($"  是否一致: {consistencyResult.IsConsistent}");
            _logger.LogInformation($"  检查事实数量: {consistencyResult.FactsChecked}");
            _logger.LogInformation($"  冲突数量: {consistencyResult.Conflicts.Count}");
            _logger.LogInformation($"  一致性评分: {consistencyResult.ConsistencyScore:F2}");
            _logger.LogInformation($"  检查时间: {consistencyResult.CheckTimeMs}ms");

            foreach (var conflict in consistencyResult.Conflicts)
            {
                _logger.LogInformation($"  冲突: {conflict.Description}");
                _logger.LogInformation($"    类型: {conflict.Type}");
                _logger.LogInformation($"    严重程度: {conflict.Severity:F2}");
                _logger.LogInformation($"    涉及事实: {string.Join(", ", conflict.ConflictingFacts.Select(f => f.ToString()))}");
            }
        }

        /// <summary>
        /// 测试查询功能
        /// </summary>
        private async Task TestQueryFunctionalityAsync()
        {
            _logger.LogInformation("--- 测试查询功能 ---");

            // 测试复杂查询
            var complexQuery = new FactQuery
            {
                Predicate = "是",
                Type = FactType.Assertion,
                MinConfidence = 0.8,
                MaxResults = 5,
                SortOrder = FactSortOrder.ByConfidence,
                Tags = new List<string> { "哲学" }
            };

            var queryResults = await _reasoningEngine.QueryFactsAsync(complexQuery);
            _logger.LogInformation($"复杂查询结果: {queryResults.Count} 个事实");

            foreach (var fact in queryResults)
            {
                _logger.LogInformation($"  {fact} (置信度: {fact.Confidence:F2})");
            }

            // 测试依赖关系图
            if (queryResults.Any())
            {
                var firstFact = queryResults.First();
                var dependencyGraph = await _reasoningEngine.GetFactDependenciesAsync(firstFact.Id);
                
                _logger.LogInformation($"事实依赖关系图:");
                _logger.LogInformation($"  根事实: {firstFact}");
                _logger.LogInformation($"  节点数量: {dependencyGraph.Nodes.Count}");
                _logger.LogInformation($"  边数量: {dependencyGraph.Edges.Count}");
                _logger.LogInformation($"  图深度: {dependencyGraph.Depth}");
            }
        }

        /// <summary>
        /// 测试统计信息
        /// </summary>
        private async Task TestStatisticsAsync()
        {
            _logger.LogInformation("--- 测试统计信息 ---");

            var statistics = await _reasoningEngine.GetStatisticsAsync();
            
            _logger.LogInformation($"推理引擎统计信息:");
            _logger.LogInformation($"  总事实数量: {statistics.TotalFacts}");
            _logger.LogInformation($"  总规则数量: {statistics.TotalRules}");
            _logger.LogInformation($"  总查询数量: {statistics.TotalQueries}");
            _logger.LogInformation($"  成功推理次数: {statistics.SuccessfulReasoning}");
            _logger.LogInformation($"  失败推理次数: {statistics.FailedReasoning}");
            _logger.LogInformation($"  平均推理时间: {statistics.AverageReasoningTimeMs:F2}ms");
            _logger.LogInformation($"  最长推理时间: {statistics.MaxReasoningTimeMs}ms");
            _logger.LogInformation($"  最短推理时间: {statistics.MinReasoningTimeMs}ms");
            _logger.LogInformation($"  平均推理链长度: {statistics.AverageChainLength:F2}");
            _logger.LogInformation($"  最长推理链长度: {statistics.MaxChainLength}");

            if (statistics.RuleUsageCount.Any())
            {
                _logger.LogInformation($"  规则使用统计:");
                foreach (var usage in statistics.RuleUsageCount.OrderByDescending(x => x.Value).Take(5))
                {
                    _logger.LogInformation($"    {usage.Key}: {usage.Value} 次");
                }
            }
        }

        /// <summary>
        /// 测试解释功能
        /// </summary>
        private async Task TestExplanationAsync()
        {
            _logger.LogInformation("--- 测试解释功能 ---");

            // 执行一个简单的推理查询
            var query = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 3,
                MinConfidence = 0.5,
                IncludeReasoningChain = true
            };

            var result = await _reasoningEngine.ReasonAsync(query);
            
            if (result.IsSuccess)
            {
                // 生成解释
                var explanation = await _reasoningEngine.ExplainReasoningAsync(result);
                
                _logger.LogInformation($"推理解释:");
                _logger.LogInformation($"  解释级别: {explanation.Level}");
                _logger.LogInformation($"  生成时间: {explanation.GeneratedAt}");
                _logger.LogInformation($"  步骤解释数量: {explanation.StepExplanations.Count}");
                _logger.LogInformation($"  关键决策点数量: {explanation.KeyDecisionPoints.Count}");
                
                _logger.LogInformation($"自然语言解释:");
                _logger.LogInformation(explanation.NaturalLanguageExplanation);

                if (explanation.StepExplanations.Any())
                {
                    _logger.LogInformation($"详细步骤解释:");
                    foreach (var stepExplanation in explanation.StepExplanations)
                    {
                        _logger.LogInformation($"  {stepExplanation.Explanation}");
                        if (!string.IsNullOrEmpty(stepExplanation.RuleExplanation))
                        {
                            _logger.LogInformation($"    规则: {stepExplanation.RuleExplanation}");
                        }
                        if (!string.IsNullOrEmpty(stepExplanation.RuleSelectionReason))
                        {
                            _logger.LogInformation($"    选择理由: {stepExplanation.RuleSelectionReason}");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 主程序入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                var tester = new TestReasoningEngine();
                await tester.RunAllTestsAsync();
                
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}

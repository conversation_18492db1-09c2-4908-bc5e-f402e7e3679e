# 主题颜色配置使用说明

## 功能概述

主题颜色配置功能允许用户自定义软件界面的颜色主题，包括主色调、次要色调、强调色等，支持浅色和深色两种基础主题模式。默认使用清新的浅蓝色主题。

## 打开主题配置

有两种方式打开主题配置界面：

### 方式1：通过菜单
1. 点击顶部菜单栏的"工具"
2. 选择"主题配置"

### 方式2：通过工具栏
1. 点击顶部工具栏中的调色板图标 🎨

## 界面介绍

主题配置界面分为两个主要区域：

### 左侧：预设主题列表
- **浅蓝色（默认）**：清新的浅蓝色主题
- **深紫色**：优雅的深紫色主题
- **绿色自然**：清新的绿色主题
- **橙色活力**：充满活力的橙色主题
- **深色模式**：护眼的深色主题
- **深色紫色**：深色模式下的紫色主题

每个预设主题都显示：
- 主题名称和描述
- 颜色预览圆点（主色调、次要色调、强调色）

### 右侧：自定义配置区域
- **基础主题选择**：浅色主题 / 深色主题
- **颜色配置**：
  - 主色调：界面的主要颜色
  - 次要色调：辅助颜色
  - 强调色：突出显示的颜色
- **实时预览**：显示配置效果
- **保存主题**：可以保存自定义主题

## 使用步骤

### 快速应用预设主题
1. 打开主题配置界面
2. 在左侧点击任意预设主题
3. 右侧会自动填充该主题的配置
4. 点击"应用"按钮

### 自定义主题
1. 打开主题配置界面
2. 选择基础主题（浅色/深色）
3. 在颜色配置区域输入十六进制颜色值（如：#2196F3）
4. 实时查看预览效果
5. 可选：点击"预览"按钮临时应用
6. 点击"应用"按钮正式应用

### 保存自定义主题
1. 配置好自定义颜色
2. 在"保存主题"区域输入主题名称
3. 可选：输入主题描述
4. 点击"保存主题"按钮

## 颜色配置说明

### 颜色格式
- 支持十六进制颜色值格式：#RRGGBB
- 例如：#2196F3（蓝色）、#4CAF50（绿色）、#FF9800（橙色）
- 可以省略#号，系统会自动添加

### 颜色作用
- **主色调**：按钮、标题栏、选中状态等主要界面元素
- **次要色调**：辅助按钮、次要界面元素
- **强调色**：链接、重要提示、焦点状态等

### 颜色建议
- **主色调**：选择品牌色或喜欢的主色
- **次要色调**：选择与主色调互补或对比的颜色
- **强调色**：通常选择主色调的深色或亮色变体

## 预设主题详情

### 浅蓝色（默认）
- 主色调：#2196F3（Material Blue）
- 次要色调：#03DAC6（Teal）
- 强调色：#1976D2（Dark Blue）
- 适合：日常办公、清新简洁的风格

### 深紫色
- 主色调：#673AB7（Deep Purple）
- 次要色调：#CDDC39（Lime）
- 强调色：#512DA8（Dark Purple）
- 适合：专业、优雅的风格

### 绿色自然
- 主色调：#4CAF50（Green）
- 次要色调：#FF9800（Orange）
- 强调色：#388E3C（Dark Green）
- 适合：自然、环保主题

### 橙色活力
- 主色调：#FF9800（Orange）
- 次要色调：#9C27B0（Purple）
- 强调色：#F57C00（Dark Orange）
- 适合：活力、创意工作

### 深色模式
- 基础主题：深色
- 主色调：#2196F3（Blue）
- 适合：夜间工作、护眼需求

## 功能按钮说明

- **预览**：临时应用当前配置，可以看到效果但不保存
- **保存主题**：将当前配置保存为自定义主题（功能开发中）
- **应用**：正式应用当前配置并关闭对话框
- **取消**：取消所有更改，恢复原主题
- **重置为默认**：恢复到系统默认的浅蓝色主题

## 注意事项

1. **颜色兼容性**：确保选择的颜色在浅色和深色背景下都有良好的可读性
2. **对比度**：主色调和背景色之间应有足够的对比度
3. **预览功能**：建议先使用预览功能查看效果，满意后再应用
4. **恢复功能**：如果不满意新主题，可以点击"取消"或"重置为默认"
5. **重启应用**：某些主题更改可能需要重启应用程序才能完全生效

## 常见问题

**Q: 如何恢复默认主题？**
A: 点击左侧的"重置为默认"按钮，或选择"浅蓝色（默认）"预设主题。

**Q: 自定义主题会保存吗？**
A: 当前应用的主题配置会保存，但自定义主题保存功能还在开发中。

**Q: 支持哪些颜色格式？**
A: 目前支持十六进制格式（#RRGGBB），如 #2196F3。

**Q: 深色模式和浅色模式有什么区别？**
A: 深色模式使用深色背景和浅色文字，适合夜间使用；浅色模式使用浅色背景和深色文字，适合日间使用。

**Q: 主题配置会影响文档内容吗？**
A: 不会，主题配置只影响软件界面，不会改变文档内容的显示。

## 技术说明

- 基于 Material Design 设计规范
- 使用 MaterialDesignInXamlToolkit 主题系统
- 支持实时主题切换
- 配置信息自动保存到应用程序配置中

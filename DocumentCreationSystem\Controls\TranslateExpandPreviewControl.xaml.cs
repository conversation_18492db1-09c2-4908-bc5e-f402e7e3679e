using System;
using System.Windows;
using System.Windows.Controls;

namespace DocumentCreationSystem.Controls
{
    /// <summary>
    /// 翻译和扩写预览控件
    /// </summary>
    public partial class TranslateExpandPreviewControl : UserControl
    {
        /// <summary>
        /// 用户接受处理结果事件
        /// </summary>
        public event EventHandler<ProcessResultEventArgs>? ProcessAccepted;

        /// <summary>
        /// 用户拒绝处理结果事件
        /// </summary>
        public event EventHandler<ProcessResultEventArgs>? ProcessRejected;

        /// <summary>
        /// 关闭预览事件
        /// </summary>
        public event EventHandler? PreviewClosed;

        private string _originalText = string.Empty;
        private string _processedText = string.Empty;
        private int _selectionStart;
        private int _selectionLength;
        private string _processType = string.Empty;
        private bool _isComparisonMode = false;

        public TranslateExpandPreviewControl()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 显示翻译预览
        /// </summary>
        /// <param name="originalText">原文</param>
        /// <param name="translatedText">翻译后文本</param>
        /// <param name="selectionStart">选择开始位置</param>
        /// <param name="selectionLength">选择长度</param>
        /// <param name="useFloatingControl">是否使用悬浮控件模式</param>
        public void ShowTranslatePreview(string originalText, string translatedText, 
            int selectionStart, int selectionLength, bool useFloatingControl = false)
        {
            ShowPreview(originalText, translatedText, selectionStart, selectionLength, "翻译", useFloatingControl);
            TitleText.Text = "文本翻译结果预览";
            FloatingTitleText.Text = "翻译结果";
            ProcessedTextTitle.Text = "翻译后文本：";
            ProcessedTextLabel.Text = "翻译后文本";
        }

        /// <summary>
        /// 显示扩写预览
        /// </summary>
        /// <param name="originalText">原文</param>
        /// <param name="expandedText">扩写后文本</param>
        /// <param name="selectionStart">选择开始位置</param>
        /// <param name="selectionLength">选择长度</param>
        /// <param name="useFloatingControl">是否使用悬浮控件模式</param>
        public void ShowExpandPreview(string originalText, string expandedText, 
            int selectionStart, int selectionLength, bool useFloatingControl = false)
        {
            ShowPreview(originalText, expandedText, selectionStart, selectionLength, "扩写", useFloatingControl);
            TitleText.Text = "文本扩写结果预览";
            FloatingTitleText.Text = "扩写结果";
            ProcessedTextTitle.Text = "扩写后文本：";
            ProcessedTextLabel.Text = "扩写后文本";
            
            // 显示字数变化
            UpdateWordCountChange();
        }

        /// <summary>
        /// 通用预览显示方法
        /// </summary>
        private void ShowPreview(string originalText, string processedText, 
            int selectionStart, int selectionLength, string processType, bool useFloatingControl = false)
        {
            _originalText = originalText;
            _processedText = processedText;
            _selectionStart = selectionStart;
            _selectionLength = selectionLength;
            _processType = processType;

            OriginalTextBlock.Text = originalText;
            ProcessedTextBlock.Text = processedText;
            FloatingProcessedText.Text = processedText;

            if (useFloatingControl)
            {
                PreviewContainer.Visibility = Visibility.Collapsed;
                FloatingControl.Visibility = Visibility.Visible;
            }
            else
            {
                PreviewContainer.Visibility = Visibility.Visible;
                FloatingControl.Visibility = Visibility.Collapsed;
            }

            Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 隐藏预览
        /// </summary>
        public void HidePreview()
        {
            Visibility = Visibility.Collapsed;
            PreviewContainer.Visibility = Visibility.Collapsed;
            FloatingControl.Visibility = Visibility.Collapsed;
        }

        private void AcceptButton_Click(object sender, RoutedEventArgs e)
        {
            var args = new ProcessResultEventArgs
            {
                OriginalText = _originalText,
                ProcessedText = _processedText,
                SelectionStart = _selectionStart,
                SelectionLength = _selectionLength,
                ProcessType = _processType,
                IsAccepted = true
            };

            ProcessAccepted?.Invoke(this, args);
            HidePreview();
        }

        private void RejectButton_Click(object sender, RoutedEventArgs e)
        {
            var args = new ProcessResultEventArgs
            {
                OriginalText = _originalText,
                ProcessedText = _processedText,
                SelectionStart = _selectionStart,
                SelectionLength = _selectionLength,
                ProcessType = _processType,
                IsAccepted = false
            };

            ProcessRejected?.Invoke(this, args);
            HidePreview();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HidePreview();
            PreviewClosed?.Invoke(this, EventArgs.Empty);
        }

        private void Compare_Click(object sender, RoutedEventArgs e)
        {
            _isComparisonMode = !_isComparisonMode;

            if (_isComparisonMode)
            {
                ComparisonGrid.Visibility = Visibility.Visible;
                NormalModeViewer.Visibility = Visibility.Collapsed;
                ProcessedTextContainer.Visibility = Visibility.Collapsed;
                CompareButton.Content = "普通模式";

                CompareOriginalText.Text = _originalText;
                CompareProcessedText.Text = _processedText;
            }
            else
            {
                ComparisonGrid.Visibility = Visibility.Collapsed;
                NormalModeViewer.Visibility = Visibility.Visible;
                ProcessedTextContainer.Visibility = Visibility.Visible;
                CompareButton.Content = "对比模式";
            }
        }

        private void Copy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Windows.Clipboard.SetText(_processedText);
                // 可以添加一个提示消息
            }
            catch (Exception ex)
            {
                // 处理复制失败的情况
                System.Windows.MessageBox.Show($"复制失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 更新字数变化显示（仅用于扩写）
        /// </summary>
        private void UpdateWordCountChange()
        {
            if (_processType != "扩写") return;

            var originalCount = _originalText.Length;
            var processedCount = _processedText.Length;
            var change = processedCount - originalCount;

            string changeText;
            if (change > 0)
            {
                changeText = $" (增加 {change} 字)";
            }
            else if (change < 0)
            {
                changeText = $" (减少 {Math.Abs(change)} 字)";
            }
            else
            {
                changeText = " (字数无变化)";
            }

            ProcessedTextTitle.Text = "扩写后文本：" + changeText;
            ProcessedTextLabel.Text = "扩写后文本" + changeText;
        }
    }

    /// <summary>
    /// 处理结果事件参数
    /// </summary>
    public class ProcessResultEventArgs : EventArgs
    {
        /// <summary>
        /// 原文
        /// </summary>
        public string OriginalText { get; set; } = string.Empty;

        /// <summary>
        /// 处理后文本
        /// </summary>
        public string ProcessedText { get; set; } = string.Empty;

        /// <summary>
        /// 选择开始位置
        /// </summary>
        public int SelectionStart { get; set; }

        /// <summary>
        /// 选择长度
        /// </summary>
        public int SelectionLength { get; set; }

        /// <summary>
        /// 处理类型（翻译、扩写等）
        /// </summary>
        public string ProcessType { get; set; } = string.Empty;

        /// <summary>
        /// 是否接受处理结果
        /// </summary>
        public bool IsAccepted { get; set; }
    }
}

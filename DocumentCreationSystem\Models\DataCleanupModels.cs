using System;
using System.Collections.Generic;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 数据清理结果
    /// </summary>
    public class DataCleanupResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        
        // 清理统计
        public int RemovedDocumentsCount { get; set; }
        public int RemovedNovelProjectsCount { get; set; }
        public int RemovedChaptersCount { get; set; }
        public int RemovedCharactersCount { get; set; }
        public int RemovedVectorRecordsCount { get; set; }
        public int RemovedWorldSettingsCount { get; set; }
        
        // 无效数据详情
        public List<Document> InvalidDocuments { get; set; } = new();
        public List<NovelProject> InvalidNovelProjects { get; set; } = new();
        public List<Chapter> InvalidChapters { get; set; } = new();
        public List<Character> InvalidCharacters { get; set; } = new();
        public List<VectorRecord> InvalidVectorRecords { get; set; } = new();
        public List<WorldSetting> InvalidWorldSettings { get; set; } = new();
        
        /// <summary>
        /// 是否有任何清理操作
        /// </summary>
        public bool HasAnyCleanup => 
            RemovedDocumentsCount > 0 || 
            RemovedNovelProjectsCount > 0 || 
            RemovedChaptersCount > 0 || 
            RemovedCharactersCount > 0 || 
            RemovedVectorRecordsCount > 0 || 
            RemovedWorldSettingsCount > 0;
        
        /// <summary>
        /// 总清理数量
        /// </summary>
        public int TotalRemovedCount => 
            RemovedDocumentsCount + 
            RemovedNovelProjectsCount + 
            RemovedChaptersCount + 
            RemovedCharactersCount + 
            RemovedVectorRecordsCount + 
            RemovedWorldSettingsCount;
    }

    /// <summary>
    /// 数据完整性报告
    /// </summary>
    public class DataIntegrityReport
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        
        // 孤立数据
        public List<Document> OrphanedDocuments { get; set; } = new();
        public List<NovelProject> OrphanedNovelProjects { get; set; } = new();
        public List<Chapter> OrphanedChapters { get; set; } = new();
        public List<Character> OrphanedCharacters { get; set; } = new();
        public List<VectorRecord> OrphanedVectorRecords { get; set; } = new();
        public List<WorldSetting> OrphanedWorldSettings { get; set; } = new();
        
        // 重复ID
        public List<int> DuplicateProjectIds { get; set; } = new();
        public List<int> DuplicateDocumentIds { get; set; } = new();
        public List<int> DuplicateNovelProjectIds { get; set; } = new();
        
        /// <summary>
        /// 是否有任何问题
        /// </summary>
        public bool HasAnyIssues => 
            OrphanedDocuments.Count > 0 || 
            OrphanedNovelProjects.Count > 0 || 
            OrphanedChapters.Count > 0 || 
            OrphanedCharacters.Count > 0 || 
            OrphanedVectorRecords.Count > 0 || 
            OrphanedWorldSettings.Count > 0 ||
            DuplicateProjectIds.Count > 0 ||
            DuplicateDocumentIds.Count > 0 ||
            DuplicateNovelProjectIds.Count > 0;
        
        /// <summary>
        /// 总问题数量
        /// </summary>
        public int TotalIssuesCount => 
            OrphanedDocuments.Count + 
            OrphanedNovelProjects.Count + 
            OrphanedChapters.Count + 
            OrphanedCharacters.Count + 
            OrphanedVectorRecords.Count + 
            OrphanedWorldSettings.Count +
            DuplicateProjectIds.Count +
            DuplicateDocumentIds.Count +
            DuplicateNovelProjectIds.Count;
    }

    /// <summary>
    /// 数据清理选项
    /// </summary>
    public class DataCleanupOptions
    {
        /// <summary>
        /// 是否清理孤立的文档
        /// </summary>
        public bool CleanOrphanedDocuments { get; set; } = true;
        
        /// <summary>
        /// 是否清理孤立的小说项目
        /// </summary>
        public bool CleanOrphanedNovelProjects { get; set; } = true;
        
        /// <summary>
        /// 是否清理孤立的章节
        /// </summary>
        public bool CleanOrphanedChapters { get; set; } = true;
        
        /// <summary>
        /// 是否清理孤立的角色
        /// </summary>
        public bool CleanOrphanedCharacters { get; set; } = true;
        
        /// <summary>
        /// 是否清理孤立的向量记录
        /// </summary>
        public bool CleanOrphanedVectorRecords { get; set; } = true;
        
        /// <summary>
        /// 是否清理孤立的世界设定
        /// </summary>
        public bool CleanOrphanedWorldSettings { get; set; } = true;
        
        /// <summary>
        /// 是否创建备份
        /// </summary>
        public bool CreateBackup { get; set; } = true;
        
        /// <summary>
        /// 备份目录
        /// </summary>
        public string BackupDirectory { get; set; } = string.Empty;
    }

    /// <summary>
    /// 数据备份信息
    /// </summary>
    public class DataBackupInfo
    {
        public string BackupPath { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public long BackupSize { get; set; }
        public string Description { get; set; } = string.Empty;
        public bool IsValid { get; set; }
    }
}

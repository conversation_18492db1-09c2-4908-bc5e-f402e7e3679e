using System;
using System.Threading;

namespace DocumentCreationSystem;

/// <summary>
/// 程序入口点
/// </summary>
public class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        try
        {
            // 输出启动信息到控制台
            Console.WriteLine("正在启动文档管理及AI创作系统...");
            Console.WriteLine($"当前时间: {DateTime.Now}");
            Console.WriteLine($"工作目录: {Environment.CurrentDirectory}");

            // 启动WPF应用程序
            Console.WriteLine("创建App实例...");
            var app = new App();

            Console.WriteLine("初始化组件...");
            app.InitializeComponent();

            Console.WriteLine("运行应用程序...");
            app.Run();
        }
        catch (Exception ex)
        {
            // 输出错误到控制台
            Console.WriteLine($"程序启动失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");

            // 在发生错误时显示消息框
            System.Windows.MessageBox.Show(
                $"程序启动失败: {ex.Message}\n\n详细错误:\n{ex}",
                "启动错误",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Error);
        }
    }
}



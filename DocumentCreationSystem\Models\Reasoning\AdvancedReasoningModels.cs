namespace DocumentCreationSystem.Models.Reasoning
{
    /// <summary>
    /// 事实依赖关系图
    /// </summary>
    public class FactDependencyGraph
    {
        /// <summary>
        /// 图ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 根事实ID
        /// </summary>
        public string RootFactId { get; set; } = string.Empty;

        /// <summary>
        /// 依赖节点列表
        /// </summary>
        public List<DependencyNode> Nodes { get; set; } = new();

        /// <summary>
        /// 依赖边列表
        /// </summary>
        public List<DependencyEdge> Edges { get; set; } = new();

        /// <summary>
        /// 图的深度
        /// </summary>
        public int Depth { get; set; } = 0;

        /// <summary>
        /// 图创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 依赖节点
    /// </summary>
    public class DependencyNode
    {
        /// <summary>
        /// 节点ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 关联的事实
        /// </summary>
        public ReasoningFact Fact { get; set; } = new();

        /// <summary>
        /// 节点层级
        /// </summary>
        public int Level { get; set; } = 0;

        /// <summary>
        /// 节点类型
        /// </summary>
        public NodeType Type { get; set; } = NodeType.Fact;

        /// <summary>
        /// 节点权重
        /// </summary>
        public double Weight { get; set; } = 1.0;
    }

    /// <summary>
    /// 节点类型
    /// </summary>
    public enum NodeType
    {
        /// <summary>
        /// 事实节点
        /// </summary>
        Fact,

        /// <summary>
        /// 规则节点
        /// </summary>
        Rule,

        /// <summary>
        /// 假设节点
        /// </summary>
        Hypothesis,

        /// <summary>
        /// 推导节点
        /// </summary>
        Derived
    }

    /// <summary>
    /// 依赖边
    /// </summary>
    public class DependencyEdge
    {
        /// <summary>
        /// 边ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 源节点ID
        /// </summary>
        public string FromNodeId { get; set; } = string.Empty;

        /// <summary>
        /// 目标节点ID
        /// </summary>
        public string ToNodeId { get; set; } = string.Empty;

        /// <summary>
        /// 边类型
        /// </summary>
        public EdgeType Type { get; set; } = EdgeType.Dependency;

        /// <summary>
        /// 边权重
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 关联的规则ID（如果适用）
        /// </summary>
        public string? RuleId { get; set; }
    }

    /// <summary>
    /// 边类型
    /// </summary>
    public enum EdgeType
    {
        /// <summary>
        /// 依赖关系
        /// </summary>
        Dependency,

        /// <summary>
        /// 推导关系
        /// </summary>
        Derivation,

        /// <summary>
        /// 支持关系
        /// </summary>
        Support,

        /// <summary>
        /// 冲突关系
        /// </summary>
        Conflict
    }

    /// <summary>
    /// 概率推理查询
    /// </summary>
    public class ProbabilisticQuery
    {
        /// <summary>
        /// 查询ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 查询事实
        /// </summary>
        public ReasoningFact QueryFact { get; set; } = new();

        /// <summary>
        /// 证据事实列表
        /// </summary>
        public List<ReasoningFact> Evidence { get; set; } = new();

        /// <summary>
        /// 查询类型
        /// </summary>
        public ProbabilisticQueryType Type { get; set; } = ProbabilisticQueryType.Probability;

        /// <summary>
        /// 推理方法
        /// </summary>
        public ProbabilisticMethod Method { get; set; } = ProbabilisticMethod.BayesianInference;

        /// <summary>
        /// 最大迭代次数
        /// </summary>
        public int MaxIterations { get; set; } = 1000;

        /// <summary>
        /// 收敛阈值
        /// </summary>
        public double ConvergenceThreshold { get; set; } = 0.001;
    }

    /// <summary>
    /// 概率查询类型
    /// </summary>
    public enum ProbabilisticQueryType
    {
        /// <summary>
        /// 计算概率
        /// </summary>
        Probability,

        /// <summary>
        /// 最可能解释
        /// </summary>
        MostProbableExplanation,

        /// <summary>
        /// 最大后验概率
        /// </summary>
        MaximumAPosteriori,

        /// <summary>
        /// 边际概率
        /// </summary>
        MarginalProbability
    }

    /// <summary>
    /// 概率推理方法
    /// </summary>
    public enum ProbabilisticMethod
    {
        /// <summary>
        /// 贝叶斯推理
        /// </summary>
        BayesianInference,

        /// <summary>
        /// 马尔可夫链蒙特卡罗
        /// </summary>
        MCMC,

        /// <summary>
        /// 变分推理
        /// </summary>
        VariationalInference,

        /// <summary>
        /// 信念传播
        /// </summary>
        BeliefPropagation
    }

    /// <summary>
    /// 概率推理结果
    /// </summary>
    public class ProbabilisticReasoningResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 查询ID
        /// </summary>
        public string QueryId { get; set; } = string.Empty;

        /// <summary>
        /// 计算出的概率
        /// </summary>
        public double Probability { get; set; } = 0.0;

        /// <summary>
        /// 概率分布
        /// </summary>
        public Dictionary<string, double> ProbabilityDistribution { get; set; } = new();

        /// <summary>
        /// 置信区间
        /// </summary>
        public ConfidenceInterval? ConfidenceInterval { get; set; }

        /// <summary>
        /// 是否收敛
        /// </summary>
        public bool HasConverged { get; set; } = false;

        /// <summary>
        /// 迭代次数
        /// </summary>
        public int Iterations { get; set; } = 0;

        /// <summary>
        /// 计算时间（毫秒）
        /// </summary>
        public long ComputationTimeMs { get; set; } = 0;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 置信区间
    /// </summary>
    public class ConfidenceInterval
    {
        /// <summary>
        /// 下界
        /// </summary>
        public double LowerBound { get; set; } = 0.0;

        /// <summary>
        /// 上界
        /// </summary>
        public double UpperBound { get; set; } = 1.0;

        /// <summary>
        /// 置信水平
        /// </summary>
        public double ConfidenceLevel { get; set; } = 0.95;
    }

    /// <summary>
    /// 推理案例（用于类比推理）
    /// </summary>
    public class ReasoningCase
    {
        /// <summary>
        /// 案例ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 案例名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 案例描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 案例特征
        /// </summary>
        public Dictionary<string, object> Features { get; set; } = new();

        /// <summary>
        /// 案例结果
        /// </summary>
        public object? Outcome { get; set; }

        /// <summary>
        /// 案例上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// 案例标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 案例创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 类比推理结果
    /// </summary>
    public class AnalogyReasoningResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 源案例ID
        /// </summary>
        public string SourceCaseId { get; set; } = string.Empty;

        /// <summary>
        /// 目标案例ID
        /// </summary>
        public string TargetCaseId { get; set; } = string.Empty;

        /// <summary>
        /// 相似度评分 (0.0 - 1.0)
        /// </summary>
        public double SimilarityScore { get; set; } = 0.0;

        /// <summary>
        /// 特征映射
        /// </summary>
        public Dictionary<string, string> FeatureMapping { get; set; } = new();

        /// <summary>
        /// 预测结果
        /// </summary>
        public object? PredictedOutcome { get; set; }

        /// <summary>
        /// 预测置信度
        /// </summary>
        public double PredictionConfidence { get; set; } = 0.0;

        /// <summary>
        /// 类比解释
        /// </summary>
        public string Explanation { get; set; } = string.Empty;

        /// <summary>
        /// 关键相似点
        /// </summary>
        public List<string> KeySimilarities { get; set; } = new();

        /// <summary>
        /// 关键差异点
        /// </summary>
        public List<string> KeyDifferences { get; set; } = new();
    }

    /// <summary>
    /// 因果推理结果
    /// </summary>
    public class CausalReasoningResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 原因事实ID
        /// </summary>
        public string CauseFactId { get; set; } = string.Empty;

        /// <summary>
        /// 结果事实ID
        /// </summary>
        public string EffectFactId { get; set; } = string.Empty;

        /// <summary>
        /// 因果强度 (0.0 - 1.0)
        /// </summary>
        public double CausalStrength { get; set; } = 0.0;

        /// <summary>
        /// 因果类型
        /// </summary>
        public CausalType Type { get; set; } = CausalType.Direct;

        /// <summary>
        /// 中介变量
        /// </summary>
        public List<ReasoningFact> MediatingFactors { get; set; } = new();

        /// <summary>
        /// 混淆变量
        /// </summary>
        public List<ReasoningFact> ConfoundingFactors { get; set; } = new();

        /// <summary>
        /// 因果路径
        /// </summary>
        public List<CausalPath> CausalPaths { get; set; } = new();

        /// <summary>
        /// 因果解释
        /// </summary>
        public string Explanation { get; set; } = string.Empty;

        /// <summary>
        /// 统计显著性
        /// </summary>
        public double StatisticalSignificance { get; set; } = 0.0;
    }

    /// <summary>
    /// 因果类型
    /// </summary>
    public enum CausalType
    {
        /// <summary>
        /// 直接因果
        /// </summary>
        Direct,

        /// <summary>
        /// 间接因果
        /// </summary>
        Indirect,

        /// <summary>
        /// 必要条件
        /// </summary>
        Necessary,

        /// <summary>
        /// 充分条件
        /// </summary>
        Sufficient,

        /// <summary>
        /// 必要充分条件
        /// </summary>
        NecessaryAndSufficient,

        /// <summary>
        /// 概率因果
        /// </summary>
        Probabilistic
    }

    /// <summary>
    /// 因果路径
    /// </summary>
    public class CausalPath
    {
        /// <summary>
        /// 路径ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 路径中的事实序列
        /// </summary>
        public List<ReasoningFact> PathFacts { get; set; } = new();

        /// <summary>
        /// 路径强度
        /// </summary>
        public double Strength { get; set; } = 0.0;

        /// <summary>
        /// 路径长度
        /// </summary>
        public int Length => PathFacts.Count;

        /// <summary>
        /// 路径描述
        /// </summary>
        public string Description { get; set; } = string.Empty;
    }
}

using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 写书流程配置服务接口
    /// </summary>
    public interface IWritingWorkflowConfigService
    {
        /// <summary>
        /// 获取所有流程配置
        /// </summary>
        Task<List<WritingWorkflowConfig>> GetAllConfigsAsync();

        /// <summary>
        /// 根据ID获取流程配置
        /// </summary>
        Task<WritingWorkflowConfig?> GetConfigByIdAsync(int id);

        /// <summary>
        /// 获取默认流程配置
        /// </summary>
        Task<WritingWorkflowConfig> GetDefaultConfigAsync();

        /// <summary>
        /// 保存流程配置
        /// </summary>
        Task<WritingWorkflowConfig> SaveConfigAsync(WritingWorkflowConfig config);

        /// <summary>
        /// 更新流程配置
        /// </summary>
        Task<WritingWorkflowConfig> UpdateConfigAsync(WritingWorkflowConfig config);

        /// <summary>
        /// 删除流程配置
        /// </summary>
        Task<bool> DeleteConfigAsync(int id);

        /// <summary>
        /// 设置默认配置
        /// </summary>
        Task<bool> SetDefaultConfigAsync(int id);

        /// <summary>
        /// 创建默认配置
        /// </summary>
        Task<WritingWorkflowConfig> CreateDefaultConfigAsync();

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        Task<bool> ExportConfigAsync(int id, string filePath);

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        Task<WritingWorkflowConfig?> ImportConfigAsync(string filePath);

        /// <summary>
        /// 验证配置的完整性
        /// </summary>
        Task<bool> ValidateConfigAsync(WritingWorkflowConfig config);

        /// <summary>
        /// 获取配置的预览信息
        /// </summary>
        Task<string> GetConfigPreviewAsync(int id);

        /// <summary>
        /// 应用配置到分步写书服务
        /// </summary>
        Task<bool> ApplyConfigToServiceAsync(int configId);

        /// <summary>
        /// 获取当前应用的配置
        /// </summary>
        Task<WritingWorkflowConfig?> GetCurrentAppliedConfigAsync();
    }
}

# 右键重命名功能说明

## 功能概述

在项目导航页面新增了右键重命名功能，用户可以通过右键菜单快速重命名文件和文件夹，提供了直观的重命名对话框和完善的输入验证。

## 功能特点

### 🎯 智能重命名
- **文件重命名**：支持重命名各种格式的文件
- **文件夹重命名**：支持重命名文件夹和子目录
- **智能选择**：文件重命名时自动选中文件名部分（不包括扩展名）
- **实时验证**：输入过程中实时验证名称有效性

### 🛡️ 安全保护
- **名称验证**：检查文件名是否包含无效字符
- **保留名称检查**：防止使用系统保留名称（如CON、PRN等）
- **重复检查**：检查目标名称是否已存在
- **长度限制**：限制文件名长度不超过255字符

### 💡 用户体验
- **直观界面**：Material Design风格的现代化对话框
- **实时反馈**：输入验证结果实时显示
- **快捷操作**：支持Enter确认、Escape取消
- **智能提示**：提供详细的输入提示和错误信息

## 使用方法

### 1. 选择目标
在项目导航树中选择要重命名的文件或文件夹。

### 2. 打开重命名对话框
右键点击选中的项目，在弹出的上下文菜单中选择"重命名"。

### 3. 输入新名称
在重命名对话框中：
- 输入框会显示当前名称
- 文件重命名时自动选中文件名部分
- 文件夹重命名时选中全部名称

### 4. 验证和确认
- 系统会实时验证输入的名称
- 绿色提示表示名称有效
- 红色提示表示名称无效
- 点击"确定"完成重命名

## 界面设计

### 重命名对话框
```
┌─────────────────────────────────────┐
│ 🔄 重命名文件                        │
├─────────────────────────────────────┤
│ 新名称:                             │
│ ┌─────────────────────────────────┐ │
│ │ 当前文件名.txt                   │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ✅ 名称有效                         │
│                                     │
│              [取消]    [确定]        │
└─────────────────────────────────────┘
```

### 右键菜单位置
```
右键菜单:
├── 复制
├── 粘贴
├── ─────────
├── 重命名    ← 新增功能
├── ─────────
├── 查看路径
└── 删除
```

## 验证规则

### 无效字符检查
文件名不能包含以下字符：
- `\` (反斜杠)
- `/` (正斜杠)
- `:` (冒号)
- `*` (星号)
- `?` (问号)
- `"` (双引号)
- `<` (小于号)
- `>` (大于号)
- `|` (竖线)

### 系统保留名称
不能使用以下系统保留名称：
- `CON`, `PRN`, `AUX`, `NUL`
- `COM1` - `COM9`
- `LPT1` - `LPT9`

### 其他限制
- 名称不能为空或只包含空格
- 名称长度不能超过255字符
- 名称不能以点(.)或空格结尾

## 技术实现

### 核心组件

#### 1. 右键菜单项 (MainWindow.xaml)
```xml
<MenuItem Header="重命名" Click="RenameItem_Click">
    <MenuItem.Icon>
        <materialDesign:PackIcon Kind="RenameBox"/>
    </MenuItem.Icon>
</MenuItem>
```

#### 2. 重命名事件处理 (MainWindow.xaml.cs)
- `RenameItem_Click`: 主重命名逻辑
- `ShowRenameDialogAsync`: 显示重命名对话框
- `IsValidFileName`: 文件名验证

#### 3. 重命名对话框 (RenameDialog)
- 现代化的Material Design界面
- 实时输入验证
- 智能文本选择
- 快捷键支持

### 重命名流程
```
1. 用户右键选择"重命名"
   ↓
2. 验证选中项目的有效性
   ↓
3. 显示重命名对话框
   ↓
4. 用户输入新名称
   ↓
5. 实时验证输入
   ↓
6. 用户确认重命名
   ↓
7. 检查目标名称是否存在
   ↓
8. 执行文件/文件夹重命名
   ↓
9. 更新项目树显示
   ↓
10. 刷新项目导航
```

## 错误处理

### 常见错误及解决方案

#### 1. 无效字符
- **错误**：文件名包含无效字符
- **提示**：❌ 名称包含无效字符或为系统保留名称
- **解决**：移除无效字符，使用有效的文件名

#### 2. 名称已存在
- **错误**：目标名称已存在
- **提示**：目标名称已存在，请选择其他名称
- **解决**：使用不同的名称

#### 3. 权限不足
- **错误**：没有重命名权限
- **提示**：重命名失败: 访问被拒绝
- **解决**：以管理员身份运行或检查文件权限

#### 4. 文件被占用
- **错误**：文件正在被其他程序使用
- **提示**：重命名失败: 文件正在使用中
- **解决**：关闭占用文件的程序后重试

## 使用场景

### 1. 文档整理
- 重命名章节文件以保持顺序
- 统一文件命名格式
- 修正文件名中的错误

### 2. 项目管理
- 重命名项目文件夹
- 调整文件组织结构
- 更新文件版本标识

### 3. 内容创作
- 重命名草稿文件
- 调整章节标题
- 整理素材文件

## 实时验证反馈

### 验证状态显示
- **✅ 名称有效** (绿色) - 可以进行重命名
- **❌ 名称包含无效字符** (红色) - 需要修正
- **💡 名称未更改** (灰色) - 与原名称相同
- **提示信息** (灰色) - 显示输入规则

### 按钮状态
- **确定按钮启用**：名称有效且与原名称不同
- **确定按钮禁用**：名称无效或未更改

## 快捷键支持

- **Enter**：确认重命名（当名称有效时）
- **Escape**：取消重命名
- **Tab**：在控件间切换焦点

## 智能选择功能

### 文件重命名
- 自动选中文件名部分（不包括扩展名）
- 便于快速修改文件名而保持扩展名不变

### 文件夹重命名
- 选中整个文件夹名称
- 便于完整修改文件夹名称

## 日志记录

系统会记录重命名操作的详细日志：
- 重命名开始时间
- 原名称和新名称
- 操作结果（成功/失败）
- 错误信息（如果有）

## 注意事项

### ⚠️ 重要提醒
1. **备份重要文件**：重命名前建议备份重要文件
2. **检查文件引用**：重命名可能影响其他文件的引用关系
3. **权限要求**：确保对文件/文件夹有重命名权限
4. **程序占用**：确保文件未被其他程序占用

### 💡 使用建议
1. **批量重命名**：对于大量文件，建议使用专门的批量重命名工具
2. **命名规范**：建立统一的文件命名规范
3. **版本管理**：如果使用版本控制，注意重命名对版本历史的影响

## 总结

右键重命名功能为用户提供了便捷的文件和文件夹重命名工具，具有以下优势：

1. **操作简便**：右键即可快速重命名
2. **界面友好**：现代化的Material Design界面
3. **验证完善**：实时验证确保名称有效性
4. **安全可靠**：完善的错误处理和保护机制
5. **体验优良**：智能选择和快捷键支持

该功能特别适合需要频繁整理和管理项目文件的用户，提供了高效、安全的文件重命名解决方案。

<Window x:Class="DocumentCreationSystem.Views.Dialogs.EnhancedMessageDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="消息" 
        Height="Auto" Width="500" 
        MinWidth="400" MaxWidth="800"
        MinHeight="200" MaxHeight="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <Window.Resources>
        <!-- 图标样式 -->
        <Style x:Key="IconStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,4,16,0"/>
        </Style>
    </Window.Resources>

    <materialDesign:Card Margin="8" materialDesign:ElevationAssist.Elevation="Dp8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="TitleText" 
                               Grid.Column="0"
                               Text="消息"
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Foreground="White"
                               VerticalAlignment="Center"/>

                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Foreground="White"
                            ToolTip="关闭"
                            Click="Close_Click">
                        <materialDesign:PackIcon Kind="Close"/>
                    </Button>
                </Grid>
            </materialDesign:ColorZone>

            <!-- 内容区域 -->
            <ScrollViewer Grid.Row="1" 
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Auto"
                          Padding="24,16">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 图标 -->
                    <materialDesign:PackIcon x:Name="MessageIcon"
                                             Grid.Column="0"
                                             Kind="Information"
                                             Style="{StaticResource IconStyle}"/>

                    <!-- 消息内容 -->
                    <StackPanel Grid.Column="1">
                        <!-- 主要消息 -->
                        <TextBlock x:Name="MessageText"
                                   Text="消息内容"
                                   Style="{StaticResource MaterialDesignBody1TextBlock}"
                                   TextWrapping="Wrap"
                                   Margin="0,0,0,12"/>

                        <!-- 详细信息（可选） -->
                        <Expander x:Name="DetailsExpander"
                                  Header="详细信息"
                                  Visibility="Collapsed"
                                  Margin="0,8,0,0">
                            <TextBlock x:Name="DetailsText"
                                       Text=""
                                       Style="{StaticResource MaterialDesignBody2TextBlock}"
                                       TextWrapping="Wrap"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                       Margin="0,8,0,0"/>
                        </Expander>

                        <!-- 自定义内容区域 -->
                        <ContentPresenter x:Name="CustomContentPresenter"
                                          Margin="0,12,0,0"/>
                    </StackPanel>
                </Grid>
            </ScrollViewer>

            <!-- 按钮区域 -->
            <Border Grid.Row="2" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,1,0,0"
                    Padding="24,16">
                <StackPanel x:Name="ButtonPanel"
                            Orientation="Horizontal"
                            HorizontalAlignment="Right">
                    <!-- 按钮将在代码中动态添加 -->
                </StackPanel>
            </Border>
        </Grid>
    </materialDesign:Card>
</Window>

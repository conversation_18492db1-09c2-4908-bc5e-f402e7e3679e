using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 主题管理服务接口
    /// </summary>
    public interface IThemeService
    {
        /// <summary>
        /// 主题变更事件
        /// </summary>
        event EventHandler<ThemeChangedEventArgs>? ThemeChanged;
        /// <summary>
        /// 获取当前主题配置
        /// </summary>
        ThemeConfig GetCurrentTheme();

        /// <summary>
        /// 应用主题配置
        /// </summary>
        Task ApplyThemeAsync(ThemeConfig themeConfig);

        /// <summary>
        /// 获取预设主题列表
        /// </summary>
        List<PresetTheme> GetPresetThemes();

        /// <summary>
        /// 重置为默认主题
        /// </summary>
        Task ResetToDefaultAsync();

        /// <summary>
        /// 从配置文件加载主题
        /// </summary>
        Task LoadThemeFromConfigAsync();
    }
}

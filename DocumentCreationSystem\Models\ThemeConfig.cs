namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 主题配置类
    /// </summary>
    public class ThemeConfig
    {
        /// <summary>
        /// 基础主题：Light 或 Dark
        /// </summary>
        public string BaseTheme { get; set; } = "Light";

        /// <summary>
        /// 主色调（十六进制颜色值）
        /// </summary>
        public string PrimaryColor { get; set; } = "#2196F3";

        /// <summary>
        /// 次要色调（十六进制颜色值）
        /// </summary>
        public string SecondaryColor { get; set; } = "#03DAC6";

        /// <summary>
        /// 背景色（十六进制颜色值）
        /// </summary>
        public string BackgroundColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 表面色（卡片背景等）
        /// </summary>
        public string SurfaceColor { get; set; } = "#FFFFFF";

        /// <summary>
        /// 文本颜色
        /// </summary>
        public string TextColor { get; set; } = "#000000";

        /// <summary>
        /// 强调色
        /// </summary>
        public string AccentColor { get; set; } = "#1976D2";

        /// <summary>
        /// 主题名称
        /// </summary>
        public string Name { get; set; } = "自定义主题";

        /// <summary>
        /// 主题描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 是否为用户自定义主题
        /// </summary>
        public bool IsCustom { get; set; } = false;
    }

    /// <summary>
    /// 预设主题类
    /// </summary>
    public class PresetTheme
    {
        /// <summary>
        /// 主题名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 主题描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 基础主题
        /// </summary>
        public string BaseTheme { get; set; } = "Light";

        /// <summary>
        /// 主色调
        /// </summary>
        public string PrimaryColor { get; set; } = "#2196F3";

        /// <summary>
        /// 次要色调
        /// </summary>
        public string SecondaryColor { get; set; } = "#03DAC6";

        /// <summary>
        /// 强调色
        /// </summary>
        public string AccentColor { get; set; } = "#1976D2";

        /// <summary>
        /// 预览图片路径（可选）
        /// </summary>
        public string? PreviewImagePath { get; set; }

        /// <summary>
        /// 主题分类
        /// </summary>
        public string Category => BaseTheme == "Dark" ? "暗黑主题" : "浅色主题";

        /// <summary>
        /// 是否为暗黑主题
        /// </summary>
        public bool IsDarkTheme => BaseTheme == "Dark";

        /// <summary>
        /// 主题标签（用于搜索和分类）
        /// </summary>
        public List<string> Tags { get; set; } = new List<string>();

        /// <summary>
        /// 转换为主题配置
        /// </summary>
        public ThemeConfig ToThemeConfig()
        {
            return new ThemeConfig
            {
                Name = Name,
                Description = Description,
                BaseTheme = BaseTheme,
                PrimaryColor = PrimaryColor,
                SecondaryColor = SecondaryColor,
                AccentColor = AccentColor,
                IsCustom = false
            };
        }
    }
}

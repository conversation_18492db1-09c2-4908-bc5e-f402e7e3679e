# AI工具系统使用说明

## 概述

AI工具系统为文档管理及创作系统提供了丰富的工具集，包括代码搜索、文件操作、开发辅助、网络工具和MCP集成等功能。这些工具可以通过AI助手调用，大大提升工作效率。

## 工具分类

### 1. 代码搜索工具

#### 代码库搜索 (search-codebase)
- **功能**: 基于自然语言描述的智能代码检索
- **参数**:
  - `query` (必需): 搜索查询
  - `fileTypes` (可选): 文件类型，默认"all"
  - `maxResults` (可选): 最大结果数，默认10
- **使用示例**: 搜索"用户登录相关的代码"

#### 正则表达式搜索 (search-by-regex)
- **功能**: 使用正则表达式进行精确匹配
- **参数**:
  - `pattern` (必需): 正则表达式模式
  - `caseSensitive` (可选): 是否区分大小写，默认false
  - `includeFiles` (可选): 包含的文件模式，默认"*.*"
- **使用示例**: 搜索所有以"Test"开头的类名

### 2. 文件操作工具

#### 批量查看文件 (view-files)
- **功能**: 同时查看多个文件的内容
- **参数**:
  - `filePaths` (必需): 文件路径列表
  - `encoding` (可选): 编码格式，默认"UTF-8"
- **使用示例**: 查看项目中的所有配置文件

#### 目录结构浏览 (list-dir)
- **功能**: 浏览目录结构和文件列表
- **参数**:
  - `path` (必需): 目录路径
  - `recursive` (可选): 是否递归查看，默认false
  - `showHidden` (可选): 是否显示隐藏文件，默认false
- **使用示例**: 查看项目根目录的文件结构

#### 文件创建/覆写 (write-to-file)
- **功能**: 创建新文件或覆写现有文件
- **参数**:
  - `filePath` (必需): 文件路径
  - `content` (必需): 文件内容
  - `encoding` (可选): 编码格式，默认"UTF-8"
  - `createBackup` (可选): 是否创建备份，默认true
- **使用示例**: 创建新的配置文件

#### 文件编辑 (update-file)
- **功能**: 使用搜索替换方式编辑文件
- **参数**:
  - `filePath` (必需): 文件路径
  - `searchPattern` (必需): 搜索模式
  - `replacement` (必需): 替换内容
  - `replaceAll` (可选): 是否替换全部，默认false
- **使用示例**: 批量更新代码中的变量名

### 3. 开发辅助工具

#### 命令行执行 (run-command)
- **功能**: 执行命令行命令
- **参数**:
  - `command` (必需): 要执行的命令
  - `workingDirectory` (可选): 工作目录，默认"."
  - `timeout` (可选): 超时时间(秒)，默认30
  - `shell` (可选): Shell类型，默认"PowerShell"
- **使用示例**: 运行构建脚本或测试命令

#### 本地服务预览 (open-preview)
- **功能**: 启动本地预览服务
- **参数**:
  - `port` (可选): 端口号，默认8080
  - `path` (可选): 预览路径，默认"/"
  - `openBrowser` (可选): 是否打开浏览器，默认true
- **使用示例**: 预览Web应用或文档

### 4. 网络工具

#### 联网搜索 (web-search)
- **功能**: 进行网络搜索获取信息
- **参数**:
  - `query` (必需): 搜索查询
  - `maxResults` (可选): 最大结果数，默认5
  - `language` (可选): 语言，默认"zh-CN"
- **使用示例**: 搜索技术文档或解决方案

### 5. MCP集成工具

#### Excel自动化 (excel-automation)
- **功能**: 自动化Excel操作
- **参数**:
  - `operation` (必需): 操作类型(读取/写入/格式化/计算/图表)
  - `filePath` (必需): Excel文件路径
  - `sheetName` (可选): 工作表名称，默认"Sheet1"
- **使用示例**: 自动生成数据报表

#### Blender三维建模 (blender-automation)
- **功能**: 自动化Blender操作
- **参数**:
  - `operation` (必需): 操作类型(创建模型/渲染/动画/导出)
  - `scriptPath` (可选): 脚本路径
  - `outputPath` (可选): 输出路径
- **使用示例**: 批量渲染3D模型

#### 浏览器自动化 (browser-automation)
- **功能**: 自动化浏览器操作
- **参数**:
  - `action` (必需): 操作类型(导航/点击/输入/截图/提取数据)
  - `url` (可选): 目标URL
  - `selector` (可选): 元素选择器
  - `value` (可选): 输入值
- **使用示例**: 自动化测试或数据抓取

## 使用方法

### 1. 通过GUI界面使用

1. 在主界面中找到"AI工具箱"面板
2. 浏览或搜索需要的工具
3. 点击工具按钮
4. 在参数对话框中设置参数
5. 点击"执行工具"按钮
6. 查看执行结果

### 2. 通过代码调用

```csharp
// 获取AI工具服务
var aiToolsService = serviceProvider.GetRequiredService<IAIToolsService>();

// 设置参数
var parameters = new Dictionary<string, object>
{
    ["query"] = "搜索内容",
    ["maxResults"] = 10
};

// 执行工具
var result = await aiToolsService.ExecuteToolAsync("search-codebase", parameters);

// 处理结果
if (result.IsSuccess)
{
    Console.WriteLine($"执行成功: {result.Message}");
    Console.WriteLine($"结果数据: {JsonSerializer.Serialize(result.Data)}");
}
else
{
    Console.WriteLine($"执行失败: {result.ErrorDetails}");
}
```

### 3. 参数验证

在执行工具前，可以先验证参数：

```csharp
var validation = await aiToolsService.ValidateParametersAsync(toolId, parameters);
if (!validation.IsValid)
{
    Console.WriteLine($"参数验证失败: {string.Join(", ", validation.Errors)}");
    return;
}
```

## 工具管理

### 获取工具列表

```csharp
// 获取所有可用工具
var allTools = await aiToolsService.GetAvailableToolsAsync();

// 按类别获取工具
var codeTools = await aiToolsService.GetToolsByCategoryAsync("代码搜索");

// 搜索工具
var searchResults = await aiToolsService.SearchToolsAsync("文件");
```

### 查看工具统计

```csharp
var statistics = await aiToolsService.GetToolStatisticsAsync();
Console.WriteLine($"总工具数: {statistics.TotalTools}");
Console.WriteLine($"使用次数: {statistics.TotalUsageCount}");
Console.WriteLine($"成功率: {statistics.AverageSuccessRate:F1}%");
```

### 查看使用历史

```csharp
var history = await aiToolsService.GetToolUsageHistoryAsync("search-codebase", 10);
foreach (var record in history)
{
    Console.WriteLine($"{record.UsedAt}: {(record.IsSuccess ? "成功" : "失败")} - {record.ExecutionTimeMs}ms");
}
```

## 注意事项

1. **权限要求**: 某些工具可能需要特定的系统权限
2. **网络连接**: 网络工具需要稳定的网络连接
3. **文件路径**: 确保文件路径正确且有访问权限
4. **参数格式**: 严格按照参数要求提供正确格式的数据
5. **错误处理**: 始终检查执行结果并处理可能的错误

## 扩展开发

如需添加自定义工具，请参考现有工具的实现模式：

1. 在`ProjectToolsService`中注册新工具
2. 实现工具的执行逻辑
3. 在`AIToolsService`中添加工具定义
4. 更新GUI界面（如需要）

## 技术支持

如遇到问题，请查看：
1. 执行日志中的详细错误信息
2. 参数验证结果
3. 工具使用历史记录
4. 系统监控信息

更多技术细节请参考源代码注释和开发文档。

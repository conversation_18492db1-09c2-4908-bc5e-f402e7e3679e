using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 任务分解结果
    /// </summary>
    public class TaskDecompositionResult
    {
        public string OriginalTask { get; set; } = string.Empty;
        public List<SubTask> SubTasks { get; set; } = new();
        public TaskDependencyGraph DependencyGraph { get; set; } = new();
        public TaskExecutionPlan ExecutionPlan { get; set; } = new();
        public TaskComplexityAnalysis ComplexityAnalysis { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
        public List<string> RecommendedResources { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 子任务
    /// </summary>
    public class SubTask
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TaskType Type { get; set; }
        public TaskPriority Priority { get; set; }
        public TaskStatus Status { get; set; }
        public int EstimatedMinutes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public string? Result { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public string? AssignedAgent { get; set; }
        public float ProgressPercentage { get; set; } = 0f;
    }

    /// <summary>
    /// 任务类型
    /// </summary>
    public enum TaskType
    {
        Research,       // 研究和信息收集
        Planning,       // 规划和设计
        Creation,       // 内容创作
        Review,         // 审查和验证
        Optimization,   // 优化和改进
        Integration,    // 整合和组装
        Testing,        // 测试和验证
        Documentation,  // 文档编写
        Communication,  // 沟通协调
        Analysis        // 分析处理
    }

    // TaskPriority 已在其他地方定义

    // TaskStatus 已在其他地方定义

    /// <summary>
    /// 任务上下文
    /// </summary>
    public class TaskContext
    {
        public string ProjectId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string> AvailableResources { get; set; } = new();
        public TimeSpan? TimeConstraint { get; set; }
        public string? QualityRequirement { get; set; }
        public List<string> Constraints { get; set; } = new();
        public string? PreferredStyle { get; set; }
        public Dictionary<string, string> EnvironmentVariables { get; set; } = new();
    }

    /// <summary>
    /// 任务依赖图
    /// </summary>
    public class TaskDependencyGraph
    {
        private readonly Dictionary<string, SubTask> _nodes = new();
        private readonly Dictionary<string, List<string>> _edges = new();

        public void AddNode(SubTask task)
        {
            _nodes[task.Id] = task;
            if (!_edges.ContainsKey(task.Id))
            {
                _edges[task.Id] = new List<string>();
            }
        }

        public void AddEdge(SubTask fromTask, SubTask toTask)
        {
            if (!_nodes.ContainsKey(fromTask.Id))
                AddNode(fromTask);
            if (!_nodes.ContainsKey(toTask.Id))
                AddNode(toTask);

            _edges[fromTask.Id].Add(toTask.Id);
        }

        public List<SubTask> GetDependencies(string taskId)
        {
            var dependencies = new List<SubTask>();
            foreach (var kvp in _edges)
            {
                if (kvp.Value.Contains(taskId))
                {
                    dependencies.Add(_nodes[kvp.Key]);
                }
            }
            return dependencies;
        }

        public List<SubTask> GetDependents(string taskId)
        {
            var dependents = new List<SubTask>();
            if (_edges.ContainsKey(taskId))
            {
                foreach (var dependentId in _edges[taskId])
                {
                    dependents.Add(_nodes[dependentId]);
                }
            }
            return dependents;
        }

        public bool HasCycle()
        {
            var visited = new HashSet<string>();
            var recursionStack = new HashSet<string>();

            foreach (var nodeId in _nodes.Keys)
            {
                if (HasCycleDFS(nodeId, visited, recursionStack))
                {
                    return true;
                }
            }
            return false;
        }

        private bool HasCycleDFS(string nodeId, HashSet<string> visited, HashSet<string> recursionStack)
        {
            if (recursionStack.Contains(nodeId))
                return true;

            if (visited.Contains(nodeId))
                return false;

            visited.Add(nodeId);
            recursionStack.Add(nodeId);

            if (_edges.ContainsKey(nodeId))
            {
                foreach (var neighbor in _edges[nodeId])
                {
                    if (HasCycleDFS(neighbor, visited, recursionStack))
                        return true;
                }
            }

            recursionStack.Remove(nodeId);
            return false;
        }

        public List<SubTask> GetTopologicalOrder()
        {
            var result = new List<SubTask>();
            var visited = new HashSet<string>();

            foreach (var nodeId in _nodes.Keys)
            {
                if (!visited.Contains(nodeId))
                {
                    TopologicalSortDFS(nodeId, visited, result);
                }
            }

            result.Reverse();
            return result;
        }

        private void TopologicalSortDFS(string nodeId, HashSet<string> visited, List<SubTask> result)
        {
            visited.Add(nodeId);

            if (_edges.ContainsKey(nodeId))
            {
                foreach (var neighbor in _edges[nodeId])
                {
                    if (!visited.Contains(neighbor))
                    {
                        TopologicalSortDFS(neighbor, visited, result);
                    }
                }
            }

            result.Add(_nodes[nodeId]);
        }
    }

    /// <summary>
    /// 任务依赖关系
    /// </summary>
    public class TaskDependency
    {
        public SubTask FromTask { get; set; } = new();
        public SubTask ToTask { get; set; } = new();
        public DependencyType DependencyType { get; set; }
        public string? Description { get; set; }
        public bool IsOptional { get; set; } = false;
    }

    // DependencyType 已在其他地方定义

    /// <summary>
    /// 任务执行计划
    /// </summary>
    public class TaskExecutionPlan
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public int TotalTasks { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public List<ExecutionPhase> ExecutionPhases { get; set; } = new();
        public Dictionary<string, object> ResourceAllocation { get; set; } = new();
        public List<string> RiskFactors { get; set; } = new();
        public string? ExecutionStrategy { get; set; }
        public Dictionary<string, string> Configuration { get; set; } = new();
    }

    /// <summary>
    /// 执行阶段
    /// </summary>
    public class ExecutionPhase
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public List<SubTask> Tasks { get; set; } = new();
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public PhaseStatus Status { get; set; } = PhaseStatus.Pending;
        public Dictionary<string, object> PhaseMetadata { get; set; } = new();
    }

    /// <summary>
    /// 阶段状态
    /// </summary>
    public enum PhaseStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 任务复杂度分析
    /// </summary>
    public class TaskComplexityAnalysis
    {
        public int TechnicalComplexity { get; set; }      // 技术复杂度 (1-10)
        public int CreativeRequirement { get; set; }     // 创意要求 (1-10)
        public int TimeRequirement { get; set; }         // 时间需求 (1-10)
        public int ResourceRequirement { get; set; }     // 资源需求 (1-10)
        public int CoordinationDifficulty { get; set; }  // 协调难度 (1-10)
        public int OverallComplexity { get; set; }       // 总体复杂度 (1-10)
        public int RecommendedDepth { get; set; }        // 推荐分解深度 (1-5)
        public List<string> KeyChallenges { get; set; } = new();
        public string RecommendedStrategy { get; set; } = string.Empty;
        public Dictionary<string, float> ComplexityFactors { get; set; } = new();
    }

    /// <summary>
    /// 任务执行结果
    /// </summary>
    public class TaskExecutionResult
    {
        public string TaskId { get; set; } = string.Empty;
        public TaskStatus Status { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? CompletedAt { get; set; }
        public TimeSpan ActualDuration { get; set; }
        public string? Output { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
        public List<string> GeneratedArtifacts { get; set; } = new();
        public float QualityScore { get; set; }
        public string? FeedbackNotes { get; set; }
    }

    /// <summary>
    /// 智能代理配置
    /// </summary>
    public class AgentConfiguration
    {
        public string AgentId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public List<TaskType> Capabilities { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public int MaxConcurrentTasks { get; set; } = 1;
        public TimeSpan MaxTaskDuration { get; set; } = TimeSpan.FromHours(1);
        public List<string> PreferredModels { get; set; } = new();
        public string? PersonalityProfile { get; set; }
        public Dictionary<string, float> SkillLevels { get; set; } = new();
    }

    /// <summary>
    /// 工作流执行监控
    /// </summary>
    public class WorkflowMonitor
    {
        public string WorkflowId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int TotalTasks { get; set; }
        public int CompletedTasks { get; set; }
        public int FailedTasks { get; set; }
        public float OverallProgress { get; set; }
        public List<TaskExecutionResult> TaskResults { get; set; } = new();
        public Dictionary<string, object> PerformanceMetrics { get; set; } = new();
        public List<string> Alerts { get; set; } = new();
        public string? CurrentPhase { get; set; }
    }

    // ==================== 多模态内容处理相关模型 ====================

    /// <summary>
    /// 图像分析选项
    /// </summary>
    public class ImageAnalysisOptions
    {
        public bool EnableAIAnalysis { get; set; } = true;
        public bool EnableObjectDetection { get; set; } = true;
        public bool EnableTextExtraction { get; set; } = true;
        public bool EnableEmotionAnalysis { get; set; } = false;
        public string AnalysisLanguage { get; set; } = "zh";
        public float ConfidenceThreshold { get; set; } = 0.5f;
    }

    /// <summary>
    /// 图像分析结果
    /// </summary>
    public class ImageAnalysisResult
    {
        public string ImagePath { get; set; } = string.Empty;
        public DateTime AnalyzedAt { get; set; }
        public ImageBasicInfo BasicInfo { get; set; } = new();
        public string AIAnalysis { get; set; } = string.Empty;
        public List<DetectedObject> DetectedObjects { get; set; } = new();
        public string ExtractedText { get; set; } = string.Empty;
        public EmotionAnalysisResult? EmotionAnalysis { get; set; }
    }

    /// <summary>
    /// 图像基础信息
    /// </summary>
    public class ImageBasicInfo
    {
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string Format { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? CameraModel { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 检测到的对象
    /// </summary>
    public class DetectedObject
    {
        public string Name { get; set; } = string.Empty;
        public float Confidence { get; set; }
        public string BoundingBox { get; set; } = string.Empty; // x,y,width,height
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 情感分析结果
    /// </summary>
    public class EmotionAnalysisResult
    {
        public string PrimaryEmotion { get; set; } = string.Empty;
        public float Confidence { get; set; }
        public Dictionary<string, float> EmotionScores { get; set; } = new();
    }

    /// <summary>
    /// 音频转录结果
    /// </summary>
    public class AudioTranscriptionResult
    {
        public string AudioPath { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public DateTime TranscribedAt { get; set; }
        public AudioInfo AudioInfo { get; set; } = new();
        public string TranscriptionText { get; set; } = string.Empty;
        public List<SpeakerSegment> SpeakerSegments { get; set; } = new();
        public EmotionAnalysisResult? EmotionAnalysis { get; set; }
        public List<string> Keywords { get; set; } = new();
        public float ConfidenceScore { get; set; }
    }

    /// <summary>
    /// 音频信息
    /// </summary>
    public class AudioInfo
    {
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string Format { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public int SampleRate { get; set; }
        public int Channels { get; set; }
        public int BitRate { get; set; }
    }

    /// <summary>
    /// 说话人片段
    /// </summary>
    public class SpeakerSegment
    {
        public string SpeakerId { get; set; } = string.Empty;
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string Text { get; set; } = string.Empty;
        public float Confidence { get; set; }
    }

    /// <summary>
    /// 视频分析选项
    /// </summary>
    public class VideoAnalysisOptions
    {
        public bool EnableKeyFrameExtraction { get; set; } = true;
        public bool EnableAudioTranscription { get; set; } = true;
        public bool EnableSceneDetection { get; set; } = true;
        public bool EnableObjectTracking { get; set; } = false;
        public TimeSpan KeyFrameInterval { get; set; } = TimeSpan.FromSeconds(10);
        public string OutputDirectory { get; set; } = "video_analysis";
    }

    /// <summary>
    /// 视频分析结果
    /// </summary>
    public class VideoAnalysisResult
    {
        public string VideoPath { get; set; } = string.Empty;
        public DateTime AnalyzedAt { get; set; }
        public VideoInfo VideoInfo { get; set; } = new();
        public List<KeyFrame> KeyFrames { get; set; } = new();
        public AudioTranscriptionResult? AudioTranscription { get; set; }
        public List<VideoScene> Scenes { get; set; } = new();
        public List<TrackedObject> TrackedObjects { get; set; } = new();
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// 视频信息
    /// </summary>
    public class VideoInfo
    {
        public string FileName { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string Format { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public float FrameRate { get; set; }
        public int BitRate { get; set; }
        public string? Codec { get; set; }
    }

    /// <summary>
    /// 关键帧
    /// </summary>
    public class KeyFrame
    {
        public TimeSpan Timestamp { get; set; }
        public string ImagePath { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public float ImportanceScore { get; set; }
    }

    /// <summary>
    /// 视频场景
    /// </summary>
    public class VideoScene
    {
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public float ConfidenceScore { get; set; }
    }

    /// <summary>
    /// 跟踪对象
    /// </summary>
    public class TrackedObject
    {
        public string ObjectId { get; set; } = string.Empty;
        public string ObjectType { get; set; } = string.Empty;
        public TimeSpan FirstAppearance { get; set; }
        public TimeSpan LastAppearance { get; set; }
        public string TrackingData { get; set; } = string.Empty; // JSON格式的跟踪数据
    }

    /// <summary>
    /// 图像生成选项
    /// </summary>
    public class ImageGenerationOptions
    {
        public string Style { get; set; } = "realistic";
        public int Width { get; set; } = 1024;
        public int Height { get; set; } = 1024;
        public string Quality { get; set; } = "high";
        public string? OutputDirectory { get; set; }
        public string Format { get; set; } = "png";
        public Dictionary<string, object> AdvancedOptions { get; set; } = new();
    }

    /// <summary>
    /// 多模态创作请求
    /// </summary>
    public class MultiModalCreationRequest
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Style { get; set; } = "professional";
        public int TargetLength { get; set; } = 1000;
        public bool IncludeText { get; set; } = true;
        public bool IncludeImages { get; set; } = false;
        public bool IncludeAudio { get; set; } = false;
        public bool IncludeVideo { get; set; } = false;
        public int ImageCount { get; set; } = 1;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 多模态创作结果
    /// </summary>
    public class MultiModalCreationResult
    {
        public string Title { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string TextContent { get; set; } = string.Empty;
        public List<string> GeneratedImages { get; set; } = new();
        public List<string> GeneratedAudio { get; set; } = new();
        public string? GeneratedVideo { get; set; }
        public string IntegratedContent { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 内容摘要结果
    /// </summary>
    public class ContentSummaryResult
    {
        public List<string> ContentPaths { get; set; } = new();
        public DateTime SummarizedAt { get; set; }
        public string Summary { get; set; } = string.Empty;
        public List<string> KeyThemes { get; set; } = new();
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, int> ContentTypeCount { get; set; } = new();
        public float OverallSentiment { get; set; }
    }

    // ==================== 智能推荐系统相关模型 ====================

    /// <summary>
    /// 推荐上下文
    /// </summary>
    public class RecommendationContext
    {
        public string UserId { get; set; } = string.Empty;
        public string? ProjectId { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public string? CurrentContent { get; set; }
        public string? ProjectContext { get; set; }
        public int MaxRecommendations { get; set; } = 10;
        public Dictionary<string, object> Filters { get; set; } = new();
        public List<string> ExcludeIds { get; set; } = new();
    }

    /// <summary>
    /// 内容推荐
    /// </summary>
    public class ContentRecommendation
    {
        public string ContentId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public RecommendationType RecommendationType { get; set; }
        public float Score { get; set; }
        public string Reason { get; set; } = string.Empty;
        public DateTime RecommendedAt { get; set; } = DateTime.Now;
        public Dictionary<string, object> Metadata { get; set; } = new();
        public List<string> Tags { get; set; } = new();
    }

    /// <summary>
    /// 推荐类型
    /// </summary>
    public enum RecommendationType
    {
        ContentBased,       // 基于内容
        Collaborative,      // 协同过滤
        KnowledgeBased,     // 基于知识
        AIGenerated,        // AI生成
        Trending,           // 热门推荐
        Personalized        // 个性化
    }

    /// <summary>
    /// 模板推荐
    /// </summary>
    public class TemplateRecommendation
    {
        public string TemplateId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public float Score { get; set; }
        public int UsageCount { get; set; }
        public DateTime LastUsed { get; set; }
        public List<string> SuitableFor { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
    }

    /// <summary>
    /// 风格推荐
    /// </summary>
    public class StyleRecommendation
    {
        public string StyleId { get; set; } = string.Empty;
        public string StyleName { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public float Score { get; set; }
        public string Reason { get; set; } = string.Empty;
        public List<string> Characteristics { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string> Examples { get; set; } = new();
    }

    /// <summary>
    /// 资源推荐
    /// </summary>
    public class ResourceRecommendation
    {
        public string ResourceId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string? Url { get; set; }
        public string? FilePath { get; set; }
        public float Score { get; set; }
        public string Reason { get; set; } = string.Empty;
        public ResourceAccessLevel AccessLevel { get; set; }
        public DateTime LastUpdated { get; set; }
        public List<string> Keywords { get; set; } = new();
    }

    /// <summary>
    /// 资源访问级别
    /// </summary>
    public enum ResourceAccessLevel
    {
        Public,
        Internal,
        Private,
        Restricted
    }

    /// <summary>
    /// 协作者推荐
    /// </summary>
    public class CollaboratorRecommendation
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Email { get; set; }
        public float Score { get; set; }
        public string Reason { get; set; } = string.Empty;
        public List<string> Skills { get; set; } = new();
        public List<string> Expertise { get; set; } = new();
        public int CollaborationHistory { get; set; }
        public float AvailabilityScore { get; set; }
        public Dictionary<string, object> Profile { get; set; } = new();
    }

    /// <summary>
    /// 用户交互
    /// </summary>
    public class UserInteraction
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; } = string.Empty;
        public string ActionType { get; set; } = string.Empty;
        public string? TargetId { get; set; }
        public string? ContentType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public TimeSpan Duration { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
        public float? Rating { get; set; }
        public string? Feedback { get; set; }
    }

    /// <summary>
    /// 用户偏好档案
    /// </summary>
    public class UserPreferenceProfile
    {
        public string UserId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public Dictionary<string, object> Preferences { get; set; } = new();
        public Dictionary<string, float> InterestScores { get; set; } = new();
        public List<string> FavoriteCategories { get; set; } = new();
        public List<string> DislikedCategories { get; set; } = new();
        public Dictionary<string, int> ActivityCounts { get; set; } = new();
        public UserPersonality? Personality { get; set; }
    }

    /// <summary>
    /// 用户个性特征
    /// </summary>
    public class UserPersonality
    {
        public float Openness { get; set; }         // 开放性
        public float Conscientiousness { get; set; } // 尽责性
        public float Extraversion { get; set; }     // 外向性
        public float Agreeableness { get; set; }    // 宜人性
        public float Neuroticism { get; set; }      // 神经质
        public string PersonalityType { get; set; } = string.Empty;
        public List<string> Traits { get; set; } = new();
    }

    /// <summary>
    /// 热门内容
    /// </summary>
    public class TrendingContent
    {
        public string ContentId { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int ViewCount { get; set; }
        public int ShareCount { get; set; }
        public int LikeCount { get; set; }
        public float TrendingScore { get; set; }
        public DateTimeRange TrendingPeriod { get; set; } = new();
        public List<string> TrendingReasons { get; set; } = new();
    }

    // DateTimeRange 已在 ProjectToolModels.cs 中定义

    /// <summary>
    /// 相似用户
    /// </summary>
    public class SimilarUser
    {
        public string UserId { get; set; } = string.Empty;
        public float SimilarityScore { get; set; }
        public List<string> CommonInterests { get; set; } = new();
        public List<string> SimilarBehaviors { get; set; } = new();
        public Dictionary<string, float> FeatureSimilarity { get; set; } = new();
    }

    /// <summary>
    /// 内容项
    /// </summary>
    public class ContentItem
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public float Score { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 内容风格
    /// </summary>
    public class ContentStyle
    {
        public string StyleName { get; set; } = string.Empty;
        public float Confidence { get; set; }
        public Dictionary<string, float> StyleFeatures { get; set; } = new();
        public List<string> Characteristics { get; set; } = new();
        public string? Description { get; set; }
    }

    /// <summary>
    /// 项目上下文
    /// </summary>
    public class ProjectContext
    {
        public string ProjectId { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public string ProjectType { get; set; } = string.Empty;
        public string ProjectPath { get; set; } = string.Empty;
        public List<string> Keywords { get; set; } = new();
        public List<string> Categories { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
        public List<string> RelatedProjects { get; set; } = new();
        public Project? CurrentProject { get; set; }
    }

    /// <summary>
    /// 项目需求
    /// </summary>
    public class ProjectRequirements
    {
        public string ProjectId { get; set; } = string.Empty;
        public List<string> RequiredSkills { get; set; } = new();
        public List<string> PreferredExpertise { get; set; } = new();
        public string ProjectPhase { get; set; } = string.Empty;
        public TimeSpan EstimatedDuration { get; set; }
        public string Complexity { get; set; } = string.Empty;
        public Dictionary<string, object> SpecialRequirements { get; set; } = new();
    }
}

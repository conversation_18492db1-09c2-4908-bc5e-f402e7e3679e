# Agent和MCP功能说明

## 概述

本文档介绍了文档创建系统中新增的AI Agent功能和Model Context Protocol (MCP) 支持。这些功能大大增强了系统的智能化程度和扩展性。

## 主要功能

### 1. AI Agent核心功能

#### 1.1 增强的记忆系统
- **持久化记忆**: 支持长期记忆存储和检索
- **记忆关联**: 自动建立记忆之间的关联关系
- **记忆巩固**: 定期巩固重要记忆，遗忘过期信息
- **多类型记忆**: 支持交互记忆、技能记忆、上下文记忆

#### 1.2 自主学习能力
- **经验学习**: 从成功和失败案例中学习
- **技能提升**: 动态调整技能熟练度
- **模式识别**: 自动发现行为和决策模式
- **学习建议**: 生成个性化的学习建议

#### 1.3 决策和推理引擎
- **多种推理类型**: 支持演绎、归纳、溯因、类比、因果、概率推理
- **决策制定**: 基于上下文和经验制定最优决策
- **推理链验证**: 验证推理过程的逻辑一致性
- **决策学习**: 从决策结果中学习和改进

#### 1.4 工具调用系统
- **多格式支持**: 支持多种工具调用格式
- **性能监控**: 实时监控工具执行性能
- **并发执行**: 支持多个工具并发执行
- **错误处理**: 完善的错误处理和重试机制

### 2. MCP (Model Context Protocol) 支持

#### 2.1 MCP协议实现
- **标准兼容**: 完全兼容MCP 1.0协议规范
- **双向通信**: 支持客户端和服务器双向通信
- **消息处理**: 完整的消息序列化和反序列化
- **错误处理**: 标准的错误码和错误处理

#### 2.2 MCP服务管理
- **服务器配置**: 支持多个MCP服务器配置和管理
- **连接管理**: 自动连接管理和重连机制
- **状态监控**: 实时监控服务器连接状态
- **工具发现**: 自动发现和注册MCP工具

#### 2.3 内置MCP工具

##### 文件系统工具
- `read_file`: 读取文件内容
- `write_file`: 写入文件内容
- `list_directory`: 列出目录内容
- `create_directory`: 创建目录
- `delete_file`: 删除文件
- `file_info`: 获取文件信息
- `search_files`: 搜索文件

##### Web搜索工具
- `search_web`: 网页搜索
- `fetch_webpage`: 获取网页内容
- `search_news`: 新闻搜索
- `search_images`: 图片搜索

##### 数据库工具
- `execute_query`: 执行SQL查询
- `execute_command`: 执行SQL命令
- `list_tables`: 列出数据库表
- `describe_table`: 获取表结构
- `create_table`: 创建新表
- `backup_database`: 备份数据库

## 使用指南

### 1. Agent配置

通过主菜单 "智能助手" -> "Agent配置" 打开配置界面：

#### 基本配置
- 设置Agent名称和描述
- 选择Agent类型和AI模型
- 调整置信度阈值和温度参数

#### 工具配置
- 启用/禁用特定工具
- 设置工具执行参数
- 查看工具使用统计

#### 记忆配置
- 配置记忆保留时间
- 设置最大记忆条目数
- 启用自动记忆巩固

#### 性能监控
- 查看工具执行统计
- 监控记忆使用情况
- 分析Agent性能指标

### 2. MCP服务器配置

通过Agent配置界面的"MCP配置"按钮打开MCP配置：

#### 添加服务器
1. 点击"添加服务器"按钮
2. 选择预设模板或自定义配置
3. 填写服务器信息和启动参数
4. 配置环境变量（如API密钥）
5. 保存配置

#### 管理服务器
- 启用/禁用服务器
- 连接/断开服务器
- 测试连接状态
- 查看服务器工具列表

### 3. 工具使用

#### 在Agent对话中使用
Agent会自动选择合适的工具来完成任务，支持以下格式：

```
[TOOL:read_file]path=/path/to/file.txt[/TOOL]
```

```json
{
  "tool": "search_web",
  "parameters": {
    "query": "AI技术发展趋势",
    "max_results": 10
  }
}
```

#### 直接调用
也可以通过工具服务直接调用：

```csharp
var result = await toolsService.ExecuteToolAsync("read_file", new Dictionary<string, object>
{
    ["path"] = "/path/to/file.txt"
});
```

## 技术架构

### 1. 核心组件

```
DocumentCreationSystem
├── Services/
│   ├── AgentDecisionEngine.cs          # 决策引擎
│   ├── AgentReasoningEngine.cs         # 推理引擎
│   ├── AgentLearningEngine.cs          # 学习引擎
│   ├── EnhancedAgentMemoryService.cs   # 记忆服务
│   ├── EnhancedToolCallParser.cs       # 工具调用解析
│   ├── ToolExecutionMonitor.cs         # 工具执行监控
│   └── MCP/
│       ├── MCPClient.cs                # MCP客户端
│       ├── MCPServiceManager.cs        # MCP服务管理
│       ├── MCPToolIntegrationService.cs # MCP工具集成
│       ├── MCPToolRegistry.cs          # MCP工具注册表
│       └── Tools/
│           ├── FileSystemMCPTool.cs    # 文件系统工具
│           ├── WebSearchMCPTool.cs     # Web搜索工具
│           └── DatabaseMCPTool.cs      # 数据库工具
├── Models/
│   ├── AgentDecisionModels.cs          # 决策模型
│   ├── Memory/
│   │   └── AgentMemoryModels.cs        # 记忆模型
│   └── MCP/
│       └── MCPModels.cs                # MCP协议模型
└── Views/
    ├── AgentConfigWindow.xaml          # Agent配置界面
    ├── MCPConfigWindow.xaml            # MCP配置界面
    └── MCPServerAddDialog.xaml         # MCP服务器添加对话框
```

### 2. 数据流

```
用户输入 -> Agent决策引擎 -> 推理引擎 -> 工具选择 -> MCP工具执行 -> 结果处理 -> 学习更新 -> 记忆存储
```

## 配置文件

### Agent配置文件
位置: `%AppData%/DocumentCreationSystem/agent-config.json`

```json
{
  "Name": "DocumentCreationAgent",
  "Description": "文档创建专用AI助手",
  "Type": "文档创建专家",
  "ConfidenceThreshold": 0.7,
  "ModelProvider": "OpenAI",
  "ModelName": "gpt-4",
  "Temperature": 0.7,
  "EnableMemory": true,
  "MemoryRetentionDays": 30,
  "MaxMemoryItems": 10000,
  "AutoConsolidate": true,
  "EnableLearning": true,
  "LearningRate": 0.1,
  "LearnFromFailures": true,
  "MaxConcurrentTools": 3,
  "ToolTimeoutSeconds": 30
}
```

### MCP服务器配置文件
位置: `%AppData%/DocumentCreationSystem/mcp-servers.json`

```json
[
  {
    "Name": "filesystem",
    "Description": "文件系统操作工具",
    "ServerPath": "npx",
    "Args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"],
    "Enabled": true,
    "Environment": {},
    "TimeoutMs": 30000,
    "AutoReconnect": true,
    "MaxReconnectAttempts": 3,
    "ReconnectIntervalMs": 5000
  }
]
```

## 扩展开发

### 1. 添加新的MCP工具

```csharp
public class CustomMCPTool : IMCPTool
{
    public string Name => "custom_tool";
    public string Description => "自定义工具描述";
    public string Version => "1.0.0";

    public async Task<List<MCPTool>> GetAvailableToolsAsync()
    {
        // 返回工具定义
    }

    public async Task<MCPToolCallResult> ExecuteToolAsync(string toolName, Dictionary<string, object> arguments)
    {
        // 执行工具逻辑
    }
}
```

### 2. 自定义推理类型

```csharp
public async Task<ReasoningStep> CustomReasoningAsync(string agentId, CustomReasoningInput input)
{
    var step = new ReasoningStep
    {
        Type = ReasoningType.Custom,
        Description = "自定义推理",
        // 设置推理逻辑
    };
    return step;
}
```

## 故障排除

### 常见问题

1. **MCP服务器连接失败**
   - 检查服务器路径和参数
   - 确认环境变量配置
   - 查看日志文件获取详细错误信息

2. **工具执行超时**
   - 增加工具超时时间设置
   - 检查网络连接状态
   - 优化工具执行逻辑

3. **记忆功能异常**
   - 检查数据库连接
   - 清理过期记忆数据
   - 重置记忆配置

### 日志位置
- 应用程序日志: `%AppData%/DocumentCreationSystem/logs/`
- MCP通信日志: `%AppData%/DocumentCreationSystem/mcp-logs/`

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础Agent功能
- 添加MCP协议支持
- 集成常用MCP工具
- 完善配置和管理界面

---

更多信息请参考项目文档或联系开发团队。

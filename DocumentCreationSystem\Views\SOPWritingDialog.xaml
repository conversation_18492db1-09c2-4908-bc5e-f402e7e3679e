<Window x:Class="DocumentCreationSystem.Views.SOPWritingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="一键写SOP（标准作业程序）" 
        Height="800" Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16" CornerRadius="4" Margin="0,0,0,20">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="FileDocumentEdit" Width="32" Height="32" VerticalAlignment="Center" Margin="0,0,16,0"/>
                <StackPanel>
                    <TextBlock Text="一键写SOP（标准作业程序）" FontSize="20" FontWeight="Bold"/>
                    <TextBlock Text="基于素材文件智能生成专业的标准作业程序文档" FontSize="14" Opacity="0.8"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 素材文件选择 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="素材文件选择" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="MaterialFilesTextBox" 
                                   Grid.Column="0"
                                   materialDesign:HintAssist.Hint="选择包含业务流程、操作步骤等内容的素材文件"
                                   IsReadOnly="True"
                                   TextWrapping="Wrap"
                                   MinHeight="60"/>
                            
                            <Button x:Name="SelectMaterialFilesButton" 
                                  Grid.Column="1"
                                  Style="{StaticResource MaterialDesignRaisedButton}"
                                  Margin="8,0,0,0"
                                  Click="SelectMaterialFiles_Click"
                                  ToolTip="选择素材文件（支持.txt、.docx、.md格式）">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FolderOpen" Margin="0,0,8,0"/>
                                    <TextBlock Text="选择文件"/>
                                </StackPanel>
                            </Button>
                        </Grid>
                        
                        <TextBlock Text="支持格式：.txt、.docx、.md | 可选择多个文件" 
                                 FontSize="12" Opacity="0.7" Margin="0,8,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- SOP基本信息 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="SOP基本信息" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- SOP标题 -->
                            <TextBox x:Name="SOPTitleTextBox" 
                                   Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2"
                                   materialDesign:HintAssist.Hint="SOP标题（留空将由AI自动生成）"
                                   Margin="0,0,0,16"/>

                            <!-- SOP类型 -->
                            <ComboBox x:Name="SOPTypeComboBox" 
                                    Grid.Row="1" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="SOP类型"
                                    Margin="0,0,8,16">
                                <ComboBoxItem Content="操作规程SOP" IsSelected="True"/>
                                <ComboBoxItem Content="管理制度SOP"/>
                                <ComboBoxItem Content="质量控制SOP"/>
                                <ComboBoxItem Content="安全操作SOP"/>
                                <ComboBoxItem Content="维护保养SOP"/>
                                <ComboBoxItem Content="应急处理SOP"/>
                                <ComboBoxItem Content="培训管理SOP"/>
                                <ComboBoxItem Content="文件管理SOP"/>
                            </ComboBox>

                            <!-- 业务领域 -->
                            <ComboBox x:Name="BusinessDomainComboBox" 
                                    Grid.Row="1" Grid.Column="1"
                                    materialDesign:HintAssist.Hint="业务领域"
                                    Margin="8,0,0,16"
                                    IsEditable="True">
                                <ComboBoxItem Content="制造业"/>
                                <ComboBoxItem Content="服务业"/>
                                <ComboBoxItem Content="信息技术"/>
                                <ComboBoxItem Content="医疗健康"/>
                                <ComboBoxItem Content="金融保险"/>
                                <ComboBoxItem Content="教育培训"/>
                                <ComboBoxItem Content="物流运输"/>
                                <ComboBoxItem Content="建筑工程"/>
                                <ComboBoxItem Content="食品安全"/>
                                <ComboBoxItem Content="环境保护"/>
                            </ComboBox>

                            <!-- 目标字数 -->
                            <TextBox x:Name="TargetWordCountTextBox" 
                                   Grid.Row="2" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="目标字数"
                                   Text="5000"
                                   Margin="0,0,8,16"/>

                            <!-- 部门信息 -->
                            <TextBox x:Name="DepartmentTextBox" 
                                   Grid.Row="2" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="所属部门（可选）"
                                   Margin="8,0,0,16"/>

                            <!-- 制定人 -->
                            <TextBox x:Name="AuthorTextBox" 
                                   Grid.Row="3" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="制定人（可选）"
                                   Margin="0,0,8,0"/>

                            <!-- 审核人 -->
                            <TextBox x:Name="ReviewerTextBox" 
                                   Grid.Row="3" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="审核人（可选）"
                                   Margin="8,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 用户要求 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="📝 用户要求" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>

                        <TextBlock Text="附加创作要求（可选）" FontWeight="Medium" Margin="0,0,0,8"/>
                        <TextBox x:Name="UserRequirementsTextBox"
                                materialDesign:HintAssist.Hint="请输入您的特殊要求，如：重点关注安全事项、包含特定检查点、强调某些操作步骤、遵循特定标准等..."
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="80"
                                MaxHeight="120"
                                VerticalScrollBarVisibility="Auto"/>
                        <TextBlock Text="示例：请重点强调安全操作规程，每个步骤都要包含质量检查点，并遵循ISO 9001标准"
                                  FontSize="11" Opacity="0.7" Margin="0,8,0,0" FontStyle="Italic"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 写作参数配置 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="写作参数配置" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 详细程度 -->
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBlock Text="详细程度" FontWeight="Medium" Margin="0,0,0,8"/>
                                <Slider x:Name="DetailLevelSlider" 
                                      Minimum="0.1" Maximum="1.0" Value="0.7" 
                                      TickFrequency="0.1" IsSnapToTickEnabled="True"
                                      materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging="True"/>
                                <TextBlock Text="{Binding ElementName=DetailLevelSlider, Path=Value, StringFormat=F1}" 
                                         HorizontalAlignment="Center" FontSize="12" Opacity="0.7"/>
                                <TextBlock Text="0.1=简要 | 0.5=适中 | 1.0=详细" 
                                         FontSize="10" Opacity="0.6" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- 规范化程度 -->
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <TextBlock Text="规范化程度" FontWeight="Medium" Margin="0,0,0,8"/>
                                <Slider x:Name="StandardizationLevelSlider" 
                                      Minimum="0.1" Maximum="1.0" Value="0.8" 
                                      TickFrequency="0.1" IsSnapToTickEnabled="True"
                                      materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging="True"/>
                                <TextBlock Text="{Binding ElementName=StandardizationLevelSlider, Path=Value, StringFormat=F1}" 
                                         HorizontalAlignment="Center" FontSize="12" Opacity="0.7"/>
                                <TextBlock Text="0.1=灵活 | 0.5=平衡 | 1.0=严格" 
                                         FontSize="10" Opacity="0.6" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- SOP结构设置 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="SOP结构设置" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <CheckBox x:Name="IncludePurposeAndScopeCheckBox" Content="目的和范围" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeResponsibilitiesCheckBox" Content="职责分工" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeProceduresCheckBox" Content="操作流程" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeQualityControlCheckBox" Content="质量控制" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeRiskControlCheckBox" Content="风险控制" IsChecked="True" Margin="0,4"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <CheckBox x:Name="IncludeRecordManagementCheckBox" Content="记录管理" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeTrainingRequirementsCheckBox" Content="培训要求" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeRelatedDocumentsCheckBox" Content="相关文件" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeAppendicesCheckBox" Content="附录" IsChecked="True" Margin="0,4"/>
                                <CheckBox x:Name="IncludeRevisionHistoryCheckBox" Content="修订历史" IsChecked="True" Margin="0,4"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 输出设置 -->
                <materialDesign:Card Margin="0,0,0,16" Padding="16">
                    <StackPanel>
                        <TextBlock Text="输出设置" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 输出格式 -->
                            <ComboBox x:Name="OutputFormatComboBox" 
                                    Grid.Column="0"
                                    materialDesign:HintAssist.Hint="输出格式"
                                    Margin="0,0,8,0">
                                <ComboBoxItem Content="Word文档" IsSelected="True"/>
                                <ComboBoxItem Content="文本文件"/>
                                <ComboBoxItem Content="Markdown"/>
                            </ComboBox>

                            <!-- 保存选项 -->
                            <CheckBox x:Name="SaveToProjectCheckBox" 
                                    Grid.Column="1"
                                    Content="保存到当前项目文件夹" 
                                    IsChecked="True" 
                                    VerticalAlignment="Center"
                                    Margin="8,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部操作区域 -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 状态显示 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="StatusIcon" Kind="CheckCircle" 
                                       Width="20" Height="20" 
                                       Foreground="Green" 
                                       VerticalAlignment="Center" 
                                       Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusText" Text="就绪" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 操作按钮 -->
            <Button x:Name="StartButton" 
                  Grid.Column="1"
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Background="{DynamicResource PrimaryHueMidBrush}"
                  Margin="0,0,16,0"
                  Padding="24,8"
                  Click="StartGeneration_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Play" Margin="0,0,8,0"/>
                    <TextBlock Text="开始生成"/>
                </StackPanel>
            </Button>

            <Button x:Name="CloseButton" 
                  Grid.Column="2"
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Padding="24,8"
                  Click="Close_Click">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Close" Margin="0,0,8,0"/>
                    <TextBlock Text="关闭"/>
                </StackPanel>
            </Button>
        </Grid>
    </Grid>
</Window>

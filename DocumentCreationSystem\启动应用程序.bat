@echo off
chcp 65001 >nul
echo ===================================
echo 文档管理及AI创作系统 - 正式版本
echo ===================================
echo.

echo 检查.NET SDK...
dotnet --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 未找到.NET SDK，请先安装.NET 8.0 SDK
    echo 下载地址: https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo ✓ .NET SDK已安装
echo.

echo 启动应用程序...
dotnet run --project .
if %ERRORLEVEL% neq 0 (
    echo 错误: 应用程序启动失败
    pause
    exit /b 1
)

echo.
echo 应用程序已关闭
pause

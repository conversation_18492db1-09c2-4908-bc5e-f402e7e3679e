<Window x:Class="DocumentCreationSystem.Views.StepByStepWritingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="分步执行写书"
        Height="700"
        Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" Padding="16" Margin="0,0,0,16">
            <DockPanel>
                <materialDesign:PackIcon Kind="BookOpenPageVariant" DockPanel.Dock="Left"
                                       VerticalAlignment="Center" Margin="0,0,12,0" Width="32" Height="32"/>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="分步执行写书" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock x:Name="ProjectStatusText" 
                             Text="请先设置书籍基本信息" 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <!-- 状态摘要 -->
                <TextBlock x:Name="StateSummaryText" 
                         DockPanel.Dock="Right"
                         VerticalAlignment="Center"
                         Style="{StaticResource MaterialDesignBody2TextBlock}"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </DockPanel>
        </materialDesign:Card>

        <!-- 书籍基本信息和创作设置 -->
        <materialDesign:Card Grid.Row="1" Padding="12" Margin="0,0,0,12">
            <Expander Header="书籍基本信息和创作设置" IsExpanded="True">
                <StackPanel Margin="0,12,0,0">
                    <!-- 书籍基本信息 -->
                    <TextBlock Text="书籍基本信息"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,0,0,8"
                             FontSize="13"/>

                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,6,0">
                            <TextBox x:Name="BookTitleTextBox"
                                   materialDesign:HintAssist.Hint="书籍名称"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"/>

                            <TextBox x:Name="AuthorNameTextBox"
                                   materialDesign:HintAssist.Hint="作者名称"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="6,0,0,0">
                            <ComboBox x:Name="GenreComboBox"
                                    materialDesign:HintAssist.Hint="小说类型"
                                    SelectedIndex="0"
                                    FontSize="12"
                                    Margin="0,0,0,12"
                                    ToolTip="选择小说的类型，这会影响AI的创作风格、情节设计和语言表达">
                                <ComboBoxItem Content="都市修仙"/>
                                <ComboBoxItem Content="玄幻奇幻"/>
                                <ComboBoxItem Content="科幻未来"/>
                                <ComboBoxItem Content="历史军事"/>
                                <ComboBoxItem Content="悬疑推理"/>
                                <ComboBoxItem Content="言情浪漫"/>
                                <ComboBoxItem Content="其他"/>
                            </ComboBox>

                            <TextBox x:Name="TargetWordCountTextBox"
                                   materialDesign:HintAssist.Hint="目标总字数"
                                   Text="650000"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="设置整部小说的目标总字数"/>
                        </StackPanel>
                    </Grid>

                    <TextBox x:Name="CreativeDirectionTextBox"
                           materialDesign:HintAssist.Hint="创作方向和主题描述"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           AcceptsReturn="True"
                           TextWrapping="Wrap"
                           MinHeight="60"
                           MaxHeight="100"
                           FontSize="12"
                           VerticalScrollBarVisibility="Auto"
                           Margin="0,0,0,16"
                           ToolTip="详细描述您想要创作的小说主题、故事背景、主要人物和核心冲突"/>

                    <!-- 创作设置 -->
                    <TextBlock Text="创作设置"
                             Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                             Margin="0,8,0,8"
                             FontSize="13"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,6,0">
                            <TextBox x:Name="VolumeCountTextBox"
                                   materialDesign:HintAssist.Hint="分卷数量"
                                   Text="10"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="将小说分成若干卷，每卷包含若干章节"/>

                            <TextBox x:Name="ChapterCountTextBox"
                                   materialDesign:HintAssist.Hint="总章节数"
                                   Text="100"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="设置小说的总章节数量"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="6,0,6,0">
                            <TextBox x:Name="WordsPerChapterTextBox"
                                   materialDesign:HintAssist.Hint="每章字数"
                                   Text="6500"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="设置每章的目标字数"/>

                            <TextBox x:Name="SegmentWordsTextBox"
                                   materialDesign:HintAssist.Hint="分段字数"
                                   Text="1000"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="设置每次AI创作的分段字数"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Margin="6,0,0,0">
                            <TextBox x:Name="ReviewChaptersTextBox"
                                   materialDesign:HintAssist.Hint="回顾章节数"
                                   Text="3"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   FontSize="12"
                                   Margin="0,0,0,12"
                                   ToolTip="设置AI创作时回顾前面多少章的内容"/>

                            <ComboBox x:Name="WritingStyleComboBox"
                                    materialDesign:HintAssist.Hint="写作风格"
                                    SelectedIndex="0"
                                    FontSize="12"
                                    Margin="0,0,0,12"
                                    ToolTip="选择AI的写作风格">
                                <ComboBoxItem Content="流畅自然"/>
                                <ComboBoxItem Content="细腻描写"/>
                                <ComboBoxItem Content="紧凑节奏"/>
                                <ComboBoxItem Content="幽默风趣"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>

                    <!-- 选项设置 -->
                    <StackPanel Orientation="Horizontal" Margin="0,8,0,12">
                        <CheckBox x:Name="AutoSaveCheckBox"
                                Content="自动保存章节"
                                IsChecked="True"
                                FontSize="11"
                                Margin="0,0,16,0"
                                ToolTip="开启后，每完成一章的创作会自动保存到项目文件夹中"/>

                        <CheckBox x:Name="GenerateOutlineFirstCheckBox"
                                Content="先生成大纲"
                                IsChecked="True"
                                FontSize="11"
                                Margin="0,0,16,0"
                                ToolTip="开启后，系统会先根据您的创作方向生成详细大纲"/>

                        <CheckBox x:Name="UseReferenceContentCheckBox"
                                Content="参考编辑器内容"
                                IsChecked="True"
                                FontSize="11"
                                ToolTip="开启后，AI会参考文档编辑器中的现有内容"/>
                    </StackPanel>

                    <!-- 卷数验证提示 -->
                    <TextBlock Text="请输入1-50之间的数字"
                              Foreground="Red"
                              FontSize="10"
                              x:Name="VolumeCountValidationText"
                              Visibility="Collapsed"
                              Margin="0,0,0,8"/>

                    <!-- 保存和加载按钮 -->
                    <StackPanel Orientation="Horizontal"
                              HorizontalAlignment="Right"
                              Margin="0,8,0,0">
                        <Button x:Name="LoadBasicInfoButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Click="LoadBasicInfo_Click"
                              FontSize="11"
                              Height="32"
                              Margin="0,0,8,0"
                              ToolTip="加载之前保存的书籍基本信息设置">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderOpen" Width="14" Height="14" Margin="0,0,6,0"/>
                                <TextBlock Text="加载设置"/>
                            </StackPanel>
                        </Button>
                        <Button x:Name="SaveBasicInfoButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Click="SaveBasicInfo_Click"
                              FontSize="11"
                              Height="32"
                              ToolTip="保存当前书籍基本信息设置">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave" Width="14" Height="14" Margin="0,0,6,0"/>
                                <TextBlock Text="保存设置"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </Expander>
        </materialDesign:Card>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧步骤控制面板 -->
            <materialDesign:Card Grid.Column="0" Padding="12" Margin="0,0,6,0">
                <StackPanel>
                    <TextBlock Text="执行步骤"
                             Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                             FontSize="13"
                             Margin="0,0,0,12"/>

                    <!-- 添加滚动视图支持 -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                MaxHeight="600"
                                Padding="0,0,6,0">
                        <StackPanel>

                            <!-- 步骤1：全书大纲 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step1Icon" Kind="Circle" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="1. 生成全书大纲"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <Button x:Name="GenerateOverallOutlineButton"
                                          Content="生成全书大纲"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="10"
                                          Height="28"
                                          Margin="0,6,0,0"
                                          Click="GenerateOverallOutline_Click"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 步骤2：世界设定 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step2Icon" Kind="Earth" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="2. 生成世界设定"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <Button x:Name="GenerateWorldSettingButton"
                                          Content="生成世界设定"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="10"
                                          Height="28"
                                          Margin="0,6,0,0"
                                          IsEnabled="False"
                                          Click="GenerateWorldSetting_Click"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 步骤3：卷宗大纲 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step3Icon" Kind="Circle" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="3. 生成卷宗大纲"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <Button x:Name="GenerateVolumeOutlinesButton"
                                          Content="生成卷宗大纲"
                                          Style="{StaticResource MaterialDesignFlatButton}"
                                          HorizontalAlignment="Stretch"
                                          FontSize="10"
                                          Height="28"
                                          Margin="0,6,0,0"
                                          IsEnabled="False"
                                          Click="GenerateVolumeOutlines_Click"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 步骤4：逐卷生成章节细纲和时间线 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step4Icon" Kind="FileDocument" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="4. 逐卷生成章节细纲和时间线"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <StackPanel Margin="0,6,0,0">
                                        <ComboBox x:Name="VolumeSelectionComboBox"
                                                materialDesign:HintAssist.Hint="选择卷宗"
                                                Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                                FontSize="10"
                                                Height="32"
                                                Margin="0,0,0,6"
                                                IsEnabled="False"/>
                                        <Button x:Name="GenerateChapterOutlinesButton"
                                              Content="生成选定卷的章节细纲和时间线"
                                              Style="{StaticResource MaterialDesignFlatButton}"
                                              HorizontalAlignment="Stretch"
                                              FontSize="10"
                                              Height="28"
                                              IsEnabled="False"
                                              Click="GenerateChapterOutlines_Click"/>
                                    </StackPanel>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 步骤5：生成章节正文并更新时间线 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step5Icon" Kind="FileText" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="5. 生成章节正文并更新时间线"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <StackPanel Margin="0,6,0,0">
                                        <TextBox x:Name="ChapterNumberTextBox"
                                               materialDesign:HintAssist.Hint="章节号"
                                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                               FontSize="10"
                                               Height="32"
                                               Margin="0,0,0,6"
                                               IsEnabled="False"/>
                                        <Button x:Name="GenerateChapterContentButton"
                                              Content="生成指定章节正文"
                                              Style="{StaticResource MaterialDesignFlatButton}"
                                              HorizontalAlignment="Stretch"
                                              FontSize="10"
                                              Height="28"
                                              IsEnabled="False"
                                              Click="GenerateChapterContent_Click"/>
                                        <Button x:Name="BatchGenerateChaptersButton"
                                              Content="批量生成章节正文"
                                              Style="{StaticResource MaterialDesignOutlinedButton}"
                                              HorizontalAlignment="Stretch"
                                              FontSize="10"
                                              Height="28"
                                              Margin="0,3,0,0"
                                              IsEnabled="False"
                                              Click="BatchGenerateChapters_Click"/>
                                        <Button x:Name="SequentialGenerateChapterButton"
                                              Content="逐章生成（细纲→正文→时间线）"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              HorizontalAlignment="Stretch"
                                              FontSize="10"
                                              Height="28"
                                              Margin="0,3,0,0"
                                              IsEnabled="False"
                                              Click="SequentialGenerateChapter_Click"
                                              ToolTip="根据分卷大纲逐章生成：先生成章节细纲，再生成正文，最后更新时间线。每章都会参考前一章的结尾内容，确保剧情连贯。"/>
                                    </StackPanel>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 新增：逐章生成控制 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon x:Name="Step6Icon" Kind="PlayCircle" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="6. 逐章连续生成模式"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <StackPanel Margin="0,6,0,0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBox x:Name="StartChapterTextBox"
                                                   Grid.Column="0"
                                                   materialDesign:HintAssist.Hint="起始章节"
                                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                   FontSize="10"
                                                   Height="32"
                                                   Margin="0,0,3,6"
                                                   IsEnabled="False"
                                                   Text="1"/>
                                            <TextBox x:Name="EndChapterTextBox"
                                                   Grid.Column="1"
                                                   materialDesign:HintAssist.Hint="结束章节"
                                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                   FontSize="10"
                                                   Height="32"
                                                   Margin="3,0,0,6"
                                                   IsEnabled="False"
                                                   Text="10"/>
                                        </Grid>
                                        <Button x:Name="StartSequentialGenerationButton"
                                              Content="开始逐章连续生成"
                                              Style="{StaticResource MaterialDesignRaisedButton}"
                                              HorizontalAlignment="Stretch"
                                              FontSize="10"
                                              Height="28"
                                              IsEnabled="False"
                                              Click="StartSequentialGeneration_Click"
                                              ToolTip="从指定章节开始，逐章生成细纲→正文→时间线，每章都会参考前一章内容确保连贯性"/>
                                        <StackPanel Orientation="Horizontal" Margin="0,3,0,0">
                                            <Button x:Name="PauseSequentialGenerationButton"
                                                  Content="暂停"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  FontSize="10"
                                                  Height="24"
                                                  Width="60"
                                                  Margin="0,0,3,0"
                                                  IsEnabled="False"
                                                  Click="PauseSequentialGeneration_Click"/>
                                            <Button x:Name="ResumeSequentialGenerationButton"
                                                  Content="继续"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  FontSize="10"
                                                  Height="24"
                                                  Width="60"
                                                  Margin="3,0,3,0"
                                                  IsEnabled="False"
                                                  Click="ResumeSequentialGeneration_Click"/>
                                            <Button x:Name="StopSequentialGenerationButton"
                                                  Content="停止"
                                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                                  FontSize="10"
                                                  Height="24"
                                                  Width="60"
                                                  Margin="3,0,0,0"
                                                  IsEnabled="False"
                                                  Click="StopSequentialGeneration_Click"/>
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 文件格式设置 -->
                            <materialDesign:Card Padding="8" Margin="0,0,0,6"
                                               Background="{DynamicResource MaterialDesignCardBackground}">
                                <StackPanel>
                                    <DockPanel>
                                        <materialDesign:PackIcon Kind="FileDocument" DockPanel.Dock="Left"
                                                               Width="14" Height="14"
                                                               VerticalAlignment="Center" Margin="0,0,6,0"/>
                                        <TextBlock Text="文件格式设置"
                                                 Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                 FontSize="11"/>
                                    </DockPanel>
                                    <ComboBox x:Name="FileFormatComboBox"
                                            materialDesign:HintAssist.Hint="选择保存格式"
                                            Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                            FontSize="10"
                                            Height="32"
                                            Margin="0,6,0,0"
                                            SelectedIndex="0">
                                        <ComboBoxItem Content=".docx (Word文档)" Tag="docx"/>
                                        <ComboBoxItem Content=".txt (纯文本)" Tag="txt"/>
                                    </ComboBox>
                                </StackPanel>
                            </materialDesign:Card>
                        </StackPanel>
                    </ScrollViewer>
                </StackPanel>
            </materialDesign:Card>

            <!-- 右侧内容显示区域 -->
            <materialDesign:Card Grid.Column="1" Padding="12" Margin="6,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 内容标题 -->
                    <DockPanel Grid.Row="0" Margin="0,0,0,12">
                        <TextBlock x:Name="ContentTitleText"
                                 Text="执行结果预览"
                                 Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                 FontSize="13"
                                 DockPanel.Dock="Left"/>

                        <!-- 进度指示器 -->
                        <ProgressBar x:Name="ExecutionProgressBar"
                                   DockPanel.Dock="Right"
                                   Width="180"
                                   Height="5"
                                   Visibility="Collapsed"
                                   Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                    </DockPanel>

                    <!-- 内容显示区域 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <TextBox x:Name="ContentDisplayTextBox"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               IsReadOnly="True"
                               FontFamily="Microsoft YaHei"
                               FontSize="12"
                               Text="请选择要执行的步骤开始创作..."/>
                    </ScrollViewer>

                    <!-- 状态信息 -->
                    <StackPanel Grid.Row="2" Margin="0,6,0,0">
                        <TextBlock x:Name="StatusText"
                                 Style="{StaticResource MaterialDesignBody2TextBlock}"
                                 FontSize="11"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Text="就绪"/>
                        <TextBlock x:Name="NetworkStatusText"
                                 Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                 FontSize="10"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                 Margin="0,3,0,0"
                                 Text="网络状态: 检查中..."
                                 Visibility="Collapsed"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- 底部按钮区域 -->
        <Grid Grid.Row="3" Margin="0,12,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧网络状态按钮 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Button x:Name="CheckNetworkButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Click="CheckNetwork_Click">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Wifi" Width="12" Height="12" Margin="0,0,3,0"/>
                            <TextBlock Text="检查网络"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>

            <!-- 右侧主要按钮 -->
            <StackPanel Grid.Column="1"
                        Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <!-- 执行控制按钮组 -->
                <Button x:Name="StartExecutionButton"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Click="StartExecution_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Play" Width="12" Height="12" Margin="0,0,3,0"/>
                        <TextBlock Text="开始执行"/>
                    </StackPanel>
                </Button>

                <Button x:Name="PauseExecutionButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Visibility="Collapsed"
                        Click="PauseExecution_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Pause" Width="12" Height="12" Margin="0,0,3,0"/>
                        <TextBlock Text="暂停执行"/>
                    </StackPanel>
                </Button>

                <Button x:Name="ContinueExecutionButton"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Visibility="Collapsed"
                        Click="ContinueExecution_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Play" Width="12" Height="12" Margin="0,0,3,0"/>
                        <TextBlock Text="继续执行"/>
                    </StackPanel>
                </Button>

                <Button x:Name="StopExecutionButton"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Visibility="Collapsed"
                        Click="StopExecution_Click">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Stop" Width="12" Height="12" Margin="0,0,3,0"/>
                        <TextBlock Text="停止执行"/>
                    </StackPanel>
                </Button>

                <!-- 状态管理按钮 -->
                <Button x:Name="SaveStateButton"
                        Content="保存状态"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Click="SaveState_Click"/>
                <Button x:Name="LoadStateButton"
                        Content="加载状态"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Click="LoadState_Click"/>
                <Button x:Name="CancelButton"
                        Content="取消"
                        Style="{StaticResource MaterialDesignFlatButton}"
                        FontSize="11"
                        Height="32"
                        Margin="0,0,6,0"
                        Click="Cancel_Click"/>
                <Button x:Name="CloseButton"
                        Content="关闭"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        FontSize="11"
                        Height="32"
                        Click="Close_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>

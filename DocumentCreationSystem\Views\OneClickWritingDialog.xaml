<Window x:Class="DocumentCreationSystem.Views.OneClickWritingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="一键写书设定"
        Height="600"
        Width="500"
        WindowStartupLocation="Manual"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}"
        Topmost="True"
        ShowInTaskbar="True"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">

    <!-- 悬浮窗口容器 -->
    <materialDesign:Card Margin="8" materialDesign:ElevationAssist.Elevation="Dp8"
                         Background="{DynamicResource MaterialDesignPaper}">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <DockPanel Grid.Row="0" Margin="0,0,0,16" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown" Background="Transparent">
                <materialDesign:PackIcon Kind="AutoFix" DockPanel.Dock="Left"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="一键写书设定" DockPanel.Dock="Left"
                         Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                         VerticalAlignment="Center"/>

                <!-- 窗口控制按钮 -->
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          Click="MinimizeWindow_Click"
                          ToolTip="最小化窗口">
                        <materialDesign:PackIcon Kind="WindowMinimize"/>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignIconButton}"
                          Click="CloseWindow_Click"
                          ToolTip="关闭设定窗口">
                        <materialDesign:PackIcon Kind="Close"/>
                    </Button>
                </StackPanel>
            </DockPanel>
        
            <!-- 主要内容区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 书籍基本信息 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="书籍基本信息" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="BookTitleTextBox"
                                         materialDesign:HintAssist.Hint="书籍名称"
                                         Margin="0,0,0,16"/>
                                
                                <TextBox x:Name="AuthorNameTextBox"
                                         materialDesign:HintAssist.Hint="作者名称"
                                         Margin="0,0,0,16"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                <ComboBox x:Name="GenreComboBox"
                                          materialDesign:HintAssist.Hint="小说类型"
                                          SelectedIndex="0"
                                          Margin="0,0,0,16"
                                          ToolTip="选择小说的类型，这会影响AI的创作风格、情节设计和语言表达。不同类型有不同的创作特点和读者期待。">
                                    <ComboBoxItem Content="都市修仙"/>
                                    <ComboBoxItem Content="玄幻奇幻"/>
                                    <ComboBoxItem Content="科幻未来"/>
                                    <ComboBoxItem Content="历史军事"/>
                                    <ComboBoxItem Content="悬疑推理"/>
                                    <ComboBoxItem Content="言情浪漫"/>
                                    <ComboBoxItem Content="其他"/>
                                </ComboBox>

                                <TextBox x:Name="TargetWordCountTextBox"
                                         materialDesign:HintAssist.Hint="目标总字数"
                                         Text="650000"
                                         Margin="0,0,0,16"
                                         ToolTip="设置整部小说的目标总字数。建议：短篇小说5-10万字，中篇小说10-30万字，长篇小说30-100万字。这个数值会影响故事的深度和复杂度。"/>
                            </StackPanel>
                        </Grid>
                        
                        <TextBox x:Name="CreativeDirectionTextBox"
                                 materialDesign:HintAssist.Hint="创作方向和主题描述"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 MinHeight="80"
                                 Margin="0,0,0,16"
                                 ToolTip="详细描述您想要创作的小说主题、故事背景、主要人物和核心冲突。这是AI创作的重要参考，描述越详细，生成的内容越符合您的期望。"/>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 创作设置 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,16">
                    <StackPanel>
                        <TextBlock Text="创作设置" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                <TextBox x:Name="VolumeCountTextBox"
                                         materialDesign:HintAssist.Hint="分卷数量"
                                         Text="10"
                                         Margin="0,0,0,16"
                                         ToolTip="将小说分成若干卷，每卷包含若干章节。分卷有助于故事结构层次化，便于情节安排和读者阅读。系统会自动平均分配章节到各卷中。"/>

                                <TextBox x:Name="ChapterCountTextBox"
                                         materialDesign:HintAssist.Hint="总章节数"
                                         Text="100"
                                         Margin="0,0,0,16"
                                         ToolTip="设置小说的总章节数量。这个数值决定了整部小说的篇幅长度。建议根据目标总字数和每章字数来计算：总章节数 = 目标总字数 ÷ 每章字数"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="8,0,8,0">
                                <TextBox x:Name="WordsPerChapterTextBox"
                                         materialDesign:HintAssist.Hint="每章字数"
                                         Text="6500"
                                         Margin="0,0,0,16"
                                         ToolTip="设置每章的目标字数。建议：网络小说2000-8000字/章，传统小说3000-6000字/章。这个数值影响章节内容的详细程度和阅读节奏。"/>

                                <TextBox x:Name="SegmentWordsTextBox"
                                         materialDesign:HintAssist.Hint="分段字数"
                                         Text="1000"
                                         Margin="0,0,0,16"
                                         ToolTip="设置每次AI创作的分段字数。系统会将每章分成多个段落来创作，每段约为此字数。建议800-1500字，过小会导致内容不连贯，过大会影响创作质量。"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Margin="8,0,0,0">
                                <TextBox x:Name="ReviewChaptersTextBox"
                                         materialDesign:HintAssist.Hint="回顾章节数"
                                         Text="3"
                                         Margin="0,0,0,16"
                                         ToolTip="设置AI创作时回顾前面多少章的内容来保持故事连贯性。建议2-5章，数值越大连贯性越好但处理时间越长。设为0则不回顾前文。"/>

                                <ComboBox x:Name="WritingStyleComboBox"
                                          materialDesign:HintAssist.Hint="写作风格"
                                          SelectedIndex="0"
                                          Margin="0,0,0,16"
                                          ToolTip="选择AI的写作风格：&#x0a;• 流畅自然：平衡的叙述风格，适合大多数题材&#x0a;• 细腻描写：注重细节和情感描写，适合文艺类作品&#x0a;• 紧凑节奏：快节奏叙述，适合悬疑、动作类作品&#x0a;• 幽默风趣：轻松幽默的语调，适合喜剧类作品">
                                    <ComboBoxItem Content="流畅自然"/>
                                    <ComboBoxItem Content="细腻描写"/>
                                    <ComboBoxItem Content="紧凑节奏"/>
                                    <ComboBoxItem Content="幽默风趣"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                            <CheckBox x:Name="AutoSaveCheckBox"
                                      Content="自动保存章节"
                                      IsChecked="True"
                                      Margin="0,0,16,0"
                                      ToolTip="开启后，每完成一章的创作会自动保存到项目文件夹中。建议开启以防止数据丢失。"/>

                            <CheckBox x:Name="GenerateOutlineFirstCheckBox"
                                      Content="先生成大纲"
                                      IsChecked="True"
                                      Margin="0,0,16,0"
                                      ToolTip="开启后，系统会先根据您的创作方向生成详细大纲，然后按大纲进行章节创作。这样能确保故事结构完整和情节连贯。"/>

                            <CheckBox x:Name="UseReferenceContentCheckBox"
                                      Content="参考编辑器内容"
                                      IsChecked="True"
                                      ToolTip="开启后，AI会参考文档编辑器中的现有内容（如人物设定、世界观等）来进行创作，确保内容一致性。"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 进度显示 -->
                <materialDesign:Card x:Name="ProgressCard" 
                                     Padding="16" 
                                     Margin="0,0,0,16"
                                     Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="创作进度" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" 
                                       x:Name="ProgressTextBlock" 
                                       Text="准备开始..."
                                       VerticalAlignment="Center"
                                       Margin="0,0,16,0"/>
                            
                            <ProgressBar Grid.Column="1" 
                                         x:Name="OverallProgressBar"
                                         Height="8"
                                         Minimum="0"
                                         Maximum="100"
                                         Value="0"/>
                            
                            <TextBlock Grid.Column="2" 
                                       x:Name="ProgressPercentageTextBlock" 
                                       Text="0%"
                                       VerticalAlignment="Center"
                                       Margin="16,0,0,0"/>
                        </Grid>
                        
                        <TextBlock x:Name="CurrentTaskTextBlock" 
                                   Text=""
                                   Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                   Margin="0,8,0,0"/>
                        
                        <TextBlock x:Name="EstimatedTimeTextBlock" 
                                   Text=""
                                   Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                   Margin="0,4,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 状态栏 -->
        <materialDesign:Card Grid.Row="2" 
                             Padding="12" 
                             Margin="0,16,0,16"
                             Background="{DynamicResource MaterialDesignToolBarBackground}">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon x:Name="StatusIcon" 
                                         Kind="Information" 
                                         VerticalAlignment="Center" 
                                         Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusTextBlock" 
                           Text="准备开始一键写书" 
                           VerticalAlignment="Center"/>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button x:Name="PauseButton"
                    Content="暂停" 
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="0,0,8,0"
                    Click="Pause_Click"
                    Visibility="Collapsed"/>
            <Button x:Name="CancelButton"
                    Content="取消" 
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="0,0,8,0"
                    Click="Cancel_Click"/>
            <Button x:Name="StartButton"
                    Content="开始写书" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="Start_Click"/>
            </StackPanel>
        </Grid>
    </materialDesign:Card>
</Window>

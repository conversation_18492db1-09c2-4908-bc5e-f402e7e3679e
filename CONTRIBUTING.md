# 贡献指南

感谢您对文档管理及AI创作系统项目的关注！我们欢迎各种形式的贡献，包括但不限于：

- 🐛 报告错误
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复
- ✨ 添加新功能

## 🚀 快速开始

### 开发环境设置

1. **系统要求**
   - Windows 10/11
   - .NET 8.0 SDK
   - Visual Studio 2022 或 Visual Studio Code
   - Git

2. **克隆项目**
   ```bash
   git clone https://github.com/your-username/document-creation-system.git
   cd document-creation-system
   ```

3. **安装依赖**
   ```bash
   dotnet restore
   ```

4. **编译项目**
   ```bash
   dotnet build
   ```

5. **运行测试**
   ```bash
   dotnet test
   ```

## 📋 贡献流程

### 1. 报告问题

如果您发现了错误或有改进建议：

1. 检查 [Issues](https://github.com/your-username/document-creation-system/issues) 确保问题未被报告
2. 创建新的 Issue，包含：
   - 清晰的标题和描述
   - 重现步骤（如果是错误）
   - 期望的行为
   - 实际的行为
   - 系统环境信息
   - 相关的错误日志或截图

### 2. 提交代码

1. **Fork 项目**
   - 点击 GitHub 页面右上角的 "Fork" 按钮

2. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   # 或
   git checkout -b bugfix/your-bugfix-name
   ```

3. **进行开发**
   - 遵循代码规范
   - 添加必要的测试
   - 更新相关文档

4. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **创建 Pull Request**
   - 在 GitHub 上创建 Pull Request
   - 填写详细的描述
   - 关联相关的 Issues

## 📝 代码规范

### C# 代码风格

我们遵循 Microsoft 的 C# 编码约定：

```csharp
// 类名使用 PascalCase
public class DocumentService
{
    // 私有字段使用 _camelCase
    private readonly ILogger<DocumentService> _logger;
    
    // 公共属性使用 PascalCase
    public string DocumentPath { get; set; }
    
    // 方法名使用 PascalCase
    public async Task<Document> CreateDocumentAsync(string name)
    {
        // 局部变量使用 camelCase
        var documentId = Guid.NewGuid();
        
        // 使用 using 语句管理资源
        using var context = new ApplicationDbContext();
        
        // 异步方法使用 ConfigureAwait(false)
        var result = await context.SaveChangesAsync().ConfigureAwait(false);
        
        return new Document { Id = documentId, Name = name };
    }
}
```

### 命名约定

- **类名**：PascalCase (`DocumentService`, `AIModelConfig`)
- **接口名**：以 I 开头的 PascalCase (`IAIService`, `IDocumentService`)
- **方法名**：PascalCase (`CreateDocumentAsync`, `GetProjectsAsync`)
- **属性名**：PascalCase (`DocumentPath`, `ProjectName`)
- **字段名**：_camelCase (`_logger`, `_httpClient`)
- **局部变量**：camelCase (`documentId`, `projectPath`)
- **常量**：PascalCase (`MaxRetryCount`, `DefaultTimeout`)

### 注释规范

```csharp
/// <summary>
/// 创建新的文档
/// </summary>
/// <param name="name">文档名称</param>
/// <param name="content">文档内容</param>
/// <returns>创建的文档对象</returns>
/// <exception cref="ArgumentNullException">当 name 为 null 时抛出</exception>
public async Task<Document> CreateDocumentAsync(string name, string content)
{
    // 单行注释使用 // 
    if (string.IsNullOrEmpty(name))
        throw new ArgumentNullException(nameof(name));
    
    /* 
     * 多行注释使用这种格式
     * 用于复杂的业务逻辑说明
     */
    
    return await _documentService.CreateAsync(name, content);
}
```

## 🧪 测试指南

### 单元测试

我们使用 xUnit 进行单元测试：

```csharp
public class DocumentServiceTests
{
    [Fact]
    public async Task CreateDocumentAsync_WithValidName_ShouldReturnDocument()
    {
        // Arrange
        var service = new DocumentService();
        var name = "Test Document";
        
        // Act
        var result = await service.CreateDocumentAsync(name);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(name, result.Name);
    }
    
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public async Task CreateDocumentAsync_WithInvalidName_ShouldThrowException(string name)
    {
        // Arrange
        var service = new DocumentService();
        
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            service.CreateDocumentAsync(name));
    }
}
```

### 集成测试

对于涉及数据库或外部服务的测试：

```csharp
public class AIServiceIntegrationTests : IClassFixture<TestFixture>
{
    private readonly TestFixture _fixture;
    
    public AIServiceIntegrationTests(TestFixture fixture)
    {
        _fixture = fixture;
    }
    
    [Fact]
    public async Task GenerateTextAsync_WithValidPrompt_ShouldReturnText()
    {
        // 使用测试配置
        var aiService = _fixture.GetService<IAIService>();
        
        var result = await aiService.GenerateTextAsync("测试提示词");
        
        Assert.NotNull(result);
        Assert.NotEmpty(result);
    }
}
```

## 📚 文档贡献

### 文档类型

1. **API 文档**：使用 XML 注释自动生成
2. **用户指南**：Markdown 格式，位于 `docs/` 目录
3. **开发文档**：技术文档和架构说明
4. **README**：项目概述和快速开始指南

### 文档规范

- 使用清晰的标题层次结构
- 提供代码示例
- 包含截图（如果适用）
- 保持内容简洁明了
- 使用中文编写用户文档

## 🔍 代码审查

### Pull Request 检查清单

在提交 PR 之前，请确保：

- [ ] 代码遵循项目的编码规范
- [ ] 添加了适当的单元测试
- [ ] 所有测试都通过
- [ ] 更新了相关文档
- [ ] 提交信息清晰明了
- [ ] 没有引入新的编译警告
- [ ] 性能没有明显下降

### 审查标准

我们会从以下方面审查代码：

1. **功能性**：代码是否正确实现了预期功能
2. **可读性**：代码是否易于理解和维护
3. **性能**：是否有性能问题或改进空间
4. **安全性**：是否存在安全漏洞
5. **测试覆盖率**：是否有足够的测试覆盖

## 🏷️ 版本管理

我们使用语义化版本控制 (Semantic Versioning)：

- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 提交信息格式

使用约定式提交 (Conventional Commits)：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型包括：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更新
- `style`: 代码格式修改
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(ai): add support for DeepSeek API
fix(ui): resolve document editor crash issue
docs: update installation guide
```

## 🤝 社区准则

### 行为准则

我们致力于为每个人提供友好、安全和欢迎的环境：

- 使用友好和包容的语言
- 尊重不同的观点和经验
- 优雅地接受建设性批评
- 关注对社区最有利的事情
- 对其他社区成员表示同理心

### 沟通渠道

- **GitHub Issues**：错误报告和功能请求
- **GitHub Discussions**：一般讨论和问答
- **Pull Requests**：代码审查和讨论

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. 查看现有的 Issues 和 Discussions
2. 阅读项目文档和 Wiki
3. 创建新的 Discussion 或 Issue
4. 联系维护者

感谢您的贡献！🎉

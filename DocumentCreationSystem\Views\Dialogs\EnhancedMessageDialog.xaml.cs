using MaterialDesignThemes.Wpf;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace DocumentCreationSystem.Views.Dialogs
{
    /// <summary>
    /// 增强的消息对话框，支持充分展示控件内容
    /// </summary>
    public partial class EnhancedMessageDialog : Window
    {
        /// <summary>
        /// 对话框结果
        /// </summary>
        public MessageBoxResult Result { get; private set; } = MessageBoxResult.None;

        /// <summary>
        /// 构造函数
        /// </summary>
        public EnhancedMessageDialog()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 显示信息对话框
        /// </summary>
        public static MessageBoxResult ShowInformation(string message, string title = "信息", string? details = null, Window? owner = null)
        {
            return ShowDialog(message, title, MessageBoxButton.OK, MessageBoxImage.Information, details, owner);
        }

        /// <summary>
        /// 显示警告对话框
        /// </summary>
        public static MessageBoxResult ShowWarning(string message, string title = "警告", string? details = null, Window? owner = null)
        {
            return ShowDialog(message, title, MessageBoxButton.OK, MessageBoxImage.Warning, details, owner);
        }

        /// <summary>
        /// 显示错误对话框
        /// </summary>
        public static MessageBoxResult ShowError(string message, string title = "错误", string? details = null, Window? owner = null)
        {
            return ShowDialog(message, title, MessageBoxButton.OK, MessageBoxImage.Error, details, owner);
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        public static MessageBoxResult ShowConfirmation(string message, string title = "确认", string? details = null, Window? owner = null)
        {
            return ShowDialog(message, title, MessageBoxButton.YesNo, MessageBoxImage.Question, details, owner);
        }

        /// <summary>
        /// 显示自定义对话框
        /// </summary>
        public static MessageBoxResult ShowDialog(
            string message, 
            string title, 
            MessageBoxButton buttons, 
            MessageBoxImage icon, 
            string? details = null, 
            Window? owner = null,
            FrameworkElement? customContent = null)
        {
            var dialog = new EnhancedMessageDialog();
            
            // 设置所有者
            if (owner != null)
            {
                dialog.Owner = owner;
            }

            // 设置标题
            dialog.Title = title;
            dialog.TitleText.Text = title;

            // 设置消息
            dialog.MessageText.Text = message;

            // 设置详细信息
            if (!string.IsNullOrEmpty(details))
            {
                dialog.DetailsExpander.Visibility = Visibility.Visible;
                dialog.DetailsText.Text = details;
            }

            // 设置自定义内容
            if (customContent != null)
            {
                dialog.CustomContentPresenter.Content = customContent;
            }

            // 设置图标
            dialog.SetIcon(icon);

            // 设置按钮
            dialog.SetButtons(buttons);

            // 显示对话框
            dialog.ShowDialog();

            return dialog.Result;
        }

        /// <summary>
        /// 设置图标
        /// </summary>
        private void SetIcon(MessageBoxImage icon)
        {
            switch (icon)
            {
                case MessageBoxImage.Information:
                    MessageIcon.Kind = PackIconKind.Information;
                    MessageIcon.Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243)); // 蓝色
                    break;
                case MessageBoxImage.Warning:
                    MessageIcon.Kind = PackIconKind.Warning;
                    MessageIcon.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // 橙色
                    break;
                case MessageBoxImage.Error:
                    MessageIcon.Kind = PackIconKind.Error;
                    MessageIcon.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54)); // 红色
                    break;
                case MessageBoxImage.Question:
                    MessageIcon.Kind = PackIconKind.HelpCircle;
                    MessageIcon.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // 绿色
                    break;
                default:
                    MessageIcon.Kind = PackIconKind.Information;
                    MessageIcon.Foreground = new SolidColorBrush(Color.FromRgb(33, 150, 243));
                    break;
            }
        }

        /// <summary>
        /// 设置按钮
        /// </summary>
        private void SetButtons(MessageBoxButton buttons)
        {
            ButtonPanel.Children.Clear();

            switch (buttons)
            {
                case MessageBoxButton.OK:
                    AddButton("确定", MessageBoxResult.OK, true);
                    break;
                case MessageBoxButton.OKCancel:
                    AddButton("取消", MessageBoxResult.Cancel, false);
                    AddButton("确定", MessageBoxResult.OK, true);
                    break;
                case MessageBoxButton.YesNo:
                    AddButton("否", MessageBoxResult.No, false);
                    AddButton("是", MessageBoxResult.Yes, true);
                    break;
                case MessageBoxButton.YesNoCancel:
                    AddButton("取消", MessageBoxResult.Cancel, false);
                    AddButton("否", MessageBoxResult.No, false);
                    AddButton("是", MessageBoxResult.Yes, true);
                    break;
            }
        }

        /// <summary>
        /// 添加按钮
        /// </summary>
        private void AddButton(string text, MessageBoxResult result, bool isDefault)
        {
            var button = new Button
            {
                Content = text,
                Margin = new Thickness(8, 0, 0, 0),
                MinWidth = 80,
                Height = 36
            };

            if (isDefault)
            {
                button.Style = (Style)FindResource("MaterialDesignRaisedButton");
                button.IsDefault = true;
            }
            else
            {
                button.Style = (Style)FindResource("MaterialDesignFlatButton");
            }

            button.Click += (s, e) =>
            {
                Result = result;
                DialogResult = true;
                Close();
            };

            ButtonPanel.Children.Add(button);
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Result = MessageBoxResult.Cancel;
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// 窗口加载完成事件
        /// </summary>
        protected override void OnSourceInitialized(EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // 自动调整窗口大小以适应内容
            SizeToContent = SizeToContent.Height;
            
            // 确保窗口不会超出屏幕
            var screen = SystemParameters.WorkArea;
            if (Width > screen.Width * 0.8)
            {
                Width = screen.Width * 0.8;
                SizeToContent = SizeToContent.Manual;
            }
            if (Height > screen.Height * 0.8)
            {
                Height = screen.Height * 0.8;
                SizeToContent = SizeToContent.Manual;
            }
        }
    }
}

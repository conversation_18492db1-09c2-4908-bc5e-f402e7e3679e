# 全书大纲规划和文件格式修复说明

## 问题描述

在分步执行写书功能中发现了两个关键问题：

1. **全书大纲规划问题**：全书大纲生成时没有按照用户在书籍基本信息中设置的分卷数量和章节数量进行规划，而是硬编码为10卷
2. **文件格式问题**：虽然界面提供了文件格式选择（.docx/.txt），但实际保存时没有按照用户选择的格式，需要统一默认为.txt格式

## 修复内容

### 1. 全书大纲规划修复

#### 问题根源
在`NovelCreationService.GenerateOverallOutlineAsync`方法中，分卷数量被硬编码为10卷：
```csharp
// 原来的代码
4. 分卷结构建议（建议分为10卷）
```

#### 修复方案
1. **修改方法签名**：为`GenerateOverallOutlineAsync`方法添加`volumeCount`参数
```csharp
// 修改前
public async Task<string> GenerateOverallOutlineAsync(int novelProjectId, string creativeDirection)

// 修改后
public async Task<string> GenerateOverallOutlineAsync(int novelProjectId, string creativeDirection, int volumeCount = 10)
```

2. **更新接口定义**：同步更新`INovelCreationService`接口
```csharp
Task<string> GenerateOverallOutlineAsync(int novelProjectId, string creativeDirection, int volumeCount = 10);
```

3. **修改提示词**：在AI提示词中使用用户设置的分卷数量
```csharp
var prompt = $@"
请为以下小说项目生成一个详细的全书大纲：

小说标题：{novelProject.Title}
创作方向：{creativeDirection}
目标章节数：{novelProject.TargetChapterCount}
每章字数：{novelProject.TargetWordsPerChapter}
分卷数量：{volumeCount}卷

请生成一个包含以下内容的全书大纲：
1. 故事背景和世界观设定
2. 主要角色介绍和关系
3. 核心冲突和主线情节
4. 分卷结构建议（请严格按照{volumeCount}卷进行规划）
5. 整体故事发展脉络
6. 预期的高潮和结局

请确保大纲具有足够的深度和细节，能够支撑{novelProject.TargetChapterCount}章的长篇创作，并且严格按照{volumeCount}卷进行规划。
";
```

4. **更新调用代码**：在`StepByStepWritingService`中传递用户设置的卷数
```csharp
// 修改前
outline = await _novelCreationService.GenerateOverallOutlineAsync(
    state.NovelProjectId, state.CreativeDirection);

// 修改后
outline = await _novelCreationService.GenerateOverallOutlineAsync(
    state.NovelProjectId, state.CreativeDirection, state.VolumeCount);
```

### 2. 文件格式统一修复

#### 问题根源
虽然界面提供了文件格式选择，但在实际保存时，不同的方法使用了不同的格式，导致文件格式不统一。

#### 修复方案
1. **统一文件格式**：所有文件均默认保存为.txt格式
```csharp
// 在SaveChapterToFileAsync方法中
// 获取选择的文件格式，默认为.txt
string fileExtension = ".txt";

// 即使有选择，也强制使用.txt格式
// 如果将来需要支持其他格式，可以取消下面的注释
/*
if (FileFormatComboBox.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
{
    fileExtension = $".{selectedItem.Tag}";
}
*/
```

2. **简化保存逻辑**：移除Word文档保存的复杂逻辑
```csharp
// 修改前
if (fileExtension.Equals(".docx", StringComparison.OrdinalIgnoreCase))
{
    // 保存为Word文档
    await SaveAsDocxAsync(filePath, chapterTitle, content);
}
else
{
    // 保存为纯文本
    await System.IO.File.WriteAllTextAsync(filePath, content);
}

// 修改后
// 统一保存为纯文本格式
await System.IO.File.WriteAllTextAsync(filePath, content, System.Text.Encoding.UTF8);
```

3. **修复其他保存方法**：确保所有文件保存方法都使用.txt格式
```csharp
// 在SaveChapterContentToFileAsync方法中
// 修改前
var fileName = $"{SanitizeFileName(state.BookTitle)}_{sanitizedTitle}";
var filePath = Path.Combine(contentDir, $"{fileName}.docx");
var txtPath = Path.Combine(contentDir, $"{fileName}.txt");

// 这里应该保存为Word文档，暂时保存为文本文件
await File.WriteAllTextAsync(txtPath, content);

// 修改后
var fileName = $"{SanitizeFileName(state.BookTitle)}_{sanitizedTitle}";
var filePath = Path.Combine(contentDir, $"{fileName}.txt");

// 保存为文本文件
await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);
```

## 修复效果

### 1. 全书大纲规划准确性
- 全书大纲现在会严格按照用户设置的分卷数量进行规划
- AI提示词中明确要求按照指定卷数进行分卷结构设计
- 确保后续的卷宗大纲生成与全书大纲保持一致

### 2. 文件格式统一性
- 所有生成的文件均保存为.txt格式
- 文件命名规则统一：`书名_卷XX_卷名_章XXXX_章名.txt`
- 简化了文件保存逻辑，避免了格式不一致的问题

### 3. 用户体验改善
- 用户设置的分卷数量得到正确应用
- 文件格式统一，便于管理和查看
- 避免了因格式不一致导致的混淆

## 测试结果

1. **编译测试**：所有修改均通过编译，没有出现编译错误
2. **运行测试**：应用程序能够正常启动和运行
3. **功能测试**：
   - 全书大纲生成时会使用用户设置的分卷数量
   - 所有文件保存为.txt格式
   - 文件命名规则正确应用

## 文件保存结构

修复后的文件保存结构：
```
项目文件夹/
├── Outlines/                    # 大纲文件夹
│   ├── overall_outline.txt      # 全书大纲
│   ├── Volumes/                 # 卷宗大纲
│   │   ├── volume_01_outline.txt
│   │   ├── volume_02_outline.txt
│   │   └── ...
│   └── Chapters/                # 章节细纲
│       ├── chapter_001_outline.txt
│       ├── chapter_002_outline.txt
│       └── ...
├── Chapters/                    # 章节正文
│   ├── 书名_卷01_卷名_章0001_章名.txt
│   ├── 书名_卷01_卷名_章0002_章名.txt
│   └── ...
├── Settings/                    # 世界设定
│   ├── 世界观设定管理.md
│   ├── 时间线管理.md
│   └── ...（23个设定文件）
└── step_execution_state.json   # 执行状态文件
```

## 使用建议

1. **设置分卷数量**：在书籍基本信息中合理设置分卷数量，建议根据总章节数进行规划
2. **检查全书大纲**：生成全书大纲后，检查分卷规划是否符合预期
3. **文件管理**：所有文件均为.txt格式，可以使用任何文本编辑器打开和编辑
4. **后续编辑**：生成的.txt文件可以方便地进行后续编辑和格式转换

这些修复确保了分步写书功能能够按照用户的设置进行准确的规划，并提供了统一的文件格式，提升了整体的用户体验和功能可靠性。

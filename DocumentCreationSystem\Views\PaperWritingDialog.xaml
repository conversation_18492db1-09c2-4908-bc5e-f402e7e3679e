<Window x:Class="DocumentCreationSystem.Views.PaperWritingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="一键写论文" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="0,16,0,8"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        </Style>
        
        <Style x:Key="ParameterLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12" CornerRadius="4" Margin="0,0,0,16">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="FileDocument" VerticalAlignment="Center" Margin="0,0,12,0" Width="24" Height="24"/>
                <TextBlock Text="AI一键写论文" VerticalAlignment="Center" FontSize="18" FontWeight="Medium"/>
                <TextBlock Text="根据给定素材自动生成学术论文" VerticalAlignment="Center" FontSize="12" 
                          Margin="16,0,0,0" Opacity="0.8"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：素材和基本设置 -->
                <StackPanel Grid.Column="0">
                    <!-- 素材文件选择 -->
                    <TextBlock Text="📄 素材文件" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="选择参考素材文件（支持.txt, .docx, .md格式）" 
                                      Style="{StaticResource ParameterLabelStyle}"/>
                            
                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="MaterialFilesTextBox" Grid.Column="0"
                                        materialDesign:HintAssist.Hint="点击右侧按钮选择文件..."
                                        IsReadOnly="True" Height="80" TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"/>
                                
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}"
                                       Click="SelectMaterialFiles_Click" Margin="8,0,0,0"
                                       ToolTip="选择素材文件">
                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                </Button>
                            </Grid>
                            
                            <TextBlock x:Name="MaterialFilesCountText" Text="已选择 0 个文件" 
                                      FontSize="11" Foreground="Gray" Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 论文基本信息 -->
                    <TextBlock Text="📋 论文基本信息" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="论文标题" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="PaperTitleTextBox" 
                                    materialDesign:HintAssist.Hint="请输入论文标题（可留空由AI生成）"
                                    Margin="0,0,0,12"/>
                            
                            <TextBlock Text="论文类型" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="PaperTypeComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="学术研究论文" IsSelected="True"/>
                                <ComboBoxItem Content="综述论文"/>
                                <ComboBoxItem Content="技术报告"/>
                                <ComboBoxItem Content="案例分析"/>
                                <ComboBoxItem Content="实验报告"/>
                                <ComboBoxItem Content="毕业论文"/>
                                <ComboBoxItem Content="会议论文"/>
                            </ComboBox>
                            
                            <TextBlock Text="学科领域" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="FieldTextBox" 
                                    materialDesign:HintAssist.Hint="如：计算机科学、生物学、经济学等"
                                    Margin="0,0,0,12"/>
                            
                            <TextBlock Text="目标字数" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="TargetWordCountTextBox" Text="8000"
                                    materialDesign:HintAssist.Hint="论文目标字数"
                                    Margin="0,0,0,12"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 用户要求 -->
                    <TextBlock Text="📝 用户要求" Style="{StaticResource SectionHeaderStyle}"/>

                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="附加创作要求（可选）" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="UserRequirementsTextBox"
                                    materialDesign:HintAssist.Hint="请输入您的特殊要求，如：重点关注某个方面、特定的研究方法、引用特定文献、避免某些内容等..."
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    MinHeight="80"
                                    MaxHeight="120"
                                    VerticalScrollBarVisibility="Auto"/>
                            <TextBlock Text="示例：请重点分析深度学习在医疗诊断中的应用，并对比传统方法的优劣势"
                                      FontSize="11" Opacity="0.7" Margin="0,8,0,0" FontStyle="Italic"/>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- 右侧：写作参数和高级设置 -->
                <StackPanel Grid.Column="2">
                    <!-- 写作参数 -->
                    <TextBlock Text="⚙️ 写作参数" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="写作风格" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="WritingStyleComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="学术严谨" IsSelected="True"/>
                                <ComboBoxItem Content="通俗易懂"/>
                                <ComboBoxItem Content="技术专业"/>
                                <ComboBoxItem Content="创新前沿"/>
                            </ComboBox>
                            
                            <TextBlock Text="引用风格" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="CitationStyleComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="APA" IsSelected="True"/>
                                <ComboBoxItem Content="MLA"/>
                                <ComboBoxItem Content="Chicago"/>
                                <ComboBoxItem Content="IEEE"/>
                                <ComboBoxItem Content="GB/T 7714"/>
                            </ComboBox>
                            
                            <TextBlock Text="创新度" Style="{StaticResource ParameterLabelStyle}"/>
                            <Slider x:Name="CreativitySlider" Minimum="0.1" Maximum="1.0" Value="0.7" 
                                   TickFrequency="0.1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                            <TextBlock x:Name="CreativityValueText" Text="0.7" FontSize="11" 
                                      Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 论文结构设置 -->
                    <TextBlock Text="📖 论文结构" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <CheckBox x:Name="IncludeAbstractCheckBox" Content="包含摘要" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeKeywordsCheckBox" Content="包含关键词" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeIntroductionCheckBox" Content="包含引言" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeLiteratureReviewCheckBox" Content="包含文献综述" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeMethodologyCheckBox" Content="包含研究方法" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeResultsCheckBox" Content="包含结果分析" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeDiscussionCheckBox" Content="包含讨论" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeConclusionCheckBox" Content="包含结论" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeReferencesCheckBox" Content="包含参考文献" IsChecked="True" Margin="0,4"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 输出设置 -->
                    <TextBlock Text="💾 输出设置" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16">
                        <StackPanel>
                            <TextBlock Text="输出格式" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="OutputFormatComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="Word文档 (.docx)" IsSelected="True"/>
                                <ComboBoxItem Content="文本文件 (.txt)"/>
                                <ComboBoxItem Content="Markdown (.md)"/>
                            </ComboBox>
                            
                            <CheckBox x:Name="SaveToProjectCheckBox" Content="保存到当前项目文件夹" 
                                     IsChecked="True" Margin="0,8"/>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- 底部按钮栏 -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 进度信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="StatusIcon" Kind="Information" 
                                        VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusText" Text="准备就绪" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 操作按钮 -->
            <Button Grid.Column="1" x:Name="StartButton" Content="开始生成论文" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="StartGeneration_Click" Margin="0,0,12,0" Padding="24,8"/>
            
            <Button Grid.Column="2" x:Name="CancelButton" Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click" Padding="24,8"/>
        </Grid>
    </Grid>
</Window>

<Window x:Class="DocumentCreationSystem.Views.ToolExecutionResultDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="工具执行结果" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon x:Name="StatusIcon" 
                                           Kind="CheckCircle" 
                                           VerticalAlignment="Center" 
                                           Width="24" Height="24"
                                           Margin="0,0,12,0"/>
                    <TextBlock x:Name="TitleText" 
                             Text="工具执行结果" 
                             FontSize="18" 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock x:Name="ExecutionTimeText" 
                             Text="执行时间: 0ms" 
                             VerticalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 结果内容 -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
            <!-- 基本信息 -->
            <TabItem Header="基本信息">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <TextBlock Text="执行状态" FontWeight="Medium" Margin="0,0,0,8"/>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon x:Name="ResultStatusIcon" 
                                                           Kind="CheckCircle" 
                                                           VerticalAlignment="Center"
                                                           Margin="0,0,8,0"/>
                                    <TextBlock x:Name="StatusText" 
                                             Text="成功" 
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16">
                            <StackPanel Margin="16">
                                <TextBlock Text="执行消息" FontWeight="Medium" Margin="0,0,0,8"/>
                                <TextBlock x:Name="MessageText" 
                                         Text="执行成功" 
                                         TextWrapping="Wrap"/>
                            </StackPanel>
                        </materialDesign:Card>
                        
                        <materialDesign:Card x:Name="ErrorCard" 
                                           materialDesign:ElevationAssist.Elevation="Dp1" 
                                           Margin="0,0,0,16"
                                           Visibility="Collapsed">
                            <StackPanel Margin="16">
                                <TextBlock Text="错误详情" FontWeight="Medium" Margin="0,0,0,8"/>
                                <TextBlock x:Name="ErrorText" 
                                         TextWrapping="Wrap"
                                         Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- 结果数据 -->
            <TabItem Header="结果数据">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">
                        <TextBox x:Name="ResultDataTextBox"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               IsReadOnly="True"
                               MinHeight="300"
                               FontFamily="Consolas"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- 生成文件 -->
            <TabItem x:Name="GeneratedFilesTab" Header="生成文件" Visibility="Collapsed">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">
                        <ListBox x:Name="GeneratedFilesListBox"
                               Style="{StaticResource MaterialDesignListBox}"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <!-- 执行日志 -->
            <TabItem x:Name="LogsTab" Header="执行日志" Visibility="Collapsed">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="24">
                        <TextBox x:Name="LogsTextBox"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               IsReadOnly="True"
                               MinHeight="300"
                               FontFamily="Consolas"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>
        
        <!-- 按钮栏 -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,16,0,0">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Right" 
                      Margin="24,16">
                <Button x:Name="CopyResultButton"
                      Content="复制结果" 
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="CopyResult_Click"/>
                <Button x:Name="SaveResultButton"
                      Content="保存结果" 
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="SaveResult_Click"/>
                <Button Content="关闭" 
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="Close_Click"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>

using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models;

/// <summary>
/// 世界设定实体类
/// </summary>
public class WorldSetting
{
    public int Id { get; set; }

    /// <summary>
    /// 所属小说项目ID
    /// </summary>
    public int NovelProjectId { get; set; }

    /// <summary>
    /// 世界观设定
    /// </summary>
    public WorldViewSetting? WorldView { get; set; }

    /// <summary>
    /// 修炼体系
    /// </summary>
    public CultivationSystem? Cultivation { get; set; }

    /// <summary>
    /// 政治体系
    /// </summary>
    public PoliticalSystem? Political { get; set; }

    /// <summary>
    /// 货币体系
    /// </summary>
    public CurrencySystem? Currency { get; set; }

    /// <summary>
    /// 商业体系
    /// </summary>
    public BusinessSystem? Business { get; set; }

    /// <summary>
    /// 种族类别
    /// </summary>
    public List<RaceCategory> Races { get; set; } = new();

    /// <summary>
    /// 功法体系
    /// </summary>
    public TechniqueSystem? Techniques { get; set; }

    /// <summary>
    /// 装备体系
    /// </summary>
    public EquipmentSystem? Equipment { get; set; }

    /// <summary>
    /// 宠物体系
    /// </summary>
    public PetSystem? Pets { get; set; }

    /// <summary>
    /// 地图结构
    /// </summary>
    public MapStructure? Map { get; set; }

    /// <summary>
    /// 维度结构
    /// </summary>
    public DimensionStructure? Dimensions { get; set; }

    /// <summary>
    /// 灵宝体系
    /// </summary>
    public TreasureSystem? Treasures { get; set; }

    /// <summary>
    /// 生民体系
    /// </summary>
    public PopulationSystem? Population { get; set; }

    /// <summary>
    /// 司法体系
    /// </summary>
    public JudicialSystem? Judicial { get; set; }

    /// <summary>
    /// 职业体系
    /// </summary>
    public ProfessionSystem? Professions { get; set; }

    /// <summary>
    /// 经济系统
    /// </summary>
    public EconomicSystem? Economy { get; set; }

    /// <summary>
    /// 关系网络
    /// </summary>
    public RelationshipNetwork? Relationships { get; set; }

    /// <summary>
    /// 角色设定
    /// </summary>
    public CharacterSettings? Characters { get; set; }

    /// <summary>
    /// 势力设定
    /// </summary>
    public FactionSettings? Factions { get; set; }

    /// <summary>
    /// 功法设定
    /// </summary>
    public TechniqueSettings? TechniqueDetails { get; set; }

    /// <summary>
    /// 秘境设定
    /// </summary>
    public SecretRealmSettings? SecretRealms { get; set; }

    /// <summary>
    /// 地图详细设定
    /// </summary>
    public DetailedMapSettings? DetailedMaps { get; set; }

    /// <summary>
    /// 历史事件设定
    /// </summary>
    public HistoricalEventSettings? HistoricalEvents { get; set; }

    /// <summary>
    /// 传说神话设定
    /// </summary>
    public LegendMythSettings? LegendsMyths { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// 世界观设定
/// </summary>
public class WorldViewSetting
{
    /// <summary>
    /// 地理环境
    /// </summary>
    public string? Geography { get; set; }

    /// <summary>
    /// 历史背景
    /// </summary>
    public string? History { get; set; }

    /// <summary>
    /// 文化特色
    /// </summary>
    public string? Culture { get; set; }

    /// <summary>
    /// 自然法则
    /// </summary>
    public string? NaturalLaws { get; set; }

    /// <summary>
    /// 时间体系
    /// </summary>
    public string? TimeSystem { get; set; }

    /// <summary>
    /// 语言文字
    /// </summary>
    public string? Languages { get; set; }
}

/// <summary>
/// 修炼体系
/// </summary>
public class CultivationSystem
{
    /// <summary>
    /// 能力体系描述
    /// </summary>
    public string? AbilitySystem { get; set; }

    /// <summary>
    /// 等级划分
    /// </summary>
    public List<CultivationLevel> Levels { get; set; } = new();

    /// <summary>
    /// 修炼方法
    /// </summary>
    public List<string> Methods { get; set; } = new();

    /// <summary>
    /// 突破条件
    /// </summary>
    public string? BreakthroughConditions { get; set; }

    /// <summary>
    /// 修炼资源
    /// </summary>
    public List<string> Resources { get; set; } = new();
}

/// <summary>
/// 修炼等级
/// </summary>
public class CultivationLevel
{
    /// <summary>
    /// 等级名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 等级描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 等级顺序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 子等级
    /// </summary>
    public List<string> SubLevels { get; set; } = new();

    /// <summary>
    /// 能力特征
    /// </summary>
    public List<string> Abilities { get; set; } = new();
}

/// <summary>
/// 政治体系
/// </summary>
public class PoliticalSystem
{
    /// <summary>
    /// 国家列表
    /// </summary>
    public List<Nation> Nations { get; set; } = new();

    /// <summary>
    /// 势力组织
    /// </summary>
    public List<Faction> Factions { get; set; } = new();

    /// <summary>
    /// 组织结构
    /// </summary>
    public string? OrganizationStructure { get; set; }

    /// <summary>
    /// 权力分配
    /// </summary>
    public string? PowerDistribution { get; set; }

    /// <summary>
    /// 法律体系
    /// </summary>
    public string? LegalSystem { get; set; }
}

/// <summary>
/// 国家
/// </summary>
public class Nation
{
    /// <summary>
    /// 国家名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 国家描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 政治制度
    /// </summary>
    public string? PoliticalSystem { get; set; }

    /// <summary>
    /// 统治者
    /// </summary>
    public string? Ruler { get; set; }

    /// <summary>
    /// 领土范围
    /// </summary>
    public string? Territory { get; set; }

    /// <summary>
    /// 军事力量
    /// </summary>
    public string? MilitaryPower { get; set; }
}

/// <summary>
/// 势力
/// </summary>
public class Faction
{
    /// <summary>
    /// 势力名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 势力类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 势力描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 领导者
    /// </summary>
    public string? Leader { get; set; }

    /// <summary>
    /// 势力范围
    /// </summary>
    public string? Influence { get; set; }

    /// <summary>
    /// 主要目标
    /// </summary>
    public string? Goals { get; set; }
}

/// <summary>
/// 货币体系
/// </summary>
public class CurrencySystem
{
    /// <summary>
    /// 货币制度描述
    /// </summary>
    public string? MonetarySystem { get; set; }

    /// <summary>
    /// 货币类型
    /// </summary>
    public List<Currency> Currencies { get; set; } = new();

    /// <summary>
    /// 汇率体系
    /// </summary>
    public string? ExchangeRates { get; set; }

    /// <summary>
    /// 金融机构
    /// </summary>
    public List<string> FinancialInstitutions { get; set; } = new();

    /// <summary>
    /// 支付方式
    /// </summary>
    public List<string> PaymentMethods { get; set; } = new();
}

/// <summary>
/// 货币
/// </summary>
public class Currency
{
    /// <summary>
    /// 货币名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 货币类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 价值描述
    /// </summary>
    public string? Value { get; set; }

    /// <summary>
    /// 使用范围
    /// </summary>
    public string? Usage { get; set; }
}

/// <summary>
/// 商业体系
/// </summary>
public class BusinessSystem
{
    /// <summary>
    /// 贸易路线
    /// </summary>
    public List<string> TradeRoutes { get; set; } = new();

    /// <summary>
    /// 商会组织
    /// </summary>
    public List<string> MerchantGuilds { get; set; } = new();

    /// <summary>
    /// 市场机制
    /// </summary>
    public string? MarketMechanism { get; set; }

    /// <summary>
    /// 商业法规
    /// </summary>
    public string? BusinessRegulations { get; set; }

    /// <summary>
    /// 主要商品
    /// </summary>
    public List<string> MainGoods { get; set; } = new();
}

/// <summary>
/// 种族类别
/// </summary>
public class RaceCategory
{
    /// <summary>
    /// 种族名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 种族特性
    /// </summary>
    public List<string> Characteristics { get; set; } = new();

    /// <summary>
    /// 分布地区
    /// </summary>
    public string? Distribution { get; set; }

    /// <summary>
    /// 与其他种族关系
    /// </summary>
    public string? Relationships { get; set; }

    /// <summary>
    /// 文化差异
    /// </summary>
    public string? CulturalDifferences { get; set; }

    /// <summary>
    /// 天赋能力
    /// </summary>
    public List<string> TalentAbilities { get; set; } = new();
}

/// <summary>
/// 功法体系
/// </summary>
public class TechniqueSystem
{
    /// <summary>
    /// 功法分类
    /// </summary>
    public List<TechniqueCategory> Categories { get; set; } = new();

    /// <summary>
    /// 修炼方法
    /// </summary>
    public string? CultivationMethods { get; set; }

    /// <summary>
    /// 技能招式
    /// </summary>
    public List<Skill> Skills { get; set; } = new();

    /// <summary>
    /// 传承体系
    /// </summary>
    public string? InheritanceSystem { get; set; }
}

/// <summary>
/// 功法分类
/// </summary>
public class TechniqueCategory
{
    /// <summary>
    /// 分类名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 分类描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 等级划分
    /// </summary>
    public List<string> Grades { get; set; } = new();

    /// <summary>
    /// 修炼要求
    /// </summary>
    public string? Requirements { get; set; }
}

/// <summary>
/// 技能
/// </summary>
public class Skill
{
    /// <summary>
    /// 技能名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 技能类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 技能描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 威力等级
    /// </summary>
    public string? PowerLevel { get; set; }

    /// <summary>
    /// 学习条件
    /// </summary>
    public string? LearningConditions { get; set; }
}

/// <summary>
/// 装备体系
/// </summary>
public class EquipmentSystem
{
    /// <summary>
    /// 装备分类
    /// </summary>
    public List<EquipmentCategory> Categories { get; set; } = new();

    /// <summary>
    /// 强化系统
    /// </summary>
    public string? EnhancementSystem { get; set; }

    /// <summary>
    /// 套装效果
    /// </summary>
    public List<SetEffect> SetEffects { get; set; } = new();

    /// <summary>
    /// 制作配方
    /// </summary>
    public List<CraftingRecipe> CraftingRecipes { get; set; } = new();
}

/// <summary>
/// 装备分类
/// </summary>
public class EquipmentCategory
{
    /// <summary>
    /// 分类名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 品质等级
    /// </summary>
    public List<string> QualityLevels { get; set; } = new();

    /// <summary>
    /// 属性类型
    /// </summary>
    public List<string> AttributeTypes { get; set; } = new();
}

/// <summary>
/// 套装效果
/// </summary>
public class SetEffect
{
    /// <summary>
    /// 套装名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 套装件数要求
    /// </summary>
    public int RequiredPieces { get; set; }

    /// <summary>
    /// 效果描述
    /// </summary>
    public string? Effect { get; set; }
}

/// <summary>
/// 制作配方
/// </summary>
public class CraftingRecipe
{
    /// <summary>
    /// 配方名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 所需材料
    /// </summary>
    public List<string> Materials { get; set; } = new();

    /// <summary>
    /// 制作条件
    /// </summary>
    public string? Conditions { get; set; }

    /// <summary>
    /// 成功率
    /// </summary>
    public string? SuccessRate { get; set; }
}

/// <summary>
/// 宠物体系
/// </summary>
public class PetSystem
{
    /// <summary>
    /// 宠物分类
    /// </summary>
    public List<PetCategory> Categories { get; set; } = new();

    /// <summary>
    /// 进化系统
    /// </summary>
    public string? EvolutionSystem { get; set; }

    /// <summary>
    /// 技能培养
    /// </summary>
    public string? SkillTraining { get; set; }

    /// <summary>
    /// 繁殖机制
    /// </summary>
    public string? BreedingMechanism { get; set; }
}

/// <summary>
/// 宠物分类
/// </summary>
public class PetCategory
{
    /// <summary>
    /// 分类名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 特性描述
    /// </summary>
    public string? Characteristics { get; set; }

    /// <summary>
    /// 成长潜力
    /// </summary>
    public string? GrowthPotential { get; set; }

    /// <summary>
    /// 栖息环境
    /// </summary>
    public string? Habitat { get; set; }
}

/// <summary>
/// 地图结构
/// </summary>
public class MapStructure
{
    /// <summary>
    /// 地图层级
    /// </summary>
    public List<MapLevel> Levels { get; set; } = new();

    /// <summary>
    /// 地形特征
    /// </summary>
    public List<TerrainFeature> TerrainFeatures { get; set; } = new();

    /// <summary>
    /// 区域划分
    /// </summary>
    public List<Region> Regions { get; set; } = new();

    /// <summary>
    /// 特殊地点
    /// </summary>
    public List<SpecialLocation> SpecialLocations { get; set; } = new();
}

/// <summary>
/// 地图层级
/// </summary>
public class MapLevel
{
    /// <summary>
    /// 层级名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 层级描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 层级顺序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 包含区域
    /// </summary>
    public List<string> ContainedRegions { get; set; } = new();
}

/// <summary>
/// 地形特征
/// </summary>
public class TerrainFeature
{
    /// <summary>
    /// 地形名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 地形类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 特征描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 影响效果
    /// </summary>
    public string? Effects { get; set; }
}

/// <summary>
/// 区域
/// </summary>
public class Region
{
    /// <summary>
    /// 区域名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 区域类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 区域描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 控制势力
    /// </summary>
    public string? ControllingFaction { get; set; }

    /// <summary>
    /// 危险等级
    /// </summary>
    public string? DangerLevel { get; set; }
}

/// <summary>
/// 特殊地点
/// </summary>
public class SpecialLocation
{
    /// <summary>
    /// 地点名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 地点类型
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 地点描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 特殊效果
    /// </summary>
    public string? SpecialEffects { get; set; }

    /// <summary>
    /// 进入条件
    /// </summary>
    public string? EntryConditions { get; set; }
}

/// <summary>
/// 维度结构
/// </summary>
public class DimensionStructure
{
    /// <summary>
    /// 多维度世界描述
    /// </summary>
    public string? MultidimensionalWorld { get; set; }

    /// <summary>
    /// 空间层次
    /// </summary>
    public List<SpatialLayer> SpatialLayers { get; set; } = new();

    /// <summary>
    /// 维度法则
    /// </summary>
    public string? DimensionalLaws { get; set; }

    /// <summary>
    /// 传送机制
    /// </summary>
    public string? TransportationMechanism { get; set; }
}

/// <summary>
/// 空间层次
/// </summary>
public class SpatialLayer
{
    /// <summary>
    /// 层次名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 层次描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 层次等级
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// 特殊规则
    /// </summary>
    public string? SpecialRules { get; set; }
}

/// <summary>
/// 灵宝体系
/// </summary>
public class TreasureSystem
{
    /// <summary>
    /// 灵宝品级
    /// </summary>
    public List<TreasureGrade> Grades { get; set; } = new();

    /// <summary>
    /// 器灵系统
    /// </summary>
    public string? SpiritSystem { get; set; }

    /// <summary>
    /// 炼制方法
    /// </summary>
    public string? RefinementMethods { get; set; }

    /// <summary>
    /// 传承历史
    /// </summary>
    public string? InheritanceHistory { get; set; }
}

/// <summary>
/// 灵宝品级
/// </summary>
public class TreasureGrade
{
    /// <summary>
    /// 品级名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 品级描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 威力等级
    /// </summary>
    public string? PowerLevel { get; set; }

    /// <summary>
    /// 稀有程度
    /// </summary>
    public string? Rarity { get; set; }
}

/// <summary>
/// 生民体系
/// </summary>
public class PopulationSystem
{
    /// <summary>
    /// 人口统计
    /// </summary>
    public string? Demographics { get; set; }

    /// <summary>
    /// 社会阶层
    /// </summary>
    public List<SocialClass> SocialClasses { get; set; } = new();

    /// <summary>
    /// 生活方式
    /// </summary>
    public string? Lifestyle { get; set; }

    /// <summary>
    /// 文化习俗
    /// </summary>
    public string? CulturalCustoms { get; set; }

    /// <summary>
    /// 不同维度的人口分布
    /// </summary>
    public string? DimensionalDistribution { get; set; }
}

/// <summary>
/// 社会阶层
/// </summary>
public class SocialClass
{
    /// <summary>
    /// 阶层名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 阶层描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 权力地位
    /// </summary>
    public string? PowerStatus { get; set; }

    /// <summary>
    /// 经济状况
    /// </summary>
    public string? EconomicStatus { get; set; }
}

/// <summary>
/// 司法体系
/// </summary>
public class JudicialSystem
{
    /// <summary>
    /// 法院体系
    /// </summary>
    public string? CourtSystem { get; set; }

    /// <summary>
    /// 审判程序
    /// </summary>
    public string? TrialProcedures { get; set; }

    /// <summary>
    /// 执法机构
    /// </summary>
    public List<string> LawEnforcementAgencies { get; set; } = new();

    /// <summary>
    /// 法律条文
    /// </summary>
    public string? LegalCodes { get; set; }

    /// <summary>
    /// 不同维度的司法制度
    /// </summary>
    public string? DimensionalJustice { get; set; }
}

/// <summary>
/// 职业体系
/// </summary>
public class ProfessionSystem
{
    /// <summary>
    /// 职业分类
    /// </summary>
    public List<ProfessionCategory> Categories { get; set; } = new();

    /// <summary>
    /// 技能要求
    /// </summary>
    public string? SkillRequirements { get; set; }

    /// <summary>
    /// 晋升路径
    /// </summary>
    public string? AdvancementPaths { get; set; }

    /// <summary>
    /// 行业组织
    /// </summary>
    public List<string> IndustryOrganizations { get; set; } = new();

    /// <summary>
    /// 不同维度的职业结构
    /// </summary>
    public string? DimensionalProfessions { get; set; }
}

/// <summary>
/// 职业分类
/// </summary>
public class ProfessionCategory
{
    /// <summary>
    /// 职业名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 职业描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 所需技能
    /// </summary>
    public List<string> RequiredSkills { get; set; } = new();

    /// <summary>
    /// 社会地位
    /// </summary>
    public string? SocialStatus { get; set; }
}

/// <summary>
/// 经济系统
/// </summary>
public class EconomicSystem
{
    /// <summary>
    /// 资源分配
    /// </summary>
    public string? ResourceAllocation { get; set; }

    /// <summary>
    /// 交易方式
    /// </summary>
    public List<string> TradingMethods { get; set; } = new();

    /// <summary>
    /// 经济政策
    /// </summary>
    public string? EconomicPolicies { get; set; }

    /// <summary>
    /// 主要产业
    /// </summary>
    public List<string> MajorIndustries { get; set; } = new();

    /// <summary>
    /// 经济周期
    /// </summary>
    public string? EconomicCycles { get; set; }
}

/// <summary>
/// 关系网络
/// </summary>
public class RelationshipNetwork
{
    /// <summary>
    /// 势力关系
    /// </summary>
    public List<FactionRelationship> FactionRelationships { get; set; } = new();

    /// <summary>
    /// 家族关系
    /// </summary>
    public List<FamilyRelationship> FamilyRelationships { get; set; } = new();

    /// <summary>
    /// 组织间关系
    /// </summary>
    public List<OrganizationRelationship> OrganizationRelationships { get; set; } = new();

    /// <summary>
    /// 关系图谱描述
    /// </summary>
    public string? RelationshipMap { get; set; }
}

/// <summary>
/// 角色设定
/// </summary>
public class CharacterSettings
{
    /// <summary>
    /// 主要角色列表
    /// </summary>
    public List<MainCharacter> MainCharacters { get; set; } = new();

    /// <summary>
    /// 配角列表
    /// </summary>
    public List<SupportingCharacter> SupportingCharacters { get; set; } = new();

    /// <summary>
    /// 反派角色列表
    /// </summary>
    public List<VillainCharacter> VillainCharacters { get; set; } = new();

    /// <summary>
    /// 角色命名规则
    /// </summary>
    public string? NamingConventions { get; set; }

    /// <summary>
    /// 角色外貌特征规则
    /// </summary>
    public string? AppearanceGuidelines { get; set; }

    /// <summary>
    /// 角色性格类型分类
    /// </summary>
    public List<PersonalityType> PersonalityTypes { get; set; } = new();
}

/// <summary>
/// 主要角色
/// </summary>
public class MainCharacter
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Background { get; set; } = string.Empty;
    public string Personality { get; set; } = string.Empty;
    public string Appearance { get; set; } = string.Empty;
    public string InitialCultivationLevel { get; set; } = string.Empty;
    public List<string> SpecialAbilities { get; set; } = new();
    public List<string> ImportantRelationships { get; set; } = new();
    public string CharacterArc { get; set; } = string.Empty;
    public List<string> KeyItems { get; set; } = new();
}

/// <summary>
/// 配角
/// </summary>
public class SupportingCharacter
{
    public string Name { get; set; } = string.Empty;
    public string Role { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string RelationshipToMain { get; set; } = string.Empty;
    public string Importance { get; set; } = string.Empty;
    public List<int> AppearanceChapters { get; set; } = new();
}

/// <summary>
/// 反派角色
/// </summary>
public class VillainCharacter
{
    public string Name { get; set; } = string.Empty;
    public string VillainType { get; set; } = string.Empty;
    public string Motivation { get; set; } = string.Empty;
    public string PowerLevel { get; set; } = string.Empty;
    public string Methods { get; set; } = string.Empty;
    public string Weakness { get; set; } = string.Empty;
    public string FinalFate { get; set; } = string.Empty;
}

/// <summary>
/// 性格类型
/// </summary>
public class PersonalityType
{
    public string TypeName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> Traits { get; set; } = new();
    public List<string> TypicalBehaviors { get; set; } = new();
}

/// <summary>
/// 势力设定
/// </summary>
public class FactionSettings
{
    /// <summary>
    /// 主要势力列表
    /// </summary>
    public List<MajorFaction> MajorFactions { get; set; } = new();

    /// <summary>
    /// 次要势力列表
    /// </summary>
    public List<MinorFaction> MinorFactions { get; set; } = new();

    /// <summary>
    /// 势力关系图
    /// </summary>
    public string? FactionRelationshipMap { get; set; }

    /// <summary>
    /// 势力冲突历史
    /// </summary>
    public List<FactionConflict> ConflictHistory { get; set; } = new();

    /// <summary>
    /// 势力联盟体系
    /// </summary>
    public List<FactionAlliance> Alliances { get; set; } = new();
}

/// <summary>
/// 主要势力
/// </summary>
public class MajorFaction
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // 宗门、帝国、商会等
    public string Description { get; set; } = string.Empty;
    public string Territory { get; set; } = string.Empty;
    public string PowerLevel { get; set; } = string.Empty;
    public string Leadership { get; set; } = string.Empty;
    public List<string> CoreMembers { get; set; } = new();
    public string Ideology { get; set; } = string.Empty;
    public List<string> Resources { get; set; } = new();
    public List<string> Enemies { get; set; } = new();
    public List<string> Allies { get; set; } = new();
    public string HistoricalBackground { get; set; } = string.Empty;
}

/// <summary>
/// 次要势力
/// </summary>
public class MinorFaction
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Affiliation { get; set; } = string.Empty; // 隶属于哪个主要势力
    public string Role { get; set; } = string.Empty;
}

/// <summary>
/// 势力冲突
/// </summary>
public class FactionConflict
{
    public string ConflictName { get; set; } = string.Empty;
    public List<string> InvolvedFactions { get; set; } = new();
    public string Cause { get; set; } = string.Empty;
    public string Outcome { get; set; } = string.Empty;
    public string TimeFrame { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
}

/// <summary>
/// 势力联盟
/// </summary>
public class FactionAlliance
{
    public string AllianceName { get; set; } = string.Empty;
    public List<string> MemberFactions { get; set; } = new();
    public string Purpose { get; set; } = string.Empty;
    public string Terms { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
}

/// <summary>
/// 功法设定
/// </summary>
public class TechniqueSettings
{
    /// <summary>
    /// 功法分类体系
    /// </summary>
    public List<TechniqueCategory> Categories { get; set; } = new();

    /// <summary>
    /// 著名功法列表
    /// </summary>
    public List<FamousTechnique> FamousTechniques { get; set; } = new();

    /// <summary>
    /// 功法等级划分
    /// </summary>
    public List<TechniqueGrade> Grades { get; set; } = new();

    /// <summary>
    /// 功法传承规则
    /// </summary>
    public string? InheritanceRules { get; set; }

    /// <summary>
    /// 功法修炼限制
    /// </summary>
    public string? CultivationRestrictions { get; set; }

    /// <summary>
    /// 功法冲突规则
    /// </summary>
    public string? ConflictRules { get; set; }
}

// TechniqueCategory 已在其他地方定义

/// <summary>
/// 著名功法
/// </summary>
public class FamousTechnique
{
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string Grade { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public List<string> Requirements { get; set; } = new();
    public List<string> Effects { get; set; } = new();
    public string Rarity { get; set; } = string.Empty;
    public List<string> KnownPractitioners { get; set; } = new();
    public string Weaknesses { get; set; } = string.Empty;
}

/// <summary>
/// 功法等级
/// </summary>
public class TechniqueGrade
{
    public string GradeName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string PowerLevel { get; set; } = string.Empty;
    public string Rarity { get; set; } = string.Empty;
    public List<string> TypicalFeatures { get; set; } = new();
}

/// <summary>
/// 秘境设定
/// </summary>
public class SecretRealmSettings
{
    /// <summary>
    /// 秘境列表
    /// </summary>
    public List<SecretRealm> Realms { get; set; } = new();

    /// <summary>
    /// 秘境分类
    /// </summary>
    public List<RealmCategory> Categories { get; set; } = new();

    /// <summary>
    /// 秘境开启规则
    /// </summary>
    public string? OpeningRules { get; set; }

    /// <summary>
    /// 秘境探索指南
    /// </summary>
    public string? ExplorationGuidelines { get; set; }
}

/// <summary>
/// 秘境
/// </summary>
public class SecretRealm
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string AccessMethod { get; set; } = string.Empty;
    public string DangerLevel { get; set; } = string.Empty;
    public List<string> Treasures { get; set; } = new();
    public List<string> Guardians { get; set; } = new();
    public List<string> Challenges { get; set; } = new();
    public string History { get; set; } = string.Empty;
    public string OpeningConditions { get; set; } = string.Empty;
    public string TimeLimit { get; set; } = string.Empty;
}

/// <summary>
/// 秘境分类
/// </summary>
public class RealmCategory
{
    public string CategoryName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string TypicalFeatures { get; set; } = string.Empty;
    public string DangerLevel { get; set; } = string.Empty;
    public List<string> CommonRewards { get; set; } = new();
}

/// <summary>
/// 详细地图设定
/// </summary>
public class DetailedMapSettings
{
    /// <summary>
    /// 大陆详细信息
    /// </summary>
    public List<ContinentDetail> Continents { get; set; } = new();

    /// <summary>
    /// 重要城市
    /// </summary>
    public List<ImportantCity> Cities { get; set; } = new();

    /// <summary>
    /// 危险区域
    /// </summary>
    public List<DangerousZone> DangerousZones { get; set; } = new();

    /// <summary>
    /// 交通路线
    /// </summary>
    public List<TransportRoute> TransportRoutes { get; set; } = new();

    /// <summary>
    /// 气候分布
    /// </summary>
    public string? ClimateDistribution { get; set; }
}

/// <summary>
/// 大陆详细信息
/// </summary>
public class ContinentDetail
{
    public string Name { get; set; } = string.Empty;
    public string Geography { get; set; } = string.Empty;
    public string Climate { get; set; } = string.Empty;
    public List<string> MajorRegions { get; set; } = new();
    public List<string> NaturalResources { get; set; } = new();
    public List<string> DominantFactions { get; set; } = new();
    public string CulturalCharacteristics { get; set; } = string.Empty;
}

/// <summary>
/// 重要城市
/// </summary>
public class ImportantCity
{
    public string Name { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // 都城、商业城、修炼圣地等
    public string Description { get; set; } = string.Empty;
    public string RulingFaction { get; set; } = string.Empty;
    public List<string> SpecialFeatures { get; set; } = new();
    public string Population { get; set; } = string.Empty;
    public List<string> ImportantBuildings { get; set; } = new();
}

/// <summary>
/// 危险区域
/// </summary>
public class DangerousZone
{
    public string Name { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string DangerType { get; set; } = string.Empty;
    public string DangerLevel { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> Threats { get; set; } = new();
    public List<string> PotentialRewards { get; set; } = new();
}

/// <summary>
/// 交通路线
/// </summary>
public class TransportRoute
{
    public string RouteName { get; set; } = string.Empty;
    public string StartPoint { get; set; } = string.Empty;
    public string EndPoint { get; set; } = string.Empty;
    public string TransportMethod { get; set; } = string.Empty;
    public string Duration { get; set; } = string.Empty;
    public string DangerLevel { get; set; } = string.Empty;
    public string Cost { get; set; } = string.Empty;
}

/// <summary>
/// 历史事件设定
/// </summary>
public class HistoricalEventSettings
{
    /// <summary>
    /// 重大历史事件
    /// </summary>
    public List<MajorHistoricalEvent> MajorEvents { get; set; } = new();

    /// <summary>
    /// 历史时期划分
    /// </summary>
    public List<HistoricalPeriod> Periods { get; set; } = new();

    /// <summary>
    /// 历史人物
    /// </summary>
    public List<HistoricalFigure> HistoricalFigures { get; set; } = new();

    /// <summary>
    /// 历史遗迹
    /// </summary>
    public List<HistoricalRelic> Relics { get; set; } = new();
}

/// <summary>
/// 重大历史事件
/// </summary>
public class MajorHistoricalEvent
{
    public string EventName { get; set; } = string.Empty;
    public string TimeFrame { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<string> KeyFigures { get; set; } = new();
    public List<string> InvolvedFactions { get; set; } = new();
    public string Cause { get; set; } = string.Empty;
    public string Outcome { get; set; } = string.Empty;
    public string Impact { get; set; } = string.Empty;
    public List<string> Consequences { get; set; } = new();
}

/// <summary>
/// 历史时期
/// </summary>
public class HistoricalPeriod
{
    public string PeriodName { get; set; } = string.Empty;
    public string TimeSpan { get; set; } = string.Empty;
    public string Characteristics { get; set; } = string.Empty;
    public List<string> MajorEvents { get; set; } = new();
    public List<string> DominantFactions { get; set; } = new();
    public string CulturalFeatures { get; set; } = string.Empty;
}

/// <summary>
/// 历史人物
/// </summary>
public class HistoricalFigure
{
    public string Name { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Achievements { get; set; } = string.Empty;
    public string PowerLevel { get; set; } = string.Empty;
    public string Faction { get; set; } = string.Empty;
    public string LegacyInfluence { get; set; } = string.Empty;
}

/// <summary>
/// 历史遗迹
/// </summary>
public class HistoricalRelic
{
    public string Name { get; set; } = string.Empty;
    public string Location { get; set; } = string.Empty;
    public string Period { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string OriginalPurpose { get; set; } = string.Empty;
    public string CurrentState { get; set; } = string.Empty;
    public List<string> Treasures { get; set; } = new();
    public string AccessDifficulty { get; set; } = string.Empty;
}

/// <summary>
/// 传说神话设定
/// </summary>
public class LegendMythSettings
{
    /// <summary>
    /// 创世神话
    /// </summary>
    public string? CreationMyth { get; set; }

    /// <summary>
    /// 神祇体系
    /// </summary>
    public List<Deity> Deities { get; set; } = new();

    /// <summary>
    /// 传说故事
    /// </summary>
    public List<LegendaryTale> LegendaryTales { get; set; } = new();

    /// <summary>
    /// 神器传说
    /// </summary>
    public List<ArtifactLegend> ArtifactLegends { get; set; } = new();

    /// <summary>
    /// 预言体系
    /// </summary>
    public List<Prophecy> Prophecies { get; set; } = new();

    /// <summary>
    /// 禁忌知识
    /// </summary>
    public List<ForbiddenKnowledge> ForbiddenKnowledge { get; set; } = new();
}

/// <summary>
/// 神祇
/// </summary>
public class Deity
{
    public string Name { get; set; } = string.Empty;
    public string Domain { get; set; } = string.Empty; // 掌管领域
    public string Description { get; set; } = string.Empty;
    public string PowerLevel { get; set; } = string.Empty;
    public List<string> Symbols { get; set; } = new();
    public List<string> Followers { get; set; } = new();
    public string Mythology { get; set; } = string.Empty;
    public string CurrentStatus { get; set; } = string.Empty; // 是否还活跃
}

/// <summary>
/// 传说故事
/// </summary>
public class LegendaryTale
{
    public string Title { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty; // 英雄传说、爱情传说等
    public string Summary { get; set; } = string.Empty;
    public List<string> KeyCharacters { get; set; } = new();
    public string Moral { get; set; } = string.Empty;
    public string HistoricalBasis { get; set; } = string.Empty;
    public string PopularityLevel { get; set; } = string.Empty;
}

/// <summary>
/// 神器传说
/// </summary>
public class ArtifactLegend
{
    public string ArtifactName { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Powers { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public string CurrentLocation { get; set; } = string.Empty;
    public List<string> PreviousOwners { get; set; } = new();
    public string Curse { get; set; } = string.Empty;
}

/// <summary>
/// 预言
/// </summary>
public class Prophecy
{
    public string Title { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
    public string Prophet { get; set; } = string.Empty;
    public string TimeOfProphecy { get; set; } = string.Empty;
    public string InterpretationDifficulty { get; set; } = string.Empty;
    public string FulfillmentStatus { get; set; } = string.Empty;
    public List<string> KeyElements { get; set; } = new();
}

/// <summary>
/// 禁忌知识
/// </summary>
public class ForbiddenKnowledge
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // 功法、咒术、秘密等
    public string Description { get; set; } = string.Empty;
    public string DangerLevel { get; set; } = string.Empty;
    public string Consequences { get; set; } = string.Empty;
    public string Origin { get; set; } = string.Empty;
    public List<string> KnownPossessors { get; set; } = new();
    public string AccessRestrictions { get; set; } = string.Empty;
}

/// <summary>
/// 势力关系
/// </summary>
public class FactionRelationship
{
    /// <summary>
    /// 势力A
    /// </summary>
    public string FactionA { get; set; } = string.Empty;

    /// <summary>
    /// 势力B
    /// </summary>
    public string FactionB { get; set; } = string.Empty;

    /// <summary>
    /// 关系类型
    /// </summary>
    public string? RelationshipType { get; set; }

    /// <summary>
    /// 关系描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 关系强度
    /// </summary>
    public string? Strength { get; set; }
}

/// <summary>
/// 家族关系
/// </summary>
public class FamilyRelationship
{
    /// <summary>
    /// 家族A
    /// </summary>
    public string FamilyA { get; set; } = string.Empty;

    /// <summary>
    /// 家族B
    /// </summary>
    public string FamilyB { get; set; } = string.Empty;

    /// <summary>
    /// 关系类型
    /// </summary>
    public string? RelationshipType { get; set; }

    /// <summary>
    /// 关系历史
    /// </summary>
    public string? History { get; set; }
}

/// <summary>
/// 组织关系
/// </summary>
public class OrganizationRelationship
{
    /// <summary>
    /// 组织A
    /// </summary>
    public string OrganizationA { get; set; } = string.Empty;

    /// <summary>
    /// 组织B
    /// </summary>
    public string OrganizationB { get; set; } = string.Empty;

    /// <summary>
    /// 合作类型
    /// </summary>
    public string? CooperationType { get; set; }

    /// <summary>
    /// 合作内容
    /// </summary>
    public string? CooperationContent { get; set; }
}

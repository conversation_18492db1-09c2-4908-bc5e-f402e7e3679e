<Window x:Class="DocumentCreationSystem.Views.ProjectToolsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="项目工具" Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Window.Resources>
        <Style x:Key="ToolCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
        <Style x:Key="CategoryHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="Margin" Value="16,16,16,8"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,8">
            <Grid Margin="24,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Tools" 
                                           VerticalAlignment="Center" 
                                           Width="24" Height="24"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="项目工具" 
                             FontSize="20" 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <ComboBox x:Name="ProjectTypeComboBox"
                            materialDesign:HintAssist.Hint="项目类型"
                            materialDesign:HintAssist.IsFloating="True"
                            Style="{StaticResource MaterialDesignOutlinedComboBox}"
                            Width="150"
                            SelectionChanged="ProjectType_SelectionChanged">
                        <ComboBoxItem Content="小说" Tag="Novel" IsSelected="True"/>
                        <ComboBoxItem Content="文档" Tag="Document"/>
                        <ComboBoxItem Content="研究" Tag="Research"/>
                        <ComboBoxItem Content="博客" Tag="Blog"/>
                        <ComboBoxItem Content="全部" Tag="All"/>
                    </ComboBox>
                    
                    <TextBox x:Name="SearchTextBox"
                           materialDesign:HintAssist.Hint="搜索工具..."
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           Width="200"
                           Margin="16,0,0,0"
                           TextChanged="Search_TextChanged">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="RefreshTools_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="刷新工具"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="CustomTools_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                <TextBlock Text="自定义工具"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 工具列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <StackPanel x:Name="ToolsPanel" Margin="16"/>
        </ScrollViewer>
        
        <!-- 状态栏 -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,8,0,0">
            <Grid Margin="16,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock x:Name="StatusText" 
                             Text="就绪" 
                             VerticalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="16,0"/>
                    <TextBlock x:Name="ToolCountText" 
                             Text="0 个工具可用" 
                             VerticalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="使用统计"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Click="ViewStatistics_Click"/>
                    <Button Content="帮助"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          Click="ShowHelp_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 工具执行进度对话框 -->
        <materialDesign:DialogHost x:Name="ProgressDialogHost" Grid.RowSpan="3">
            <materialDesign:DialogHost.DialogContent>
                <StackPanel Margin="32" Width="300">
                    <TextBlock x:Name="ProgressTitle" 
                             Text="执行工具中..." 
                             FontSize="16" 
                             FontWeight="Medium"
                             HorizontalAlignment="Center"
                             Margin="0,0,0,16"/>
                    
                    <ProgressBar x:Name="ProgressBar" 
                               IsIndeterminate="True"
                               Margin="0,0,0,16"/>
                    
                    <TextBlock x:Name="ProgressMessage" 
                             Text="正在处理，请稍候..."
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    
                    <Button Content="取消"
                          Style="{StaticResource MaterialDesignFlatButton}"
                          HorizontalAlignment="Center"
                          Margin="0,16,0,0"
                          Click="CancelExecution_Click"/>
                </StackPanel>
            </materialDesign:DialogHost.DialogContent>
        </materialDesign:DialogHost>
    </Grid>
</Window>

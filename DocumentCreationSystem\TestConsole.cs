using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;

namespace DocumentCreationSystem
{
    public class TestConsole
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("开始测试控制台应用程序...");

                // 创建主机构建器
                var hostBuilder = Host.CreateDefaultBuilder(args)
                    .ConfigureServices((context, services) =>
                    {
                        Console.WriteLine("配置服务...");
                        
                        // 注册基本服务
                        services.AddLogging();
                        services.AddSingleton<IDataStorageService, JsonDataStorageService>();
                        
                        Console.WriteLine("服务配置完成");
                    });

                Console.WriteLine("构建主机...");
                var host = hostBuilder.Build();

                Console.WriteLine("启动主机...");
                await host.StartAsync();

                Console.WriteLine("获取数据存储服务...");
                var dataStorage = host.Services.GetRequiredService<IDataStorageService>();

                Console.WriteLine("初始化数据存储...");
                await dataStorage.LoadAsync();

                Console.WriteLine("测试完成，按任意键退出...");
                Console.ReadKey();

                await host.StopAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}

# 章节细纲生成示例

## 改进前后对比

### 改进前的章节细纲生成

**输入信息：**
- 书籍标题：《仙路逆天》
- 创作方向：玄幻修仙
- 章节号：第3章
- 卷宗大纲：主角初入修仙界，遇到各种挑战

**生成的细纲（问题示例）：**
```
## 第3章：初入宗门

### 场景设定
- 时间：某日清晨
- 地点：某个宗门
- 环境：山清水秀

### 人物安排
- 主要人物：主角
- 次要人物：师兄师姐

### 主线发展
- 核心事件：主角进入宗门
- 爽点设计：展现天赋
- 冲突点：与师兄发生冲突
- 悬念设置：神秘的功法

### 预期结果
- 情节推进：主角开始修炼
- 角色成长：获得功法
- 章节结尾：准备修炼
```

**问题分析：**
1. 缺乏具体的世界观细节
2. 人物设定模糊
3. 没有参考前文，可能重复
4. 修炼体系不明确

### 改进后的章节细纲生成

**输入信息：**
- 书籍标题：《仙路逆天》
- 创作方向：玄幻修仙
- 章节号：第3章
- 卷宗大纲：主角初入修仙界，遇到各种挑战
- **世界设定文件：**
  - 世界观设定：九州大陆，修仙盛行
  - 修炼体系：练气、筑基、金丹、元婴
  - 势力设定：青云宗、天魔教、散修联盟
  - 角色设定：主角林天，天赋异禀但出身低微
- **前文参考：**
  - 第1章：林天在凡人村庄发现修仙天赋
  - 第2章：林天离开家乡，前往青云宗

**生成的细纲（改进示例）：**
```
## 第3章：青云宗入门试炼

### 场景设定
- 时间：九州历3847年春，卯时（早晨5-7点）
- 地点：青云宗外门试炼场，位于青云山半山腰
- 环境：晨雾缭绕的试炼广场，数百名少年聚集，灵气浓郁
- 相关势力：青云宗外门，试炼长老团

### 人物安排
- 主要人物：林天（16岁，练气一层初期，来自偏远村庄）
- 次要人物：
  - 试炼长老张无忌（筑基后期，负责入门考核）
  - 同期试炼者：王富贵（商贾之子）、李小雅（散修后代）
- 人物关系：林天与其他试炼者初次相遇，身份差距明显
- 人物心理：林天紧张但坚定，渴望证明自己

### 主线发展
- 核心事件：青云宗入门试炼正式开始，包含灵根测试、体能考核、心性试炼三个环节
- 爽点设计：
  - 林天在灵根测试中展现罕见的五行灵根
  - 体能考核中凭借在山村锻炼的基础超越城市少年
- 冲突点：
  - 王富贵因出身优越看不起林天，公然挑衅
  - 心性试炼中面临幻境考验，差点被心魔迷惑
- 悬念设置：
  - 试炼长老对林天的五行灵根露出异样神色
  - 暗中观察的神秘人物对林天产生兴趣
- 世界观展现：
  - 详细描述青云宗的等级制度（外门、内门、核心弟子）
  - 展现修仙界对灵根的重视程度
  - 体现不同出身修士之间的阶层差异

### 预期结果
- 情节推进：林天成功通过入门试炼，正式成为青云宗外门弟子
- 角色成长：
  - 修为稳固在练气一层中期
  - 心性得到磨练，更加坚韧
  - 对修仙界的残酷有了初步认识
- 伏笔铺设：
  - 为后续与王富贵的冲突埋下伏笔
  - 神秘人物的关注为后续剧情做铺垫
  - 五行灵根的特殊性将影响后续修炼
- 章节结尾：林天获得外门弟子身份牌和基础功法《青云诀》，准备进入外门修炼
```

## 改进效果对比

### 1. 世界观一致性
**改进前：** 模糊的"某个宗门"、"山清水秀"
**改进后：** 具体的"青云宗"、"九州大陆"、详细的时间地点设定

### 2. 角色设定丰富度
**改进前：** 简单的"主角"、"师兄师姐"
**改进后：** 详细的角色背景、年龄、修为、出身、心理状态

### 3. 剧情连贯性
**改进前：** 没有前文参考，可能重复
**改进后：** 明确承接前两章内容，避免剧情重复

### 4. 修炼体系明确性
**改进前：** 模糊的"修炼"、"功法"
**改进后：** 具体的修炼等级、功法名称、修为进展

### 5. 冲突设计合理性
**改进前：** 简单的"与师兄发生冲突"
**改进后：** 基于出身差异的合理冲突，符合世界观设定

## 技术实现要点

### 1. 世界设定文件读取
```csharp
// 自动读取项目中的世界设定文件
var worldSettings = await ReadRelevantWorldSettingsAsync(projectPath);

// 包含的文件类型：
// - 世界观设定管理.md
// - 修炼体系设定管理.md  
// - 角色设定管理.md
// - 势力管理.md
// 等23个设定文件
```

### 2. 前文参考机制
```csharp
// 获取前3章的细纲作为参考
var previousOutlines = GetPreviousChapterOutlines(state, chapterNumber, 3);

// 避免剧情重复，确保连贯性
```

### 3. 增强的提示词构建
```csharp
// 构建包含世界设定和前文参考的详细提示词
var prompt = BuildEnhancedChapterOutlinePrompt(
    state, chapterNumber, volume, worldSettings, previousOutlines);
```

## 使用建议

### 1. 完善世界设定文件
在使用改进功能前，确保项目文件夹中包含完整的世界设定文件：
- 创建详细的世界观设定
- 明确修炼体系和等级
- 设计丰富的角色背景
- 规划势力关系网络

### 2. 定期检查细纲质量
- 生成细纲后仔细检查是否符合世界观设定
- 确认与前文的连贯性
- 验证角色行为的合理性

### 3. 适当调整生成参数
- 根据需要调整AI生成的token数量
- 调整temperature参数控制创意程度
- 必要时手动修改生成的细纲

通过这些改进，章节细纲生成将更加准确、丰富和连贯，显著提升一键写书功能的质量。

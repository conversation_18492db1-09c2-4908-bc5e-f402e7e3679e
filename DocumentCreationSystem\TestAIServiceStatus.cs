using DocumentCreationSystem.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace DocumentCreationSystem
{
    /// <summary>
    /// AI服务状态诊断工具
    /// </summary>
    public class AIServiceStatusDiagnostic
    {
        public static async Task RunDiagnosticAsync()
        {
            Console.WriteLine("=== AI服务状态诊断 ===");
            Console.WriteLine();

            try
            {
                // 创建服务容器
                var services = new ServiceCollection();
                
                // 添加日志服务
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // 添加AI相关服务
                services.AddSingleton<AIModelConfigService>();
                services.AddSingleton<AIServiceManager>();
                services.AddSingleton<ZhipuAIService>();
                services.AddSingleton<LMStudioService>();
                services.AddSingleton<DeepSeekService>();
                services.AddSingleton<OllamaService>();
                services.AddSingleton<NovelCreationService>();
                services.AddSingleton<StepByStepWritingService>();

                var serviceProvider = services.BuildServiceProvider();

                // 获取服务
                var configService = serviceProvider.GetRequiredService<AIModelConfigService>();
                var aiServiceManager = serviceProvider.GetRequiredService<AIServiceManager>();
                var novelService = serviceProvider.GetRequiredService<NovelCreationService>();

                Console.WriteLine("1. 检查AI模型配置...");
                var config = await configService.GetConfigAsync();
                Console.WriteLine($"   平台: {config.Platform}");
                Console.WriteLine($"   温度: {config.Temperature}");
                Console.WriteLine($"   最大令牌: {config.MaxTokens}");
                Console.WriteLine($"   超时: {config.Timeout}秒");
                Console.WriteLine();

                Console.WriteLine("2. 检查当前AI服务状态...");
                var currentProvider = aiServiceManager.GetCurrentProviderName();
                var currentModel = aiServiceManager.GetCurrentModel();
                Console.WriteLine($"   当前提供者: {currentProvider ?? "未设置"}");
                Console.WriteLine($"   当前模型: {currentModel?.Name ?? "未设置"}");
                Console.WriteLine();

                Console.WriteLine("3. 检查各平台配置...");
                
                // 检查Ollama配置
                Console.WriteLine($"   Ollama:");
                Console.WriteLine($"     URL: {config.OllamaConfig.BaseUrl}");
                Console.WriteLine($"     选择的模型: {config.OllamaConfig.SelectedModel ?? "未选择"}");
                
                // 检查LM Studio配置
                Console.WriteLine($"   LM Studio:");
                Console.WriteLine($"     URL: {config.LMStudioConfig.BaseUrl}");
                Console.WriteLine($"     选择的模型: {config.LMStudioConfig.SelectedModel ?? "未选择"}");
                
                // 检查智谱AI配置
                Console.WriteLine($"   智谱AI:");
                Console.WriteLine($"     API Key: {(string.IsNullOrEmpty(config.ZhipuAIConfig.ApiKey) ? "未设置" : "已设置")}");
                Console.WriteLine($"     模型: {config.ZhipuAIConfig.Model}");
                
                // 检查DeepSeek配置
                Console.WriteLine($"   DeepSeek:");
                Console.WriteLine($"     API Key: {(string.IsNullOrEmpty(config.DeepSeekConfig.ApiKey) ? "未设置" : "已设置")}");
                Console.WriteLine($"     模型: {config.DeepSeekConfig.Model}");
                Console.WriteLine();

                Console.WriteLine("4. 测试AI服务连接...");
                try
                {
                    var testResult = await configService.TestConnectionAsync(config);
                    Console.WriteLine($"   连接测试: {(testResult ? "成功" : "失败")}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   连接测试失败: {ex.Message}");
                }
                Console.WriteLine();

                Console.WriteLine("5. 测试文本生成...");
                try
                {
                    var testText = await aiServiceManager.GenerateTextAsync("请说'测试成功'", 50, 0.7f);
                    Console.WriteLine($"   生成测试: 成功");
                    Console.WriteLine($"   生成内容: {testText}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   生成测试失败: {ex.Message}");
                    Console.WriteLine($"   详细错误: {ex}");
                }
                Console.WriteLine();

                Console.WriteLine("6. 测试小说创作服务...");
                try
                {
                    // 这里需要一个有效的项目ID，我们先跳过这个测试
                    Console.WriteLine("   小说创作服务测试: 跳过（需要有效项目ID）");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   小说创作服务测试失败: {ex.Message}");
                }

                Console.WriteLine();
                Console.WriteLine("=== 诊断完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"诊断过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
        }
    }
}

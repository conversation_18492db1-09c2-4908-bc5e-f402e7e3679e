<Window x:Class="DocumentCreationSystem.Views.Dialogs.AIToolResultDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="工具执行结果" 
        Height="600" 
        Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,15,0,5"/>
            <Setter Property="Foreground" Value="#2196F3"/>
        </Style>
        
        <Style x:Key="DataTextStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas, Courier New"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,2"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Padding="15,10">
            <Border.Background>
                <SolidColorBrush x:Name="HeaderBackground" Color="#4CAF50"/>
            </Border.Background>
            <StackPanel>
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="StatusIcon" 
                               Text="✓" 
                               FontSize="20" 
                               Foreground="White" 
                               Margin="0,0,10,0"/>
                    <TextBlock x:Name="ResultTitleText" 
                               Text="工具执行成功" 
                               FontSize="16" 
                               FontWeight="Bold" 
                               Foreground="White"/>
                </StackPanel>
                <TextBlock x:Name="ToolIdText" 
                           Text="工具ID: example-tool" 
                           FontSize="12" 
                           Foreground="#E8F5E8" 
                           Margin="0,5,0,0"/>
                <TextBlock x:Name="ExecutionTimeText" 
                           Text="执行时间: 1234ms" 
                           FontSize="12" 
                           Foreground="#E8F5E8" 
                           Margin="0,2,0,0"/>
            </StackPanel>
        </Border>

        <TabControl Grid.Row="1" Margin="10">
            <TabItem Header="基本信息">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel>
                        <TextBlock Text="执行状态" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock x:Name="StatusText" 
                                   Text="成功" 
                                   Style="{StaticResource DataTextStyle}"
                                   Foreground="Green"/>
                        
                        <TextBlock Text="结果消息" Style="{StaticResource SectionHeaderStyle}"/>
                        <TextBlock x:Name="MessageText" 
                                   Text="工具执行完成" 
                                   Style="{StaticResource DataTextStyle}"/>
                        
                        <TextBlock x:Name="ErrorHeader" 
                                   Text="错误详情" 
                                   Style="{StaticResource SectionHeaderStyle}"
                                   Visibility="Collapsed"/>
                        <TextBlock x:Name="ErrorText" 
                                   Style="{StaticResource DataTextStyle}"
                                   Foreground="Red"
                                   Visibility="Collapsed"/>
                        
                        <TextBlock Text="执行时间" Style="{StaticResource SectionHeaderStyle}"/>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock x:Name="ExecutionTimeDetailText" 
                                       Text="1234ms" 
                                       Style="{StaticResource DataTextStyle}"/>
                            <TextBlock x:Name="ExecutionDateText" 
                                       Text="(2024-01-01 12:00:00)" 
                                       Style="{StaticResource DataTextStyle}"
                                       Foreground="Gray"
                                       Margin="10,0,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="结果数据">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel>
                        <TextBlock Text="输出数据" Style="{StaticResource SectionHeaderStyle}"/>
                        <Border Background="#F8F9FA" 
                                BorderBrush="#E0E0E0" 
                                BorderThickness="1" 
                                Padding="10" 
                                CornerRadius="4">
                            <TextBox x:Name="DataTextBox" 
                                     IsReadOnly="True"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     FontFamily="Consolas, Courier New"
                                     FontSize="11"
                                     TextWrapping="Wrap"
                                     VerticalScrollBarVisibility="Auto"
                                     MinHeight="200"/>
                        </Border>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                            <Button x:Name="CopyDataButton" 
                                    Content="复制数据" 
                                    Padding="10,5" 
                                    Margin="0,0,10,0"
                                    Click="CopyDataButton_Click"/>
                            <Button x:Name="SaveDataButton" 
                                    Content="保存到文件" 
                                    Padding="10,5"
                                    Click="SaveDataButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="执行日志">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                    <StackPanel>
                        <TextBlock Text="执行日志" Style="{StaticResource SectionHeaderStyle}"/>
                        <Border Background="#F8F9FA" 
                                BorderBrush="#E0E0E0" 
                                BorderThickness="1" 
                                Padding="10" 
                                CornerRadius="4">
                            <ListBox x:Name="LogsListBox" 
                                     Background="Transparent"
                                     BorderThickness="0"
                                     FontFamily="Consolas, Courier New"
                                     FontSize="11"
                                     MinHeight="200">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding}" 
                                                   TextWrapping="Wrap"
                                                   Margin="0,1"/>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Border>
                        
                        <Button x:Name="CopyLogsButton" 
                                Content="复制日志" 
                                Padding="10,5" 
                                Margin="0,10,0,0"
                                HorizontalAlignment="Left"
                                Click="CopyLogsButton_Click"/>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>
        </TabControl>

        <Border Grid.Row="2" Background="#F8F9FA" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="RetryButton" 
                        Content="重新执行" 
                        Padding="15,8" 
                        Margin="0,0,10,0"
                        Background="#FF9800"
                        Foreground="White"
                        BorderThickness="0"
                        Click="RetryButton_Click"/>
                
                <Button x:Name="CloseButton" 
                        Content="关闭" 
                        Padding="15,8"
                        Background="#2196F3"
                        Foreground="White"
                        BorderThickness="0"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

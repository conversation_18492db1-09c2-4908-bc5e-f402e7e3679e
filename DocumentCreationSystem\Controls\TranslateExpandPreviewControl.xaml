<UserControl x:Class="DocumentCreationSystem.Controls.TranslateExpandPreviewControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}"
             Visibility="Collapsed">
    
    <Grid>
        <!-- 主预览容器 -->
        <Border x:Name="PreviewContainer" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="1"
                CornerRadius="8"
                Margin="8"
                Padding="16">
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题栏 -->
                <Grid Grid.Row="0" Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock x:Name="TitleText" 
                               Grid.Column="0"
                               Text="AI处理结果预览"
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Foreground="{DynamicResource MaterialDesignBody}"/>
                    
                    <Button x:Name="CloseButton" 
                            Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Click="CloseButton_Click"
                            ToolTip="关闭预览">
                        <materialDesign:PackIcon Kind="Close"/>
                    </Button>
                </Grid>

                <!-- 内容区域 -->
                <Grid Grid.Row="1">
                    <!-- 普通模式 -->
                    <ScrollViewer x:Name="NormalModeViewer" 
                                  VerticalScrollBarVisibility="Auto"
                                  HorizontalScrollBarVisibility="Auto">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 原文标签 -->
                            <TextBlock Grid.Row="0" 
                                       Text="原文："
                                       Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                       Margin="0,0,0,8"/>
                            
                            <!-- 原文内容 -->
                            <Border Grid.Row="1"
                                    Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                    BorderBrush="{DynamicResource MaterialDesignTextFieldBoxBorder}"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Padding="12"
                                    Margin="0,0,0,16">
                                <TextBlock x:Name="OriginalTextBlock"
                                           TextWrapping="Wrap"
                                           FontSize="14"
                                           LineHeight="20"/>
                            </Border>
                        </Grid>
                    </ScrollViewer>

                    <!-- 对比模式 -->
                    <Grid x:Name="ComparisonGrid" Visibility="Collapsed">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="8"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- 原文列 -->
                        <Grid Grid.Column="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" 
                                       Text="原文"
                                       Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                       Margin="0,0,0,8"/>
                            
                            <Border Grid.Row="1"
                                    Background="{DynamicResource MaterialDesignTextFieldBoxBackground}"
                                    BorderBrush="{DynamicResource MaterialDesignTextFieldBoxBorder}"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Padding="12">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBlock x:Name="CompareOriginalText"
                                               TextWrapping="Wrap"
                                               FontSize="14"
                                               LineHeight="20"/>
                                </ScrollViewer>
                            </Border>
                        </Grid>
                        
                        <!-- 处理后文本列 -->
                        <Grid Grid.Column="2">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock x:Name="ProcessedTextLabel" 
                                       Grid.Row="0"
                                       Text="处理后文本"
                                       Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                       Margin="0,0,0,8"/>
                            
                            <Border Grid.Row="1"
                                    Background="{DynamicResource MaterialDesignCardBackground}"
                                    BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                                    BorderThickness="2"
                                    CornerRadius="4"
                                    Padding="12">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBlock x:Name="CompareProcessedText"
                                               TextWrapping="Wrap"
                                               FontSize="14"
                                               LineHeight="20"/>
                                </ScrollViewer>
                            </Border>
                        </Grid>
                    </Grid>
                </Grid>

                <!-- 处理后文本显示（普通模式） -->
                <Grid Grid.Row="2" x:Name="ProcessedTextContainer">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock x:Name="ProcessedTextTitle" 
                               Grid.Row="0"
                               Text="处理后文本："
                               Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                               Margin="0,0,0,8"/>
                    
                    <Border Grid.Row="1"
                            Background="{DynamicResource MaterialDesignCardBackground}"
                            BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                            BorderThickness="2"
                            CornerRadius="4"
                            Padding="12"
                            MinHeight="120"
                            MaxHeight="300">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <TextBlock x:Name="ProcessedTextBlock"
                                       TextWrapping="Wrap"
                                       FontSize="14"
                                       LineHeight="20"/>
                        </ScrollViewer>
                    </Border>
                </Grid>

                <!-- 操作按钮区域 -->
                <Grid Grid.Row="3" Margin="0,16,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 左侧按钮 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button x:Name="CompareButton"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="Compare_Click"
                                Content="对比模式"
                                Margin="0,0,8,0"/>

                        <Button x:Name="CopyButton"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="Copy_Click"
                                ToolTip="复制处理后的文本">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentCopy" Margin="0,0,4,0"/>
                                <TextBlock Text="复制"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                    
                    <!-- 右侧按钮 -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <Button x:Name="RejectButton"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Click="RejectButton_Click"
                                Content="取消"
                                Margin="0,0,8,0"/>

                        <Button x:Name="AcceptButton"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Click="AcceptButton_Click"
                                Content="采用"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 悬浮控件模式 -->
        <Border x:Name="FloatingControl" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                BorderThickness="1"
                CornerRadius="8"
                Padding="12"
                Visibility="Collapsed"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Margin="16"
                MaxWidth="400">
            
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- 悬浮标题 -->
                <Grid Grid.Row="0" Margin="0,0,0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock x:Name="FloatingTitleText" 
                               Grid.Column="0"
                               Text="AI处理结果"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>
                    
                    <Button x:Name="FloatingCloseButton" 
                            Grid.Column="1"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="24" Height="24"
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </Grid>
                
                <!-- 悬浮内容 -->
                <Border Grid.Row="1"
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                        BorderThickness="1"
                        CornerRadius="4"
                        Padding="8"
                        MaxHeight="200">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <TextBlock x:Name="FloatingProcessedText"
                                   TextWrapping="Wrap"
                                   FontSize="12"
                                   LineHeight="16"/>
                    </ScrollViewer>
                </Border>
                
                <!-- 悬浮按钮 -->
                <StackPanel Grid.Row="2"
                            Orientation="Horizontal"
                            HorizontalAlignment="Right"
                            Margin="0,8,0,0">
                    <Button x:Name="FloatingRejectButton"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="RejectButton_Click"
                            Content="取消"
                            Padding="8,4"
                            Margin="0,0,4,0"/>

                    <Button x:Name="FloatingAcceptButton"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="AcceptButton_Click"
                            Content="采用"
                            Padding="8,4"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>

<Window x:Class="DocumentCreationSystem.Views.CreateFolderDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:DocumentCreationSystem.Views"
        Title="创建文件夹" Height="400" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}">
    
    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="创建新文件夹"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,24"/>

        <!-- 文件夹名称输入 -->
        <TextBox Grid.Row="1"
                 x:Name="FolderNameTextBox"
                 materialDesign:HintAssist.Hint="文件夹名称"
                 materialDesign:HintAssist.IsFloating="True"
                 Margin="0,0,0,16"
                 Text="新建文件夹"/>

        <!-- 路径选择 -->
        <GroupBox Grid.Row="2" 
                  Header="选择创建位置"
                  Style="{StaticResource MaterialDesignCardGroupBox}"
                  Margin="0,0,0,16">
            <Grid Margin="16">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 当前选择的路径 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,8">
                    <TextBlock Text="当前路径：" VerticalAlignment="Center"/>
                    <TextBlock x:Name="CurrentPathTextBlock" 
                               Text="未选择"
                               VerticalAlignment="Center"
                               Margin="8,0,0,0"
                               FontWeight="Bold"/>
                    <Button x:Name="BrowseButton"
                            Content="浏览..."
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="16,0,0,0"
                            Click="BrowseButton_Click"/>
                </StackPanel>

                <!-- 文件夹树 -->
                <Border Grid.Row="1" 
                        BorderBrush="LightGray" 
                        BorderThickness="1"
                        Background="White">
                    <TreeView x:Name="FolderTreeView"
                              Margin="8"
                              SelectedItemChanged="FolderTreeView_SelectedItemChanged">
                        <TreeView.Resources>
                            <HierarchicalDataTemplate DataType="{x:Type local:FolderItem}"
                                                      ItemsSource="{Binding Children}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Folder"
                                                           Margin="0,0,4,0"
                                                           VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding Name}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </HierarchicalDataTemplate>
                        </TreeView.Resources>
                    </TreeView>
                </Border>
            </Grid>
        </GroupBox>

        <!-- 按钮 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <Button Content="取消"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="0,0,8,0"
                    Click="Cancel_Click"/>
            <Button Content="创建"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Click="Create_Click"/>
        </StackPanel>
    </Grid>
</Window>

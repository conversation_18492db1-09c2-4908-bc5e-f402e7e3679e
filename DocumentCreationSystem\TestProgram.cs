using System;
using System.Windows;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 简化的测试程序，用于诊断GUI问题
    /// </summary>
    public class TestProgram
    {
        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== 开始测试程序 ===");
                Console.WriteLine($"当前时间: {DateTime.Now}");
                Console.WriteLine($"工作目录: {Environment.CurrentDirectory}");
                Console.WriteLine($"命令行参数: {string.Join(" ", args)}");

                // 测试基本的WPF功能
                Console.WriteLine("正在创建WPF应用程序...");
                var app = new Application();
                
                Console.WriteLine("正在创建简单窗口...");
                var window = new Window
                {
                    Title = "测试窗口",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen
                };

                Console.WriteLine("正在显示窗口...");
                window.Show();

                Console.WriteLine("正在运行应用程序消息循环...");
                app.Run(window);

                Console.WriteLine("应用程序正常退出");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                
                try
                {
                    MessageBox.Show($"测试程序错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                catch (Exception msgEx)
                {
                    Console.WriteLine($"无法显示消息框: {msgEx.Message}");
                }
            }
        }
    }
}

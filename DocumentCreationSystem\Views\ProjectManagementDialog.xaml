<Window x:Class="DocumentCreationSystem.Views.ProjectManagementDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="项目管理"
        Height="600"
        Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" Padding="16" Margin="0,0,0,16">
            <DockPanel>
                <materialDesign:PackIcon Kind="FolderMultiple" DockPanel.Dock="Left"
                                       VerticalAlignment="Center" Margin="0,0,12,0" Width="32" Height="32"/>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="项目管理" 
                             Style="{StaticResource MaterialDesignHeadline5TextBlock}"/>
                    <TextBlock x:Name="ProjectCountText" 
                             Text="加载中..." 
                             Style="{StaticResource MaterialDesignBody2TextBlock}"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <!-- 操作按钮 -->
                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="RefreshButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="刷新列表"
                          Margin="0,0,8,0"
                          Click="Refresh_Click">
                        <materialDesign:PackIcon Kind="Refresh"/>
                    </Button>
                    <Button x:Name="DeleteSelectedButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="删除选中"
                          Background="{DynamicResource MaterialDesignValidationErrorBrush}"
                          Margin="0,0,8,0"
                          IsEnabled="False"
                          Click="DeleteSelected_Click"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- 项目列表 -->
        <materialDesign:Card Grid.Row="1" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 搜索栏 -->
                <Border Grid.Row="0" Background="{DynamicResource MaterialDesignCardBackground}" 
                      BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                    <Grid Margin="16,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox x:Name="SearchTextBox"
                               Grid.Column="0"
                               materialDesign:HintAssist.Hint="搜索项目名称或路径..."
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               TextChanged="Search_TextChanged"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="16,0,0,0">
                            <CheckBox x:Name="SelectAllCheckBox"
                                    Content="全选"
                                    VerticalAlignment="Center"
                                    Margin="0,0,16,0"
                                    Checked="SelectAll_Checked"
                                    Unchecked="SelectAll_Unchecked"/>
                            <TextBlock x:Name="SelectedCountText"
                                     Text="已选择: 0"
                                     VerticalAlignment="Center"
                                     Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 项目列表 -->
                <DataGrid x:Name="ProjectsDataGrid"
                        Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        CanUserReorderColumns="True"
                        CanUserResizeColumns="True"
                        CanUserSortColumns="True"
                        SelectionMode="Extended"
                        SelectionChanged="ProjectsDataGrid_SelectionChanged"
                        MouseDoubleClick="ProjectsDataGrid_MouseDoubleClick"
                        Style="{StaticResource MaterialDesignDataGrid}">
                    
                    <DataGrid.Columns>
                        <!-- 选择列 -->
                        <DataGridCheckBoxColumn Header="选择" Width="60" Binding="{Binding IsSelected}"/>
                        
                        <!-- ID列 -->
                        <DataGridTextColumn Header="ID" Width="80" Binding="{Binding Id}" IsReadOnly="True"/>
                        
                        <!-- 项目名称列 -->
                        <DataGridTextColumn Header="项目名称" Width="200" Binding="{Binding Name}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- 描述列 -->
                        <DataGridTextColumn Header="描述" Width="250" Binding="{Binding Description}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- 路径列 -->
                        <DataGridTextColumn Header="路径" Width="300" Binding="{Binding RootPath}" IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="FontFamily" Value="Consolas"/>
                                    <Setter Property="FontSize" Value="11"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                        
                        <!-- 创建时间列 -->
                        <DataGridTextColumn Header="创建时间" Width="150" Binding="{Binding CreatedAt, StringFormat=yyyy-MM-dd HH:mm}" IsReadOnly="True"/>
                        
                        <!-- 最后修改时间列 -->
                        <DataGridTextColumn Header="最后修改" Width="150" Binding="{Binding UpdatedAt, StringFormat=yyyy-MM-dd HH:mm}" IsReadOnly="True"/>
                        
                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="打开项目"
                                              Margin="2"
                                              Click="OpenProject_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16"/>
                                        </Button>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="删除项目"
                                              Margin="2"
                                              Click="DeleteProject_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16" 
                                                                   Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 底部状态栏和按钮 -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 状态信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="StatusIcon" Kind="Circle" 
                                       Width="12" Height="12" Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusText" Text="就绪" 
                         Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
            </StackPanel>

            <!-- 操作按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button x:Name="ExportButton"
                      Content="导出列表"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Margin="0,0,8,0"
                      Click="Export_Click"/>
                <Button x:Name="CleanupButton"
                      Content="清理无效项目"
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Margin="0,0,8,0"
                      Click="Cleanup_Click"/>
                <Button x:Name="CloseButton"
                      Content="关闭"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="Close_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>

# 章节细纲功能说明

## 功能概述

在一键写书过程中，系统现在会在创建章节正文之前先生成详细的章节细纲，确保创作的结构化和质量。

## 章节细纲格式

### 标准格式结构
```
## 第X章：[章节标题]

### 场景设定
- 时间：[具体时间]
- 地点：[详细地点描述]
- 环境：[环境氛围和背景]
- 道具：[重要道具和物品]

### 人物安排
- 主要人物：[本章出场的主要角色及其状态]
- 次要人物：[配角和群众角色]
- 人物关系：[角色间的关系变化]
- 人物心理：[关键角色的心理状态]

### 主线发展
- 核心事件：[本章的核心情节]
- 爽点设计：[读者期待的精彩内容]
- 冲突点：[矛盾和冲突的爆发]
- 悬念设置：[留给读者的疑问和期待]

### 预期结果
- 情节推进：[对整体故事的推进作用]
- 角色成长：[角色的变化和成长]
- 伏笔铺设：[为后续章节埋下的伏笔]
- 章节结尾：[本章的结束方式和过渡]
```

## 创作流程

### 1. 章节细纲生成
- 系统根据全书大纲、前文内容和角色信息生成详细细纲
- 细纲按照"场景 -> 人物 -> 主线/爽点/冲突点/悬念 -> 结果"的逻辑结构组织
- 自动保存细纲到项目文件夹的`Outlines/Chapters/`目录

### 2. 正文创作
- 基于生成的细纲创作章节正文
- 严格按照标准小说格式输出
- 不包含任何解释、说明、总结等非正文内容
- 确保与前文无缝衔接

### 3. 文件保存
- 章节细纲：`Outlines/Chapters/第XXX章_细纲.txt`
- 章节正文：`Chapters/书籍名称_卷宗号_卷宗名称_章节号_章节名称.txt`

## 示例章节细纲

```
## 第1章：觉醒之始

### 场景设定
- 时间：现代都市，深夜时分
- 地点：市立图书馆古籍阅览室
- 环境：昏暗安静，月光透过窗户洒在古老的书架上
- 道具：一本泛黄的古籍《修真秘录》，散发着淡淡光芒

### 人物安排
- 主要人物：林轩（大学生，22岁，对古籍有浓厚兴趣）
- 次要人物：图书馆管理员老张（即将下班）
- 人物关系：林轩独自一人研究古籍
- 人物心理：好奇中带着困惑，对古籍内容半信半疑

### 主线发展
- 核心事件：林轩意外触碰古籍，激活其中的修真传承
- 爽点设计：古籍突然发光，神秘力量涌入体内
- 冲突点：力量觉醒带来的身体剧痛和意识冲击
- 悬念设置：这股力量的真正来源和林轩的身世之谜

### 预期结果
- 情节推进：开启修真之路，为后续修炼打下基础
- 角色成长：从普通大学生转变为修真者
- 伏笔铺设：古籍中提到的"天选之人"预言
- 章节结尾：林轩昏倒，醒来时发现自己已经不同
```

## 正文创作要求

### 格式标准
1. **纯正文输出**：只输出小说正文内容，不包含任何标题、说明、总结
2. **无缝衔接**：续章时直接从前文结尾处继续，不添加过渡说明
3. **标准格式**：使用标准的小说段落格式，对话用引号

### 内容要求
1. **严格按纲**：严格按照细纲的场景、人物、情节安排创作
2. **细节丰富**：注重环境描写、人物心理、动作细节
3. **对话自然**：符合角色性格，推动情节发展
4. **节奏把控**：合理安排紧张与舒缓的节奏

### 示例正文片段
```
夜深人静，市立图书馆的古籍阅览室里只剩下林轩一个人。昏黄的台灯光线下，他正专注地翻阅着一本泛黄的古籍。

"《修真秘录》......"林轩轻声念着封面上的四个古字，眉头微皱。这本书是他在最角落的书架上发现的，书页已经发黄，散发着一股淡淡的檀香味。

"同学，图书馆要关门了。"管理员老张走过来提醒道。

"好的，张叔，我马上就走。"林轩抬头应了一声，目光却舍不得离开手中的古籍。

就在他准备合上书本的瞬间，指尖无意中触碰到了书页上的一个奇特符号。

突然，整本书爆发出耀眼的金光！
```

## 技术实现

### 细纲生成
- 使用AI服务根据上下文生成结构化细纲
- 考虑前文情节、角色发展、整体大纲的连贯性
- 确保每个章节都有明确的目标和作用

### 正文创作
- 基于细纲使用AI生成标准小说正文
- 严格控制输出格式，过滤非正文内容
- 保持与前文的风格和语调一致

### 文件管理
- 自动创建章节细纲文件夹结构
- 按章节顺序命名细纲文件
- 与正文文件分开管理，便于查阅和修改

## 优势特点

1. **结构清晰**：细纲确保每章都有明确的目标和逻辑
2. **质量保证**：先规划后创作，避免情节混乱
3. **便于修改**：细纲和正文分离，方便后期调整
4. **无缝衔接**：正文创作时严格按照标准格式，确保连贯性
5. **文件管理**：规范的文件命名和存储结构

这个功能大大提升了一键写书的专业性和实用性，让AI创作的小说更加结构化和高质量。

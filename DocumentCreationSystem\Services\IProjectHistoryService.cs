using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 项目历史记录服务接口
/// </summary>
public interface IProjectHistoryService
{
    /// <summary>
    /// 添加或更新项目历史记录
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <param name="projectName">项目名称</param>
    /// <param name="projectType">项目类型</param>
    Task AddOrUpdateHistoryAsync(string projectPath, string projectName, string projectType = "Normal");
    
    /// <summary>
    /// 获取所有历史记录
    /// </summary>
    /// <returns>历史记录列表，按最后访问时间降序排列</returns>
    Task<List<ProjectHistory>> GetAllHistoryAsync();
    
    /// <summary>
    /// 获取最近的历史记录
    /// </summary>
    /// <param name="count">获取数量</param>
    /// <returns>最近的历史记录列表</returns>
    Task<List<ProjectHistory>> GetRecentHistoryAsync(int count = 10);
    
    /// <summary>
    /// 获取收藏的项目
    /// </summary>
    /// <returns>收藏的项目列表</returns>
    Task<List<ProjectHistory>> GetFavoriteHistoryAsync();
    
    /// <summary>
    /// 设置项目收藏状态
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <param name="isFavorite">是否收藏</param>
    Task SetFavoriteAsync(string projectPath, bool isFavorite);
    
    /// <summary>
    /// 删除历史记录
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    Task RemoveHistoryAsync(string projectPath);
    
    /// <summary>
    /// 清空所有历史记录
    /// </summary>
    Task ClearAllHistoryAsync();
    
    /// <summary>
    /// 检查项目路径是否存在
    /// </summary>
    /// <param name="projectPath">项目路径</param>
    /// <returns>是否存在</returns>
    bool IsProjectPathValid(string projectPath);

    /// <summary>
    /// 清理无效的历史记录（路径不存在的记录）
    /// </summary>
    /// <returns>清理的记录数量</returns>
    Task<int> CleanupInvalidHistoryAsync();

    /// <summary>
    /// 获取所有无效的历史记录
    /// </summary>
    /// <returns>无效的历史记录列表</returns>
    Task<List<ProjectHistory>> GetInvalidHistoryAsync();

    /// <summary>
    /// 清理重复的历史记录（相同路径的记录）
    /// </summary>
    /// <returns>清理的记录数量</returns>
    Task<int> CleanupDuplicateHistoryAsync();

    /// <summary>
    /// 获取所有重复的历史记录
    /// </summary>
    /// <returns>重复的历史记录列表</returns>
    Task<List<ProjectHistory>> GetDuplicateHistoryAsync();
}

# Augment Agent 可用工具清单

## 1. 文件编辑工具
- **str-replace-editor**: 文件编辑工具，支持字符串替换和插入操作
  - 支持精确的行号定位和多处替换
  - 用于编辑现有文件，不能创建新文件

- **save-file**: 新文件创建工具
  - 用于创建新文件并写入内容
  - 限制文件内容最多300行

- **remove-files**: 文件删除工具
  - 安全删除用户工作区中的文件
  - 支持批量删除多个文件

## 2. 文件查看工具
- **view**: 文件和目录查看工具
  - 支持查看文件内容和目录结构
  - 支持正则表达式搜索
  - 支持指定行范围查看
  - 支持上下文行数控制

- **view-range-untruncated**: 查看截断内容的指定行范围
- **search-untruncated**: 在截断内容中搜索

## 3. 代码库检索工具
- **codebase-retrieval**: Augment的世界领先代码库上下文引擎
  - 基于自然语言描述检索相关代码片段
  - 实时索引，反映代码库当前状态
  - 支持跨编程语言检索

## 4. 进程管理工具
- **launch-process**: 启动新进程
  - 支持等待和非等待模式
  - 使用PowerShell作为默认shell
  - 支持交互式终端

- **read-process**: 读取进程输出
- **write-process**: 向进程写入输入
- **kill-process**: 终止进程
- **list-processes**: 列出所有已知进程
- **read-terminal**: 读取VSCode终端输出

## 5. 网络工具
- **web-search**: 网络搜索工具
  - 使用Google自定义搜索API
  - 返回Markdown格式结果
  - 支持指定结果数量

- **web-fetch**: 网页内容获取工具
  - 获取网页内容并转换为Markdown格式

- **open-browser**: 浏览器打开工具
  - 在默认浏览器中打开URL

## 6. GitHub集成工具
- **github-api**: GitHub API调用工具
  - 支持GET、POST、PATCH、PUT方法
  - 自动处理查询参数和JSON请求体
  - 支持issues、PRs、commits等操作

## 7. 诊断工具
- **diagnostics**: IDE问题诊断工具
  - 获取指定文件的错误、警告等问题

## 8. 任务管理工具
- **view_tasklist**: 查看当前任务列表
- **add_tasks**: 添加新任务
  - 支持创建子任务和指定任务位置
  - 支持批量添加多个任务

- **update_tasks**: 更新任务属性
  - 支持更新任务状态、名称、描述
  - 支持批量更新多个任务

- **reorganize_tasklist**: 重组任务列表结构
  - 用于复杂的任务列表重构

## 9. 记忆工具
- **remember**: 长期记忆存储工具
  - 存储可在长期使用的重要信息

## 10. 图表工具
- **render-mermaid**: Mermaid图表渲染工具
  - 渲染交互式图表
  - 支持平移、缩放和复制功能

## 工具使用说明

### 任务状态说明
- `[ ]` = 未开始 (NOT_STARTED)
- `[/]` = 进行中 (IN_PROGRESS)
- `[-]` = 已取消 (CANCELLED)
- `[x]` = 已完成 (COMPLETE)

### 文件编辑最佳实践
1. 编辑前先使用codebase-retrieval获取详细信息
2. 使用str-replace-editor进行精确编辑
3. 避免手动编辑包管理文件，使用包管理器命令

### 代码显示格式
显示代码时使用XML标签格式：
```xml
<augment_code_snippet path="文件路径" mode="EXCERPT">
````语言
代码内容
````
</augment_code_snippet>
```

### 包管理器使用
- **JavaScript/Node.js**: npm, yarn, pnpm
- **Python**: pip, poetry, conda
- **Rust**: cargo
- **Go**: go get, go mod
- **其他语言**: 使用相应的包管理器

> 注意：始终使用包管理器而非手动编辑配置文件来管理依赖项
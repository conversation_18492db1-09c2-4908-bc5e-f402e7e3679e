using System;
using System.Collections.Generic;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 专利交底书生成请求
    /// </summary>
    public class PatentDisclosureRequest
    {
        /// <summary>
        /// 发明名称（可为空，由AI生成）
        /// </summary>
        public string? InventionTitle { get; set; }

        /// <summary>
        /// 专利类型
        /// </summary>
        public string PatentType { get; set; } = string.Empty;

        /// <summary>
        /// 技术领域
        /// </summary>
        public string TechnicalField { get; set; } = string.Empty;

        /// <summary>
        /// 申请人
        /// </summary>
        public string Applicant { get; set; } = string.Empty;

        /// <summary>
        /// 发明人
        /// </summary>
        public string Inventor { get; set; } = string.Empty;

        /// <summary>
        /// 数学公式复杂度 (1-5)
        /// </summary>
        public int FormulaComplexity { get; set; } = 3;

        /// <summary>
        /// 技术深度 (0.1-1.0)
        /// </summary>
        public float TechnicalDepth { get; set; } = 0.7f;

        /// <summary>
        /// 创新性强调 (0.1-1.0)
        /// </summary>
        public float InnovationEmphasis { get; set; } = 0.8f;

        /// <summary>
        /// 技术素材文件路径列表
        /// </summary>
        public List<string> MaterialFiles { get; set; } = new();

        /// <summary>
        /// 专利结构设置
        /// </summary>
        public PatentStructure Structure { get; set; } = new();

        /// <summary>
        /// 数学公式设置
        /// </summary>
        public FormulaSettings FormulaSettings { get; set; } = new();

        /// <summary>
        /// 输出格式
        /// </summary>
        public string OutputFormat { get; set; } = "docx";

        /// <summary>
        /// 是否保存到项目文件夹
        /// </summary>
        public bool SaveToProject { get; set; } = true;

        /// <summary>
        /// 是否生成附图占位符
        /// </summary>
        public bool GenerateDrawingPlaceholders { get; set; } = true;

        /// <summary>
        /// 用户附加要求
        /// </summary>
        public string? UserRequirements { get; set; }
    }

    /// <summary>
    /// 专利结构设置
    /// </summary>
    public class PatentStructure
    {
        /// <summary>
        /// 包含技术领域
        /// </summary>
        public bool IncludeTechnicalField { get; set; } = true;

        /// <summary>
        /// 包含背景技术
        /// </summary>
        public bool IncludeBackground { get; set; } = true;

        /// <summary>
        /// 包含发明内容
        /// </summary>
        public bool IncludeSummary { get; set; } = true;

        /// <summary>
        /// 包含附图说明
        /// </summary>
        public bool IncludeDrawings { get; set; } = true;

        /// <summary>
        /// 包含具体实施方式
        /// </summary>
        public bool IncludeDetailedDescription { get; set; } = true;

        /// <summary>
        /// 包含权利要求书
        /// </summary>
        public bool IncludeClaims { get; set; } = true;

        /// <summary>
        /// 包含摘要
        /// </summary>
        public bool IncludeAbstract { get; set; } = true;
    }

    /// <summary>
    /// 数学公式设置
    /// </summary>
    public class FormulaSettings
    {
        /// <summary>
        /// 包含算法公式
        /// </summary>
        public bool IncludeAlgorithmFormulas { get; set; } = true;

        /// <summary>
        /// 包含性能计算公式
        /// </summary>
        public bool IncludePerformanceFormulas { get; set; } = true;

        /// <summary>
        /// 包含优化公式
        /// </summary>
        public bool IncludeOptimizationFormulas { get; set; } = true;

        /// <summary>
        /// 包含统计分析公式
        /// </summary>
        public bool IncludeStatisticalFormulas { get; set; } = false;

        /// <summary>
        /// 包含数学模型公式
        /// </summary>
        public bool IncludeModelFormulas { get; set; } = true;
    }

    /// <summary>
    /// 专利交底书生成结果
    /// </summary>
    public class PatentDisclosureResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 发明名称
        /// </summary>
        public string? InventionTitle { get; set; }

        /// <summary>
        /// 专利交底书内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 生成的章节列表
        /// </summary>
        public List<PatentSection> Sections { get; set; } = new();

        /// <summary>
        /// 生成的数学公式列表
        /// </summary>
        public List<MathematicalFormula> Formulas { get; set; } = new();

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 生成统计信息
        /// </summary>
        public PatentGenerationStatistics Statistics { get; set; } = new();
    }

    /// <summary>
    /// 专利章节
    /// </summary>
    public class PatentSection
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 字数
        /// </summary>
        public int WordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 包含的数学公式数量
        /// </summary>
        public int FormulaCount { get; set; }
    }

    /// <summary>
    /// 数学公式
    /// </summary>
    public class MathematicalFormula
    {
        /// <summary>
        /// 公式ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 公式名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 公式表达式
        /// </summary>
        public string Expression { get; set; } = string.Empty;

        /// <summary>
        /// 公式类型
        /// </summary>
        public string FormulaType { get; set; } = string.Empty;

        /// <summary>
        /// 公式说明
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 变量定义
        /// </summary>
        public List<FormulaVariable> Variables { get; set; } = new();

        /// <summary>
        /// 应用场景
        /// </summary>
        public string ApplicationContext { get; set; } = string.Empty;

        /// <summary>
        /// 复杂度等级 (1-5)
        /// </summary>
        public int ComplexityLevel { get; set; } = 1;
    }

    /// <summary>
    /// 公式变量
    /// </summary>
    public class FormulaVariable
    {
        /// <summary>
        /// 变量符号
        /// </summary>
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// 变量名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 变量描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 变量单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 取值范围
        /// </summary>
        public string Range { get; set; } = string.Empty;
    }

    /// <summary>
    /// 专利生成统计信息
    /// </summary>
    public class PatentGenerationStatistics
    {
        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount { get; set; }

        /// <summary>
        /// 生成时间
        /// </summary>
        public TimeSpan GenerationTime { get; set; }

        /// <summary>
        /// 使用的技术素材文件数量
        /// </summary>
        public int MaterialFilesCount { get; set; }

        /// <summary>
        /// 生成的章节数量
        /// </summary>
        public int SectionsCount { get; set; }

        /// <summary>
        /// 生成的数学公式数量
        /// </summary>
        public int FormulasCount { get; set; }

        /// <summary>
        /// 技术创新点数量
        /// </summary>
        public int InnovationPointsCount { get; set; }

        /// <summary>
        /// 素材利用率
        /// </summary>
        public float MaterialUtilizationRate { get; set; }
    }

    /// <summary>
    /// 技术分析结果
    /// </summary>
    public class TechnicalAnalysisResult
    {
        /// <summary>
        /// 核心技术点
        /// </summary>
        public List<string> CoreTechnologies { get; set; } = new();

        /// <summary>
        /// 创新点
        /// </summary>
        public List<string> InnovationPoints { get; set; } = new();

        /// <summary>
        /// 技术优势
        /// </summary>
        public List<string> TechnicalAdvantages { get; set; } = new();

        /// <summary>
        /// 应用场景
        /// </summary>
        public List<string> ApplicationScenarios { get; set; } = new();

        /// <summary>
        /// 技术难点
        /// </summary>
        public List<string> TechnicalChallenges { get; set; } = new();

        /// <summary>
        /// 解决方案
        /// </summary>
        public List<string> Solutions { get; set; } = new();

        /// <summary>
        /// 技术摘要
        /// </summary>
        public string TechnicalSummary { get; set; } = string.Empty;

        /// <summary>
        /// 建议的权利要求
        /// </summary>
        public List<string> SuggestedClaims { get; set; } = new();
    }

    /// <summary>
    /// 专利大纲
    /// </summary>
    public class PatentOutline
    {
        /// <summary>
        /// 发明名称
        /// </summary>
        public string InventionTitle { get; set; } = string.Empty;

        /// <summary>
        /// 技术领域描述
        /// </summary>
        public string TechnicalFieldDescription { get; set; } = string.Empty;

        /// <summary>
        /// 章节大纲列表
        /// </summary>
        public List<PatentSectionOutline> SectionOutlines { get; set; } = new();

        /// <summary>
        /// 预估字数分配
        /// </summary>
        public Dictionary<string, int> WordCountAllocation { get; set; } = new();

        /// <summary>
        /// 数学公式分配计划
        /// </summary>
        public Dictionary<string, int> FormulaAllocation { get; set; } = new();
    }

    /// <summary>
    /// 专利章节大纲
    /// </summary>
    public class PatentSectionOutline
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 章节要点
        /// </summary>
        public List<string> KeyPoints { get; set; } = new();

        /// <summary>
        /// 预估字数
        /// </summary>
        public int EstimatedWordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 相关技术素材引用
        /// </summary>
        public List<string> RelatedMaterials { get; set; } = new();

        /// <summary>
        /// 计划包含的数学公式类型
        /// </summary>
        public List<string> PlannedFormulaTypes { get; set; } = new();
    }
}

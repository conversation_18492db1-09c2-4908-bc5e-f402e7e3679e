# 数据库清理工具使用说明

## 概述

数据库清理工具是文档管理及创作系统的重要维护功能，用于清理数据库中的无效项目ID引用和孤立数据，确保数据完整性和系统稳定性。

## 功能特性

### 1. 数据完整性检查
- **孤立数据检测**：检测引用不存在项目ID的文档、小说项目、章节、角色等数据
- **重复ID检测**：检测数据库中是否存在重复的ID
- **引用完整性验证**：验证所有外键引用的有效性

### 2. 自动数据清理
- **孤立文档清理**：删除引用无效项目ID的文档记录
- **孤立小说项目清理**：删除引用无效项目ID的小说项目记录
- **孤立章节清理**：删除引用无效小说项目ID的章节记录
- **孤立角色清理**：删除引用无效小说项目ID的角色记录
- **孤立向量记录清理**：删除引用无效文档ID的向量记录
- **孤立世界设定清理**：删除引用无效小说项目ID的世界设定记录

### 3. 数据备份管理
- **自动备份**：清理前自动创建数据备份
- **手动备份**：支持随时创建数据备份
- **备份查看**：查看所有历史备份文件
- **旧备份清理**：自动清理30天前的旧备份文件

## 使用方法

### 打开清理工具
1. 在主窗口菜单栏中选择 **工具(T)** → **数据库清理**
2. 清理工具对话框将打开

### 数据完整性检查
1. 点击 **检查数据完整性** 按钮
2. 系统将扫描所有数据表，检查引用完整性
3. 检查结果将显示在界面上：
   - ✅ 绿色：数据完整性良好，无问题
   - ⚠️ 橙色：发现问题，显示具体问题数量和类型
   - ❌ 红色：检查失败，显示错误信息

### 执行数据清理
1. **配置清理选项**：
   - 勾选需要清理的数据类型
   - 建议保持 **清理前创建备份** 选项开启
   
2. **开始清理**：
   - 点击 **开始清理** 按钮
   - 确认清理操作（此操作不可逆）
   - 等待清理完成

3. **查看清理结果**：
   - 清理完成后，界面将显示详细的清理结果
   - 包括清理的数据数量和类型

### 备份管理
1. **创建备份**：
   - 点击 **创建备份** 按钮
   - 系统将创建当前数据的完整备份
   
2. **查看备份**：
   - 点击 **查看备份** 按钮
   - 显示所有历史备份文件的列表
   
3. **清理旧备份**：
   - 点击 **清理旧备份** 按钮
   - 删除30天前的旧备份文件

## 常见问题

### Q: 什么时候需要使用数据清理工具？
A: 以下情况建议使用：
- 系统出现"项目不存在"等错误
- 删除项目后发现相关数据仍然存在
- 数据导入/导出后出现数据不一致
- 定期维护（建议每月执行一次）

### Q: 清理操作是否安全？
A: 是的，清理工具具有以下安全保障：
- 清理前自动创建数据备份
- 只删除确认无效的引用数据
- 不会删除有效的项目和文档数据
- 提供详细的操作日志

### Q: 如果清理后出现问题怎么办？
A: 可以通过以下方式恢复：
- 查看备份文件列表，找到清理前的备份
- 手动恢复备份文件到数据目录
- 重启应用程序加载恢复的数据

### Q: 备份文件存储在哪里？
A: 备份文件存储在：
```
%APPDATA%\DocumentCreationSystem\Backups\
```

### Q: 清理工具会影响正在编辑的文档吗？
A: 不会。清理工具只处理数据库中的元数据记录，不会影响：
- 正在编辑的文档内容
- 项目文件夹中的实际文件
- 用户的工作进度

## 技术细节

### 数据存储位置
系统数据存储在以下JSON文件中：
- `projects.json` - 项目信息
- `documents.json` - 文档信息
- `novel_projects.json` - 小说项目信息
- `chapters.json` - 章节信息
- `characters.json` - 角色信息
- `vector_records.json` - 向量记录
- `world_settings.json` - 世界设定

### 清理逻辑
1. **获取有效项目ID集合**：从projects表中获取所有状态不为"Deleted"的项目ID
2. **检查外键引用**：检查所有表中的ProjectId字段是否引用有效项目
3. **级联清理**：按依赖关系顺序清理无效数据
4. **保存更改**：将清理后的数据保存到文件

### 备份格式
备份文件为ZIP格式，包含：
- 所有JSON数据文件
- `backup_info.json` - 备份元信息

## 注意事项

1. **定期备份**：建议在重要操作前手动创建备份
2. **谨慎操作**：清理操作不可逆，请确认后再执行
3. **关闭其他功能**：清理时建议暂停其他数据操作
4. **检查日志**：清理后查看应用程序日志确认操作结果
5. **测试环境**：重要系统建议先在测试环境验证清理效果

## 更新日志

### v1.0.0 (2024-07-14)
- 初始版本发布
- 支持基本的数据完整性检查
- 支持自动数据清理
- 支持备份管理功能
- 提供图形化操作界面

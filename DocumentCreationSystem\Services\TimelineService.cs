using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Models;
using System.Text.Json;
using System.IO;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 时间线管理服务
    /// </summary>
    public class TimelineService
    {
        private readonly ILogger<TimelineService> _logger;
        private readonly IDataStorageService _dataStorage;
        private readonly IAIService _aiService;

        public TimelineService(
            ILogger<TimelineService> logger,
            IDataStorageService dataStorage,
            IAIService aiService)
        {
            _logger = logger;
            _dataStorage = dataStorage;
            _aiService = aiService;
        }

        /// <summary>
        /// 生成章节时间线总结
        /// </summary>
        public async Task<ChapterSummaryTimeline> GenerateChapterTimelineSummaryAsync(
            int projectId, int chapterNumber, string chapterTitle, string chapterContent)
        {
            try
            {
                _logger.LogInformation($"开始生成第{chapterNumber}章时间线总结");

                // 获取项目信息
                var project = await _dataStorage.GetProjectAsync(projectId);
                if (project == null)
                {
                    throw new ArgumentException($"项目不存在: {projectId}");
                }

                // 获取前一章的总结作为上下文
                var previousSummary = await GetPreviousChapterSummaryAsync(projectId, chapterNumber - 1);
                var previousContext = previousSummary != null 
                    ? $"前一章状态：\n{FormatPreviousSummary(previousSummary)}\n\n"
                    : "";

                var prompt = $@"请分析以下章节内容，生成详细的时间线总结：

{previousContext}章节信息：
- 标题：{chapterTitle}
- 章节号：第{chapterNumber}章

章节内容：
{chapterContent}

请按照以下格式生成时间线总结：

### 主要事件总结
[列出本章发生的主要事件，按时间顺序]

### 角色变化
[详细描述主要角色的状态变化、实力提升、关系变化等]

### 势力变化
[描述各势力的状态变化、实力对比、关系变化等]

### 世界状态变化
[描述世界观、环境、规则等方面的变化]

### 重要伏笔
[记录本章埋下的伏笔和线索]

### 故事时间跨度
[本章覆盖的故事内时间]

请确保总结准确、详细，便于后续章节创作时参考。";

                var summaryText = await _aiService.GenerateTextAsync(prompt, 2000, 0.7f);
                
                // 解析AI生成的总结
                var summary = ParseTimelineSummary(summaryText, projectId, chapterNumber, chapterTitle);
                
                // 保存到数据库
                await SaveChapterSummaryAsync(summary);
                
                // 更新角色和势力时间线
                await UpdateCharacterTimelinesAsync(projectId, chapterNumber, summary);
                await UpdateFactionTimelinesAsync(projectId, chapterNumber, summary);

                _logger.LogInformation($"第{chapterNumber}章时间线总结生成完成");
                return summary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成第{chapterNumber}章时间线总结失败");
                throw;
            }
        }

        /// <summary>
        /// 解析时间线总结文本
        /// </summary>
        private ChapterSummaryTimeline ParseTimelineSummary(string summaryText, int projectId, int chapterNumber, string chapterTitle)
        {
            var summary = new ChapterSummaryTimeline
            {
                ProjectId = projectId,
                ChapterNumber = chapterNumber,
                ChapterTitle = chapterTitle
            };

            var sections = summaryText.Split(new[] { "###" }, StringSplitOptions.RemoveEmptyEntries);
            
            foreach (var section in sections)
            {
                var lines = section.Trim().Split('\n');
                if (lines.Length == 0) continue;

                var sectionTitle = lines[0].Trim();
                var content = string.Join("\n", lines.Skip(1)).Trim();

                switch (sectionTitle)
                {
                    case "主要事件总结":
                        summary.MainEvents = content;
                        break;
                    case "角色变化":
                        summary.CharacterChanges = content;
                        break;
                    case "势力变化":
                        summary.FactionChanges = content;
                        break;
                    case "世界状态变化":
                        summary.WorldChanges = content;
                        break;
                    case "重要伏笔":
                        summary.ImportantForeshadowing = content;
                        break;
                    case "故事时间跨度":
                        summary.TimeSpan = content;
                        break;
                }
            }

            return summary;
        }

        /// <summary>
        /// 获取前一章总结
        /// </summary>
        public async Task<ChapterSummaryTimeline?> GetPreviousChapterSummaryAsync(int projectId, int chapterNumber)
        {
            try
            {
                // 这里需要实现数据库查询逻辑
                // 暂时返回null，后续实现数据存储时补充
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"获取前一章总结失败: 项目{projectId}, 章节{chapterNumber}");
                return null;
            }
        }

        /// <summary>
        /// 格式化前一章总结
        /// </summary>
        private string FormatPreviousSummary(ChapterSummaryTimeline summary)
        {
            return $@"第{summary.ChapterNumber}章结束时状态：
主要事件：{summary.MainEvents}
角色状态：{summary.CharacterChanges}
势力状态：{summary.FactionChanges}
世界状态：{summary.WorldChanges}";
        }

        /// <summary>
        /// 保存章节总结
        /// </summary>
        private async Task SaveChapterSummaryAsync(ChapterSummaryTimeline summary)
        {
            try
            {
                // 保存到项目文件夹
                var project = await _dataStorage.GetProjectAsync(summary.ProjectId);
                if (project != null)
                {
                    var timelineDir = Path.Combine(project.RootPath, "Timeline");
                    Directory.CreateDirectory(timelineDir);

                    var summaryFile = Path.Combine(timelineDir, $"第{summary.ChapterNumber:D3}章_时间线总结.txt");
                    var content = FormatSummaryForFile(summary);
                    await File.WriteAllTextAsync(summaryFile, content, System.Text.Encoding.UTF8);
                }

                // 这里可以添加数据库保存逻辑
                _logger.LogInformation($"章节时间线总结已保存: 第{summary.ChapterNumber}章");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"保存章节总结失败: 第{summary.ChapterNumber}章");
            }
        }

        /// <summary>
        /// 格式化总结用于文件保存
        /// </summary>
        private string FormatSummaryForFile(ChapterSummaryTimeline summary)
        {
            return $@"# 第{summary.ChapterNumber}章时间线总结

## 章节信息
- 标题：{summary.ChapterTitle}
- 生成时间：{summary.CreatedAt:yyyy-MM-dd HH:mm:ss}

## 主要事件总结
{summary.MainEvents}

## 角色变化
{summary.CharacterChanges}

## 势力变化
{summary.FactionChanges}

## 世界状态变化
{summary.WorldChanges}

## 重要伏笔
{summary.ImportantForeshadowing ?? "无"}

## 故事时间跨度
{summary.TimeSpan ?? "未指定"}
";
        }

        /// <summary>
        /// 更新角色时间线
        /// </summary>
        private async Task UpdateCharacterTimelinesAsync(int projectId, int chapterNumber, ChapterSummaryTimeline summary)
        {
            try
            {
                // 解析角色变化信息，更新角色时间线
                // 这里需要实现具体的角色状态更新逻辑
                _logger.LogInformation($"角色时间线更新完成: 第{chapterNumber}章");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新角色时间线失败: 第{chapterNumber}章");
            }
        }

        /// <summary>
        /// 更新势力时间线
        /// </summary>
        private async Task UpdateFactionTimelinesAsync(int projectId, int chapterNumber, ChapterSummaryTimeline summary)
        {
            try
            {
                // 解析势力变化信息，更新势力时间线
                // 这里需要实现具体的势力状态更新逻辑
                _logger.LogInformation($"势力时间线更新完成: 第{chapterNumber}章");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新势力时间线失败: 第{chapterNumber}章");
            }
        }

        /// <summary>
        /// 获取最后三章的时间线内容
        /// </summary>
        public async Task<string> GetLastThreeChaptersTimelineAsync(int projectId, int currentChapter)
        {
            try
            {
                var project = await _dataStorage.GetProjectAsync(projectId);
                if (project == null)
                {
                    return "";
                }

                var timelineDir = Path.Combine(project.RootPath, "设定");
                var timelineFile = Path.Combine(timelineDir, "时间线管理.md");

                if (!File.Exists(timelineFile))
                {
                    return "";
                }

                var timelineContent = await File.ReadAllTextAsync(timelineFile);
                var lastThreeChapters = new List<string>();

                // 获取前三章的时间线内容
                for (int i = Math.Max(1, currentChapter - 2); i < currentChapter; i++)
                {
                    var chapterTimeline = ExtractChapterTimelineFromContent(timelineContent, i);
                    if (!string.IsNullOrEmpty(chapterTimeline))
                    {
                        lastThreeChapters.Add($"## 第{i}章时间线\n{chapterTimeline}");
                    }
                }

                return string.Join("\n\n", lastThreeChapters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取最后三章时间线失败: 项目{projectId}, 当前章节{currentChapter}");
                return "";
            }
        }

        /// <summary>
        /// 从时间线内容中提取指定章节的时间线
        /// </summary>
        private string ExtractChapterTimelineFromContent(string timelineContent, int chapterNumber)
        {
            try
            {
                var lines = timelineContent.Split('\n');
                var chapterLines = new List<string>();
                bool inTargetChapter = false;

                foreach (var line in lines)
                {
                    // 检查是否是目标章节的开始
                    if (line.StartsWith($"## 第{chapterNumber}章时间线"))
                    {
                        inTargetChapter = true;
                        continue; // 跳过标题行
                    }
                    // 检查是否遇到下一个章节
                    else if (line.StartsWith("## 第") && line.Contains("章时间线") && inTargetChapter)
                    {
                        break; // 结束当前章节的提取
                    }
                    // 如果在目标章节内，收集内容
                    else if (inTargetChapter)
                    {
                        chapterLines.Add(line);
                    }
                }

                return string.Join("\n", chapterLines).Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提取第{chapterNumber}章时间线失败");
                return "";
            }
        }

        /// <summary>
        /// 更新章节时间线
        /// </summary>
        public async Task UpdateChapterTimelineAsync(int projectId, int chapterNumber, string chapterContent, string chapterTitle = "")
        {
            try
            {
                _logger.LogInformation($"开始更新第{chapterNumber}章时间线");

                var project = await _dataStorage.GetProjectAsync(projectId);
                if (project == null)
                {
                    _logger.LogWarning($"项目不存在: {projectId}");
                    return;
                }

                // 生成章节时间线摘要
                var timelineSummary = await GenerateChapterTimelineSummaryForUpdateAsync(chapterNumber, chapterContent, chapterTitle);

                // 更新时间线文件
                var timelineDir = Path.Combine(project.RootPath, "设定");
                Directory.CreateDirectory(timelineDir);
                var timelineFile = Path.Combine(timelineDir, "时间线管理.md");

                // 读取现有时间线
                string existingTimeline = "";
                if (File.Exists(timelineFile))
                {
                    existingTimeline = await File.ReadAllTextAsync(timelineFile);
                }

                // 更新时间线内容
                var updatedTimeline = UpdateTimelineContent(existingTimeline, chapterNumber, timelineSummary);

                // 保存更新后的时间线
                await File.WriteAllTextAsync(timelineFile, updatedTimeline);

                _logger.LogInformation($"第{chapterNumber}章时间线更新完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新第{chapterNumber}章时间线失败");
            }
        }

        /// <summary>
        /// 生成章节时间线摘要（用于更新）
        /// </summary>
        private async Task<string> GenerateChapterTimelineSummaryForUpdateAsync(int chapterNumber, string chapterContent, string chapterTitle)
        {
            try
            {
                var prompt = $@"请基于以下第{chapterNumber}章的内容，生成简洁的时间线摘要：

=== 章节信息 ===
标题：{chapterTitle}
章节号：第{chapterNumber}章

=== 章节内容 ===
{chapterContent.Substring(0, Math.Min(chapterContent.Length, 3000))}...

=== 要求 ===
请提取本章的关键事件和时间线信息，格式如下：

**时间**：[具体时间或时间段]
**地点**：[主要场景]
**主要事件**：[核心情节，按时间顺序列出]
**角色状态变化**：[主要角色的重要变化]
**势力动态**：[势力关系的变化]
**重要结果**：[对后续剧情的影响]
**伏笔线索**：[本章埋下的伏笔]

请确保摘要简洁明了，便于后续章节创作时参考。";

                return await _aiService.GenerateTextAsync(prompt, 800, 0.7f);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成第{chapterNumber}章时间线摘要失败");
                return $"第{chapterNumber}章 - 时间线摘要生成失败";
            }
        }

        /// <summary>
        /// 更新时间线内容
        /// </summary>
        private string UpdateTimelineContent(string existingTimeline, int chapterNumber, string chapterSummary)
        {
            var lines = existingTimeline.Split('\n').ToList();

            // 查找是否已存在该章节的时间线
            var chapterLineIndex = lines.FindIndex(line => line.Contains($"第{chapterNumber}章时间线"));

            if (chapterLineIndex >= 0)
            {
                // 更新现有章节时间线
                lines[chapterLineIndex] = $"## 第{chapterNumber}章时间线";

                // 删除旧的章节内容
                var nextChapterIndex = lines.FindIndex(chapterLineIndex + 1, line => line.StartsWith("## 第"));
                if (nextChapterIndex > 0)
                {
                    lines.RemoveRange(chapterLineIndex + 1, nextChapterIndex - chapterLineIndex - 1);
                }
                else
                {
                    // 如果没有下一章，删除到文件末尾
                    if (chapterLineIndex + 1 < lines.Count)
                    {
                        lines.RemoveRange(chapterLineIndex + 1, lines.Count - chapterLineIndex - 1);
                    }
                }

                // 插入新的章节内容
                lines.Insert(chapterLineIndex + 1, chapterSummary);
                lines.Insert(chapterLineIndex + 2, "");
            }
            else
            {
                // 添加新的章节时间线
                if (string.IsNullOrEmpty(existingTimeline))
                {
                    lines.Add("# 故事时间线");
                    lines.Add("");
                }

                lines.Add($"## 第{chapterNumber}章时间线");
                lines.Add(chapterSummary);
                lines.Add("");
            }

            return string.Join("\n", lines);
        }

        /// <summary>
        /// 获取项目时间线概览
        /// </summary>
        public async Task<string> GetTimelineOverviewAsync(int projectId)
        {
            try
            {
                var project = await _dataStorage.GetProjectAsync(projectId);
                if (project == null)
                {
                    return "项目不存在";
                }

                var timelineDir = Path.Combine(project.RootPath, "Timeline");
                if (!Directory.Exists(timelineDir))
                {
                    return "暂无时间线记录";
                }

                var summaryFiles = Directory.GetFiles(timelineDir, "*时间线总结.txt")
                    .OrderBy(f => f)
                    .ToList();

                if (!summaryFiles.Any())
                {
                    return "暂无时间线记录";
                }

                var overview = "# 项目时间线概览\n\n";
                foreach (var file in summaryFiles)
                {
                    var fileName = Path.GetFileNameWithoutExtension(file);
                    overview += $"- {fileName}\n";
                }

                return overview;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取时间线概览失败: 项目{projectId}");
                return "获取时间线概览失败";
            }
        }
    }
}

<Window x:Class="DocumentCreationSystem.Views.AgentConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Agent配置管理" Height="600" Width="900"
        WindowStartupLocation="CenterOwner">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="Agent配置管理"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              Margin="0,0,0,8"/>
                    <TextBlock Text="配置和管理AI Agent的各项功能和参数"
                              Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="保存配置"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="SaveConfig_Click">
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <TabControl Grid.Row="1" Margin="8">

            <!-- 基本配置 -->
            <TabItem Header="基本配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <GroupBox Header="Agent基本信息" Margin="0,0,0,16">
                            <StackPanel Margin="8">
                                <TextBox x:Name="AgentNameTextBox"
                                        materialDesign:HintAssist.Hint="Agent名称"
                                        Margin="0,0,0,16"/>

                                <TextBox x:Name="AgentDescriptionTextBox"
                                        materialDesign:HintAssist.Hint="Agent描述"
                                        AcceptsReturn="True"
                                        MinLines="3"
                                        Margin="0,0,0,16"/>

                                <ComboBox x:Name="AgentTypeComboBox"
                                         materialDesign:HintAssist.Hint="Agent类型"
                                         Margin="0,0,0,16">
                                    <ComboBoxItem Content="通用助手"/>
                                    <ComboBoxItem Content="文档创建专家"/>
                                    <ComboBoxItem Content="代码助手"/>
                                    <ComboBoxItem Content="研究助手"/>
                                    <ComboBoxItem Content="自定义"/>
                                </ComboBox>

                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <TextBlock Text="置信度阈值:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider x:Name="ConfidenceThresholdSlider"
                                           Minimum="0" Maximum="1" Value="0.7"
                                           Width="200"
                                           TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=ConfidenceThresholdSlider, Path=Value, StringFormat={}{0:P0}}"
                                              VerticalAlignment="Center" Margin="8,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>

                        <GroupBox Header="AI模型配置" Margin="0,0,0,16">
                            <StackPanel Margin="8">
                                <!-- 当前配置的AI模型信息显示 -->
                                <Border Background="{DynamicResource MaterialDesignCardBackground}"
                                       CornerRadius="4" Padding="12" Margin="0,0,0,16">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 提供商信息 -->
                                        <TextBlock Grid.Row="0" Grid.Column="0" Text="当前提供商:"
                                                  FontWeight="Medium" Margin="0,0,12,8"/>
                                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CurrentProviderText"
                                                  Text="未配置" Margin="0,0,0,8"/>

                                        <!-- 模型信息 -->
                                        <TextBlock Grid.Row="1" Grid.Column="0" Text="当前模型:"
                                                  FontWeight="Medium" Margin="0,0,12,8"/>
                                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="CurrentModelText"
                                                  Text="未配置" Margin="0,0,0,8"/>

                                        <!-- 状态信息 -->
                                        <TextBlock Grid.Row="2" Grid.Column="0" Text="连接状态:"
                                                  FontWeight="Medium" Margin="0,0,12,8"/>
                                        <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,8">
                                            <Ellipse x:Name="StatusIndicator" Width="8" Height="8"
                                                    Fill="Gray" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                            <TextBlock x:Name="CurrentStatusText" Text="未知"/>
                                        </StackPanel>

                                        <!-- 配置按钮 -->
                                        <Button Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"
                                               x:Name="OpenAIConfigButton"
                                               Content="打开AI模型配置"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               Click="OpenAIConfig_Click"
                                               HorizontalAlignment="Left"
                                               Margin="0,8,0,0"/>
                                    </Grid>
                                </Border>

                                <!-- 温度参数设置 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                                    <TextBlock Text="温度参数:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <Slider x:Name="TemperatureSlider"
                                           Minimum="0" Maximum="2" Value="0.7"
                                           Width="200"
                                           TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                    <TextBlock Text="{Binding ElementName=TemperatureSlider, Path=Value, StringFormat={}{0:F1}}"
                                              VerticalAlignment="Center" Margin="8,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 工具配置 -->
            <TabItem Header="工具配置">
                <Grid Margin="16">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,16">
                        <TextBlock Text="可用工具"
                                  Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                  VerticalAlignment="Center"/>
                        <Button Content="刷新工具列表"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Click="RefreshTools_Click"
                               Margin="16,0,0,0"/>
                    </StackPanel>

                    <DataGrid Grid.Row="1" x:Name="ToolsDataGrid"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             Margin="0,0,0,16">
                        <DataGrid.Columns>
                            <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="60"/>
                            <DataGridTextColumn Header="工具名称" Binding="{Binding Name}" Width="150" IsReadOnly="True"/>
                            <DataGridTextColumn Header="类别" Binding="{Binding Category}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="描述" Binding="{Binding Description}" Width="*" IsReadOnly="True"/>
                            <DataGridTextColumn Header="版本" Binding="{Binding Version}" Width="80" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <GroupBox Grid.Row="2" Header="工具执行设置">
                        <Grid Margin="8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox Grid.Column="0" x:Name="MaxConcurrentToolsTextBox"
                                    materialDesign:HintAssist.Hint="最大并发工具数"
                                    Text="3"
                                    Margin="0,0,8,0"/>

                            <TextBox Grid.Column="1" x:Name="ToolTimeoutTextBox"
                                    materialDesign:HintAssist.Hint="工具超时时间（秒）"
                                    Text="30"
                                    Margin="8,0,0,0"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="{DynamicResource MaterialDesignPaper}" Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" x:Name="StatusTextBlock"
                          Text="就绪"
                          VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="重置配置"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Click="ResetConfig_Click"
                           Margin="0,0,8,0"/>
                    <Button Content="关闭"
                           Style="{StaticResource MaterialDesignFlatButton}"
                           Click="Close_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
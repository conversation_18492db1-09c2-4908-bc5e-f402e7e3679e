<Window x:Class="DocumentCreationSystem.Views.DataCleanupDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="数据库清理工具" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style TargetType="GroupBox" BasedOn="{StaticResource MaterialDesignGroupBox}">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="8"/>
        </Style>
        
        <Style TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="16,8"/>
        </Style>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="数据库清理工具" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   Margin="0,0,0,16"/>

        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 数据完整性检查 -->
                <GroupBox Header="数据完整性检查">
                    <StackPanel>
                        <TextBlock Text="检查数据库中的无效引用和孤立数据" 
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                   Margin="0,0,0,8"/>
                        
                        <Button Name="CheckIntegrityButton" 
                                Content="检查数据完整性" 
                                Click="CheckIntegrityButton_Click"
                                HorizontalAlignment="Left"/>
                        
                        <TextBlock Name="IntegrityResultText" 
                                   Margin="0,8,0,0"
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"/>
                    </StackPanel>
                </GroupBox>

                <!-- 清理选项 -->
                <GroupBox Header="清理选项">
                    <StackPanel>
                        <CheckBox Name="CleanDocumentsCheckBox" 
                                  Content="清理孤立的文档记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <CheckBox Name="CleanNovelProjectsCheckBox" 
                                  Content="清理孤立的小说项目记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <CheckBox Name="CleanChaptersCheckBox" 
                                  Content="清理孤立的章节记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <CheckBox Name="CleanCharactersCheckBox" 
                                  Content="清理孤立的角色记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <CheckBox Name="CleanVectorRecordsCheckBox" 
                                  Content="清理孤立的向量记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <CheckBox Name="CleanWorldSettingsCheckBox" 
                                  Content="清理孤立的世界设定记录" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                        
                        <Separator Margin="0,8"/>
                        
                        <CheckBox Name="CreateBackupCheckBox" 
                                  Content="清理前创建备份" 
                                  IsChecked="True"
                                  Margin="0,4"/>
                    </StackPanel>
                </GroupBox>

                <!-- 备份管理 -->
                <GroupBox Header="备份管理">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <Button Name="CreateBackupButton" 
                                    Content="创建备份" 
                                    Click="CreateBackupButton_Click"/>
                            
                            <Button Name="ViewBackupsButton" 
                                    Content="查看备份" 
                                    Click="ViewBackupsButton_Click"/>
                            
                            <Button Name="CleanOldBackupsButton" 
                                    Content="清理旧备份" 
                                    Click="CleanOldBackupsButton_Click"/>
                        </StackPanel>
                        
                        <TextBlock Name="BackupStatusText" 
                                   Margin="0,4,0,0"
                                   TextWrapping="Wrap"
                                   Visibility="Collapsed"/>
                    </StackPanel>
                </GroupBox>

                <!-- 清理结果 -->
                <GroupBox Header="清理结果" Name="ResultGroupBox" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Name="CleanupResultText" 
                                   TextWrapping="Wrap"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                        
                        <ListBox Name="CleanupDetailsListBox"
                                 Margin="0,8,0,0"
                                 MaxHeight="150"
                                 ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding}"
                                               Margin="8,2"
                                               Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </GroupBox>

            </StackPanel>
        </ScrollViewer>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="2" 
                     Name="ProgressBar" 
                     IsIndeterminate="True" 
                     Visibility="Collapsed"
                     Margin="0,8"/>

        <!-- 按钮栏 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right"
                    Margin="0,16,0,0">
            
            <Button Name="StartCleanupButton" 
                    Content="开始清理" 
                    Click="StartCleanupButton_Click"
                    Style="{StaticResource MaterialDesignRaisedAccentButton}"/>
            
            <Button Name="CloseButton" 
                    Content="关闭" 
                    Click="CloseButton_Click"
                    IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>

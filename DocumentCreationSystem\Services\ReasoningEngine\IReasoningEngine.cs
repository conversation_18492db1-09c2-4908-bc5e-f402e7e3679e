using DocumentCreationSystem.Models.Reasoning;

namespace DocumentCreationSystem.Services.ReasoningEngine
{
    /// <summary>
    /// 推理引擎接口 - 提供逻辑推理、规则处理和知识推导能力
    /// </summary>
    public interface IReasoningEngine
    {
        /// <summary>
        /// 添加事实到知识库
        /// </summary>
        /// <param name="fact">事实</param>
        Task AddFactAsync(ReasoningFact fact);

        /// <summary>
        /// 批量添加事实
        /// </summary>
        /// <param name="facts">事实列表</param>
        Task AddFactsAsync(IEnumerable<ReasoningFact> facts);

        /// <summary>
        /// 添加推理规则
        /// </summary>
        /// <param name="rule">推理规则</param>
        Task AddRuleAsync(ReasoningRule rule);

        /// <summary>
        /// 批量添加规则
        /// </summary>
        /// <param name="rules">规则列表</param>
        Task AddRulesAsync(IEnumerable<ReasoningRule> rules);

        /// <summary>
        /// 查询事实
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns>匹配的事实列表</returns>
        Task<List<ReasoningFact>> QueryFactsAsync(FactQuery query);

        /// <summary>
        /// 执行推理
        /// </summary>
        /// <param name="query">推理查询</param>
        /// <returns>推理结果</returns>
        Task<ReasoningResult> ReasonAsync(ReasoningQuery query);

        /// <summary>
        /// 执行前向推理
        /// </summary>
        /// <param name="initialFacts">初始事实</param>
        /// <returns>推导出的新事实</returns>
        Task<List<ReasoningFact>> ForwardReasoningAsync(List<ReasoningFact> initialFacts);

        /// <summary>
        /// 执行后向推理
        /// </summary>
        /// <param name="goal">目标事实</param>
        /// <returns>推理链</returns>
        Task<ReasoningChain> BackwardReasoningAsync(ReasoningFact goal);

        /// <summary>
        /// 验证事实的一致性
        /// </summary>
        /// <param name="facts">要验证的事实</param>
        /// <returns>一致性检查结果</returns>
        Task<ConsistencyCheckResult> CheckConsistencyAsync(List<ReasoningFact> facts);

        /// <summary>
        /// 解释推理过程
        /// </summary>
        /// <param name="result">推理结果</param>
        /// <returns>推理解释</returns>
        Task<ReasoningExplanation> ExplainReasoningAsync(ReasoningResult result);

        /// <summary>
        /// 获取推理统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        Task<ReasoningStatistics> GetStatisticsAsync();

        /// <summary>
        /// 清空知识库
        /// </summary>
        Task ClearKnowledgeBaseAsync();

        /// <summary>
        /// 保存知识库到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        Task SaveKnowledgeBaseAsync(string filePath);

        /// <summary>
        /// 从文件加载知识库
        /// </summary>
        /// <param name="filePath">文件路径</param>
        Task LoadKnowledgeBaseAsync(string filePath);

        /// <summary>
        /// 获取所有事实
        /// </summary>
        /// <returns>事实列表</returns>
        Task<List<ReasoningFact>> GetAllFactsAsync();

        /// <summary>
        /// 获取所有规则
        /// </summary>
        /// <returns>规则列表</returns>
        Task<List<ReasoningRule>> GetAllRulesAsync();

        /// <summary>
        /// 删除事实
        /// </summary>
        /// <param name="factId">事实ID</param>
        Task RemoveFactAsync(string factId);

        /// <summary>
        /// 删除规则
        /// </summary>
        /// <param name="ruleId">规则ID</param>
        Task RemoveRuleAsync(string ruleId);

        /// <summary>
        /// 更新事实
        /// </summary>
        /// <param name="fact">更新的事实</param>
        Task UpdateFactAsync(ReasoningFact fact);

        /// <summary>
        /// 更新规则
        /// </summary>
        /// <param name="rule">更新的规则</param>
        Task UpdateRuleAsync(ReasoningRule rule);

        /// <summary>
        /// 搜索相关事实
        /// </summary>
        /// <param name="searchTerm">搜索词</param>
        /// <param name="maxResults">最大结果数</param>
        /// <returns>相关事实列表</returns>
        Task<List<ReasoningFact>> SearchFactsAsync(string searchTerm, int maxResults = 10);

        /// <summary>
        /// 获取事实的依赖关系
        /// </summary>
        /// <param name="factId">事实ID</param>
        /// <returns>依赖关系图</returns>
        Task<FactDependencyGraph> GetFactDependenciesAsync(string factId);

        /// <summary>
        /// 执行概率推理
        /// </summary>
        /// <param name="query">概率查询</param>
        /// <returns>概率推理结果</returns>
        Task<ProbabilisticReasoningResult> ProbabilisticReasoningAsync(ProbabilisticQuery query);

        /// <summary>
        /// 执行类比推理
        /// </summary>
        /// <param name="sourceCase">源案例</param>
        /// <param name="targetCase">目标案例</param>
        /// <returns>类比推理结果</returns>
        Task<AnalogyReasoningResult> AnalogyReasoningAsync(ReasoningCase sourceCase, ReasoningCase targetCase);

        /// <summary>
        /// 执行因果推理
        /// </summary>
        /// <param name="cause">原因</param>
        /// <param name="effect">结果</param>
        /// <returns>因果推理结果</returns>
        Task<CausalReasoningResult> CausalReasoningAsync(ReasoningFact cause, ReasoningFact effect);
    }
}

using System.Collections.ObjectModel;

namespace DocumentCreationSystem.ViewModels
{
    /// <summary>
    /// 项目树节点项
    /// </summary>
    public class ProjectTreeItem
    {
        public string Name { get; set; } = string.Empty;
        public ProjectTreeItemType Type { get; set; }
        public int? ProjectId { get; set; }
        public int? DocumentId { get; set; }
        public string? FilePath { get; set; }
        public ObservableCollection<ProjectTreeItem>? Children { get; set; }

        /// <summary>
        /// 显示名称（包含路径信息）
        /// </summary>
        public string DisplayName
        {
            get
            {
                // 如果是项目文件夹且有路径，显示路径信息
                if (Type == ProjectTreeItemType.Project && !string.IsNullOrEmpty(FilePath))
                {
                    return $"{Name}\n{FilePath}";
                }
                return Name;
            }
        }

        /// <summary>
        /// 检查是否有有效路径
        /// </summary>
        public bool HasValidPath
        {
            get
            {
                if (string.IsNullOrEmpty(FilePath))
                    return false;

                // 检查文件或文件夹是否存在
                return System.IO.File.Exists(FilePath) || System.IO.Directory.Exists(FilePath);
            }
        }

        /// <summary>
        /// 获取图标类型
        /// </summary>
        public MaterialDesignThemes.Wpf.PackIconKind IconKind
        {
            get
            {
                return Type switch
                {
                    ProjectTreeItemType.Project => MaterialDesignThemes.Wpf.PackIconKind.Folder,
                    ProjectTreeItemType.Document => GetDocumentIcon(),
                    ProjectTreeItemType.Chapter => MaterialDesignThemes.Wpf.PackIconKind.BookOpenPageVariant,
                    ProjectTreeItemType.Character => MaterialDesignThemes.Wpf.PackIconKind.Account,
                    ProjectTreeItemType.Setting => MaterialDesignThemes.Wpf.PackIconKind.Cog,
                    _ => MaterialDesignThemes.Wpf.PackIconKind.File
                };
            }
        }

        private MaterialDesignThemes.Wpf.PackIconKind GetDocumentIcon()
        {
            var extension = System.IO.Path.GetExtension(Name).ToLower();
            return extension switch
            {
                ".docx" or ".doc" => MaterialDesignThemes.Wpf.PackIconKind.FileWord,
                ".txt" => MaterialDesignThemes.Wpf.PackIconKind.FileDocument,
                ".md" => MaterialDesignThemes.Wpf.PackIconKind.LanguageMarkdown,
                _ => MaterialDesignThemes.Wpf.PackIconKind.FileDocument
            };
        }
    }

    /// <summary>
    /// 项目树节点类型
    /// </summary>
    public enum ProjectTreeItemType
    {
        Project,
        Document,
        Chapter,
        Character,
        Setting
    }
}

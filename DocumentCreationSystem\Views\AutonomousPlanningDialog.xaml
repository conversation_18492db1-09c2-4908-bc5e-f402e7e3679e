<Window x:Class="DocumentCreationSystem.Views.AutonomousPlanningDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="智能自主规划系统" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5">

    <Window.Resources>
        <Style x:Key="ModernButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4A90E2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#357ABD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#2E6DA4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical">
                    <TextBlock Text="🤖 智能自主规划系统"
                             FontSize="24" FontWeight="Bold"
                             Foreground="White"/>
                    <TextBlock Text="Agent根据用户需求自主规划人物及工作流并实施"
                             FontSize="14"
                             Foreground="#BDC3C7"
                             Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="MinimizeButton" Content="🗕"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="Transparent"
                          Click="MinimizeButton_Click"/>
                    <Button Name="CloseButton" Content="✕"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="#E74C3C"
                          Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：用户输入和配置 -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="📝 需求输入" FontSize="18" FontWeight="Bold"
                             Foreground="#2C3E50" Margin="0,0,0,15"/>

                    <TextBlock Text="请描述您的需求：" FontSize="14" Margin="0,0,0,5"/>
                    <TextBox Name="UserRequestTextBox"
                           Height="120"
                           TextWrapping="Wrap"
                           AcceptsReturn="True"
                           VerticalScrollBarVisibility="Auto"
                           Padding="10"
                           FontSize="14"
                           Margin="0,0,0,15"
                           Text="请帮我规划一个小说项目的主要角色和创作流程"/>

                    <TextBlock Text="🎯 规划范围：" FontSize="14" Margin="0,0,0,5"/>
                    <ComboBox Name="PlanningScopeComboBox"
                            FontSize="14" Padding="10,8" Margin="0,0,0,15">
                        <ComboBoxItem Content="任务级别" IsSelected="True"/>
                        <ComboBoxItem Content="内容创作"/>
                        <ComboBoxItem Content="项目级别"/>
                        <ComboBoxItem Content="系统级别"/>
                    </ComboBox>

                    <CheckBox Name="EnableCharacterPlanningCheckBox"
                            Content="启用角色规划"
                            IsChecked="True"
                            FontSize="14" Margin="0,0,0,10"/>

                    <CheckBox Name="EnableWorkflowPlanningCheckBox"
                            Content="启用工作流规划"
                            IsChecked="True"
                            FontSize="14" Margin="0,0,0,15"/>

                    <Button Name="StartPlanningButton"
                          Content="🚀 开始智能规划"
                          Style="{StaticResource ModernButtonStyle}"
                          FontSize="16" FontWeight="Bold"
                          Click="StartPlanningButton_Click"/>

                    <Button Name="StopPlanningButton"
                          Content="⏹ 停止规划"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="#E74C3C"
                          FontSize="14"
                          IsEnabled="False"
                          Click="StopPlanningButton_Click"/>
                </StackPanel>
            </Border>

            <!-- 中间：规划预览和执行状态 -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 标题和状态 -->
                    <Grid Grid.Row="0" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="📊 规划预览与执行"
                                 FontSize="18" FontWeight="Bold"
                                 Foreground="#2C3E50"/>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Ellipse Name="StatusIndicator"
                                   Width="12" Height="12"
                                   Fill="#95A5A6"
                                   Margin="0,0,8,0"/>
                            <TextBlock Name="StatusText"
                                     Text="就绪"
                                     FontSize="14"
                                     Foreground="#7F8C8D"/>
                        </StackPanel>
                    </Grid>

                    <!-- 主要内容区域 -->
                    <TabControl Grid.Row="1" Name="MainTabControl">
                        <TabItem Header="📋 规划概览">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="PlanOverviewPanel" Margin="10">
                                    <TextBlock Text="等待开始规划..."
                                             FontSize="14"
                                             Foreground="#7F8C8D"
                                             HorizontalAlignment="Center"
                                             VerticalAlignment="Center"/>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>

                        <TabItem Header="👥 角色规划">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="CharacterPlanPanel" Margin="10">
                                    <TextBlock Text="角色规划将在这里显示..."
                                             FontSize="14"
                                             Foreground="#7F8C8D"/>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>

                        <TabItem Header="⚙️ 工作流规划">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="WorkflowPlanPanel" Margin="10">
                                    <TextBlock Text="工作流规划将在这里显示..."
                                             FontSize="14"
                                             Foreground="#7F8C8D"/>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>

                        <TabItem Header="📈 执行监控">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="ExecutionMonitorPanel" Margin="10">
                                    <TextBlock Text="执行监控信息将在这里显示..."
                                             FontSize="14"
                                             Foreground="#7F8C8D"/>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>
                    </TabControl>

                    <!-- 进度条 -->
                    <StackPanel Grid.Row="2" Margin="0,15,0,0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Name="ProgressText"
                                     Text="进度：0%"
                                     FontSize="12"
                                     Foreground="#7F8C8D"/>

                            <TextBlock Grid.Column="1" Name="TimeElapsedText"
                                     Text="用时：00:00"
                                     FontSize="12"
                                     Foreground="#7F8C8D"/>
                        </Grid>

                        <ProgressBar Name="ExecutionProgressBar"
                                   Height="8"
                                   Margin="0,5,0,0"
                                   Background="#ECF0F1"
                                   Foreground="#4A90E2"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 右侧：结果展示和控制 -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="📊 执行结果" FontSize="18" FontWeight="Bold"
                             Foreground="#2C3E50" Margin="0,0,0,15"/>

                    <!-- 快速统计 -->
                    <Border Background="#ECF0F1" CornerRadius="5" Padding="10" Margin="0,0,0,15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="创建角色：" FontSize="12"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Name="CreatedCharactersCount" Text="0" FontSize="12" FontWeight="Bold"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="工作流步骤：" FontSize="12"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Name="WorkflowStepsCount" Text="0" FontSize="12" FontWeight="Bold"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="执行调整：" FontSize="12"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Name="AdjustmentsCount" Text="0" FontSize="12" FontWeight="Bold"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="成功率：" FontSize="12"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" Name="SuccessRate" Text="0%" FontSize="12" FontWeight="Bold"/>
                        </Grid>
                    </Border>

                    <!-- 控制按钮 -->
                    <TextBlock Text="🎮 执行控制" FontSize="14" FontWeight="Bold"
                             Foreground="#2C3E50" Margin="0,0,0,10"/>

                    <Button Name="PauseResumeButton"
                          Content="⏸ 暂停执行"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="#F39C12"
                          IsEnabled="False"
                          Click="PauseResumeButton_Click"/>

                    <Button Name="RestartButton"
                          Content="🔄 重新开始"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="#9B59B6"
                          Click="RestartButton_Click"/>

                    <Button Name="ExportResultsButton"
                          Content="📤 导出结果"
                          Style="{StaticResource ModernButtonStyle}"
                          Background="#27AE60"
                          Click="ExportResultsButton_Click"/>

                    <!-- 日志输出 -->
                    <TextBlock Text="📝 执行日志" FontSize="14" FontWeight="Bold"
                             Foreground="#2C3E50" Margin="0,20,0,10"/>

                    <Border Background="#2C3E50" CornerRadius="5" Height="200">
                        <ScrollViewer Name="LogScrollViewer"
                                    VerticalScrollBarVisibility="Auto"
                                    Padding="10">
                            <TextBlock Name="LogTextBlock"
                                     Foreground="#ECF0F1"
                                     FontFamily="Consolas"
                                     FontSize="11"
                                     TextWrapping="Wrap"
                                     Text="系统就绪，等待开始规划..."/>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </Border>
        </Grid>

        <!-- 底部状态栏 -->
        <Border Grid.Row="2" Background="#34495E" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Name="StatusBarText"
                         Text="智能自主规划系统已就绪"
                         Foreground="White"
                         FontSize="12"/>

                <TextBlock Grid.Column="1" Name="CurrentProjectText"
                         Text="当前项目：未选择"
                         Foreground="#BDC3C7"
                         FontSize="12"
                         Margin="0,0,20,0"/>

                <TextBlock Grid.Column="2" Name="SystemTimeText"
                         Text="00:00:00"
                         Foreground="#BDC3C7"
                         FontSize="12"/>
            </Grid>
        </Border>
    </Grid>
</Window>

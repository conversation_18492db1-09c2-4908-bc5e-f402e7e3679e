using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 向量服务接口
/// </summary>
public interface IVectorService
{
    /// <summary>
    /// 初始化向量数据库连接
    /// </summary>
    /// <returns>是否初始化成功</returns>
    Task<bool> InitializeAsync();

    /// <summary>
    /// 创建集合（如果不存在）
    /// </summary>
    /// <param name="collectionName">集合名称</param>
    /// <param name="vectorSize">向量维度</param>
    /// <returns>是否创建成功</returns>
    Task<bool> CreateCollectionAsync(string collectionName, int vectorSize = 1024);

    /// <summary>
    /// 删除集合
    /// </summary>
    /// <param name="collectionName">集合名称</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteCollectionAsync(string collectionName);

    /// <summary>
    /// 向量化文本
    /// </summary>
    /// <param name="text">文本内容</param>
    /// <returns>向量数组</returns>
    Task<float[]> EmbedTextAsync(string text);

    /// <summary>
    /// 添加文档向量
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="textChunks">文本片段列表</param>
    /// <returns>添加的向量记录</returns>
    Task<List<VectorRecord>> AddDocumentVectorsAsync(int documentId, List<string> textChunks);

    /// <summary>
    /// 添加文档向量（使用自定义向量ID）
    /// </summary>
    /// <param name="vectorId">向量ID</param>
    /// <param name="content">文档内容</param>
    /// <param name="metadata">元数据</param>
    /// <returns>向量ID</returns>
    Task<string> AddDocumentAsync(string vectorId, string content, Dictionary<string, object>? metadata = null);

    /// <summary>
    /// 更新文档向量
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="textChunks">新的文本片段列表</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateDocumentVectorsAsync(int documentId, List<string> textChunks);

    /// <summary>
    /// 删除文档向量
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteDocumentVectorsAsync(int documentId);

    /// <summary>
    /// 语义搜索
    /// </summary>
    /// <param name="query">查询文本</param>
    /// <param name="collectionName">集合名称</param>
    /// <param name="limit">返回结果数量</param>
    /// <param name="threshold">相似度阈值</param>
    /// <returns>搜索结果</returns>
    Task<List<VectorSearchResult>> SearchAsync(string query, string collectionName = "documents", int limit = 10, float threshold = 0.7f);

    /// <summary>
    /// 获取相关上下文
    /// </summary>
    /// <param name="query">查询文本</param>
    /// <param name="projectId">项目ID（可选，限制搜索范围）</param>
    /// <param name="limit">返回结果数量</param>
    /// <returns>相关上下文文本</returns>
    Task<List<string>> GetRelevantContextAsync(string query, int? projectId = null, int limit = 5);

    /// <summary>
    /// 分割文本为片段
    /// </summary>
    /// <param name="text">原始文本</param>
    /// <param name="chunkSize">片段大小</param>
    /// <param name="overlap">重叠大小</param>
    /// <returns>文本片段列表</returns>
    List<string> SplitTextIntoChunks(string text, int chunkSize = 500, int overlap = 50);

    /// <summary>
    /// 检查向量数据库连接状态
    /// </summary>
    /// <returns>是否连接正常</returns>
    Task<bool> CheckConnectionAsync();

    /// <summary>
    /// 获取集合信息
    /// </summary>
    /// <param name="collectionName">集合名称</param>
    /// <returns>集合信息</returns>
    Task<VectorCollectionInfo?> GetCollectionInfoAsync(string collectionName);

    /// <summary>
    /// 重建索引
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>是否重建成功</returns>
    Task<bool> RebuildIndexAsync(int projectId);
}



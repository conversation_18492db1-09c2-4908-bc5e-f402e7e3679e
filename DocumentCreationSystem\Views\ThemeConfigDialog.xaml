<Window x:Class="DocumentCreationSystem.Views.ThemeConfigDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:converters="clr-namespace:DocumentCreationSystem.Converters"
        Title="主题颜色配置" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <converters:HexToColorConverter x:Key="HexToColorConverter"/>
    </Window.Resources>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- 左侧预设主题列表 -->
        <materialDesign:Card Grid.Column="0" Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题和筛选 -->
                <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                            <materialDesign:PackIcon Kind="Palette" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBlock Text="预设主题" VerticalAlignment="Center" FontWeight="Medium" FontSize="16"/>
                        </StackPanel>

                        <!-- 主题分类选择 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                            <RadioButton x:Name="AllThemesRadio" Content="全部"
                                       Margin="0,0,16,0" Checked="ThemeFilter_Changed"/>
                            <RadioButton x:Name="LightThemesRadio" Content="浅色"
                                       Margin="0,0,16,0" Checked="ThemeFilter_Changed"/>
                            <RadioButton x:Name="DarkThemesRadio" Content="暗黑"
                                       Checked="ThemeFilter_Changed"/>
                        </StackPanel>

                        <!-- 搜索框 -->
                        <TextBox x:Name="ThemeSearchBox"
                               materialDesign:HintAssist.Hint="搜索主题..."
                               materialDesign:TextFieldAssist.HasLeadingIcon="True"
                               materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                               TextChanged="ThemeSearch_TextChanged"
                               Margin="0,4,0,0"/>
                    </StackPanel>
                </materialDesign:ColorZone>

                <!-- 预设主题列表 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl x:Name="PresetThemesItemsControl" Margin="8">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <materialDesign:Card Margin="4" Cursor="Hand" MouseLeftButtonUp="PresetTheme_Click">
                                    <materialDesign:Card.Tag>
                                        <Binding/>
                                    </materialDesign:Card.Tag>
                                    <Grid Margin="12">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- 主题名称和类型标识 -->
                                        <StackPanel Grid.Row="0" Orientation="Horizontal">
                                            <TextBlock Text="{Binding Name}" FontWeight="Medium" FontSize="14" VerticalAlignment="Center"/>
                                            <materialDesign:Chip Grid.Row="0" Content="{Binding Category}"
                                                               Margin="8,0,0,0" FontSize="10" Height="20">
                                                <materialDesign:Chip.Style>
                                                    <Style TargetType="materialDesign:Chip" BasedOn="{StaticResource {x:Type materialDesign:Chip}}">
                                                        <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding BaseTheme}" Value="Dark">
                                                                <Setter Property="Background" Value="#424242"/>
                                                                <Setter Property="Foreground" Value="White"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </materialDesign:Chip.Style>
                                            </materialDesign:Chip>
                                        </StackPanel>

                                        <TextBlock Grid.Row="1" Text="{Binding Description}" FontSize="12"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="0,4,0,8"/>

                                        <!-- 颜色预览 -->
                                        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,4">
                                            <Ellipse Width="16" Height="16" Margin="0,0,4,0">
                                                <Ellipse.Fill>
                                                    <SolidColorBrush Color="{Binding PrimaryColor, Converter={StaticResource HexToColorConverter}}"/>
                                                </Ellipse.Fill>
                                            </Ellipse>
                                            <Ellipse Width="16" Height="16" Margin="0,0,4,0">
                                                <Ellipse.Fill>
                                                    <SolidColorBrush Color="{Binding SecondaryColor, Converter={StaticResource HexToColorConverter}}"/>
                                                </Ellipse.Fill>
                                            </Ellipse>
                                            <Ellipse Width="16" Height="16">
                                                <Ellipse.Fill>
                                                    <SolidColorBrush Color="{Binding AccentColor, Converter={StaticResource HexToColorConverter}}"/>
                                                </Ellipse.Fill>
                                            </Ellipse>
                                        </StackPanel>

                                        <!-- 主题标签 -->
                                        <ItemsControl Grid.Row="3" ItemsSource="{Binding Tags}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <WrapPanel Orientation="Horizontal"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border Background="{DynamicResource MaterialDesignDivider}"
                                                          CornerRadius="8" Margin="0,0,4,2" Padding="4,1">
                                                        <TextBlock Text="{Binding}" FontSize="9"
                                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- 重置按钮 -->
                <Button Grid.Row="2" Content="重置为默认" Margin="16" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Click="ResetToDefault_Click"/>
            </Grid>
        </materialDesign:Card>

        <!-- 右侧自定义配置 -->
        <materialDesign:Card Grid.Column="1" Margin="16" materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 标题 -->
                <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="16">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ColorLens" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <TextBlock Text="自定义主题" VerticalAlignment="Center" FontWeight="Medium" FontSize="16"/>
                    </StackPanel>
                </materialDesign:ColorZone>

                <!-- 配置内容 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="16">
                    <StackPanel>
                        <!-- 基础主题选择 -->
                        <TextBlock Text="基础主题" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,8"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,24">
                            <RadioButton x:Name="LightThemeRadio" Content="浅色主题" Margin="0,0,24,0"
                                       Checked="BaseTheme_Changed"/>
                            <RadioButton x:Name="DarkThemeRadio" Content="深色主题" 
                                       Checked="BaseTheme_Changed"/>
                        </StackPanel>

                        <!-- 颜色配置 -->
                        <TextBlock Text="颜色配置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,8"/>
                        
                        <!-- 主色调 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="主色调" VerticalAlignment="Center"/>
                            <TextBox x:Name="PrimaryColorTextBox" Grid.Column="1" Text="#2196F3" Margin="8,0"
                                   materialDesign:HintAssist.Hint="十六进制颜色值"
                                   TextChanged="ColorTextBox_TextChanged"/>
                            <Rectangle Grid.Column="2" x:Name="PrimaryColorPreview" Width="40" Height="30" 
                                     Fill="#2196F3" Stroke="Gray" StrokeThickness="1"/>
                        </Grid>

                        <!-- 次要色调 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="次要色调" VerticalAlignment="Center"/>
                            <TextBox x:Name="SecondaryColorTextBox" Grid.Column="1" Text="#03DAC6" Margin="8,0"
                                   materialDesign:HintAssist.Hint="十六进制颜色值"
                                   TextChanged="ColorTextBox_TextChanged"/>
                            <Rectangle Grid.Column="2" x:Name="SecondaryColorPreview" Width="40" Height="30" 
                                     Fill="#03DAC6" Stroke="Gray" StrokeThickness="1"/>
                        </Grid>

                        <!-- 强调色 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="60"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="强调色" VerticalAlignment="Center"/>
                            <TextBox x:Name="AccentColorTextBox" Grid.Column="1" Text="#1976D2" Margin="8,0"
                                   materialDesign:HintAssist.Hint="十六进制颜色值"
                                   TextChanged="ColorTextBox_TextChanged"/>
                            <Rectangle Grid.Column="2" x:Name="AccentColorPreview" Width="40" Height="30" 
                                     Fill="#1976D2" Stroke="Gray" StrokeThickness="1"/>
                        </Grid>

                        <!-- 预览区域 -->
                        <TextBlock Text="预览效果" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,24,0,8"/>
                        <materialDesign:Card x:Name="PreviewCard" Padding="16" Margin="0,0,0,16">
                            <StackPanel>
                                <TextBlock Text="这是预览文本" FontSize="16" Margin="0,0,0,8"/>
                                <Button Content="主要按钮" Style="{StaticResource MaterialDesignRaisedButton}" 
                                      Margin="0,0,8,8" HorizontalAlignment="Left"/>
                                <Button Content="次要按钮" Style="{StaticResource MaterialDesignOutlinedButton}" 
                                      Margin="0,0,8,8" HorizontalAlignment="Left"/>
                                <TextBox materialDesign:HintAssist.Hint="输入框示例" Margin="0,8,0,0" Width="200" HorizontalAlignment="Left"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- 保存自定义主题 -->
                        <TextBlock Text="保存主题" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,24,0,8"/>
                        <TextBox x:Name="ThemeNameTextBox" materialDesign:HintAssist.Hint="主题名称" Margin="0,0,0,8"/>
                        <TextBox x:Name="ThemeDescriptionTextBox" materialDesign:HintAssist.Hint="主题描述（可选）" 
                               AcceptsReturn="True" MaxLines="3" Margin="0,0,0,16"/>
                    </StackPanel>
                </ScrollViewer>

                <!-- 底部按钮 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="16">
                    <Button Content="预览" Margin="0,0,8,0" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="Preview_Click"/>
                    <Button Content="保存主题" Margin="0,0,8,0" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="SaveTheme_Click"/>
                    <Button Content="应用" Margin="0,0,8,0" Style="{StaticResource MaterialDesignRaisedButton}"
                            Click="Apply_Click"/>
                    <Button Content="取消" Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="Cancel_Click"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 决策上下文
    /// </summary>
    public class DecisionContext
    {
        public string ProblemDescription { get; set; } = string.Empty;
        public string ProblemType { get; set; } = string.Empty;
        public string? Domain { get; set; }
        public List<string> Goals { get; set; } = new();
        public Dictionary<string, object> Constraints { get; set; } = new();
        public List<string> Stakeholders { get; set; } = new();
        public Dictionary<string, object> AvailableResources { get; set; } = new();
        public double Budget { get; set; }
        public DateTime Deadline { get; set; }
        public Urgency Urgency { get; set; } = Urgency.Medium;
        public RiskTolerance RiskTolerance { get; set; } = RiskTolerance.Medium;
        public bool AllowInnovation { get; set; } = true;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 决策选项
    /// </summary>
    public class DecisionOption
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<string> Actions { get; set; } = new();
        public List<string> RequiredSkills { get; set; } = new();
        public Dictionary<string, object> RequiredResources { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
        public double EstimatedCost { get; set; }
        public Dictionary<string, object> ExpectedOutcomes { get; set; } = new();
        public double Score { get; set; }
        public List<string> Risks { get; set; } = new();
        public List<string> Benefits { get; set; } = new();
        public double Feasibility { get; set; } = 1.0;
        public string? EvaluationDetails { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public override bool Equals(object? obj)
        {
            return obj is DecisionOption option && Name == option.Name;
        }

        public override int GetHashCode()
        {
            return Name.GetHashCode();
        }
    }

    /// <summary>
    /// 决策
    /// </summary>
    public class Decision
    {
        public string Id { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public DecisionContext Context { get; set; } = new();
        public DecisionOption SelectedOption { get; set; } = new();
        public List<DecisionOption> AlternativeOptions { get; set; } = new();
        public string Reasoning { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public DecisionStatus Status { get; set; } = DecisionStatus.Pending;
        public DateTime? ExecutionStartTime { get; set; }
        public DateTime? ExecutionEndTime { get; set; }
        public Dictionary<string, object>? ActualOutcome { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// 决策结果
    /// </summary>
    public class DecisionResult
    {
        public string DecisionId { get; set; } = string.Empty;
        public string AgentId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool Success { get; set; }
        public ExecutionStatus Status { get; set; } = ExecutionStatus.Pending;
        public Dictionary<string, object> Outcomes { get; set; } = new();
        public TimeSpan? ActualDuration { get; set; }
        public TimeSpan? ActualCost { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> Lessons { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 决策模式
    /// </summary>
    public class DecisionPattern
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Frequency { get; set; }
        public double Confidence { get; set; }
        public Dictionary<string, object> Characteristics { get; set; } = new();
        public DateTime DiscoveredAt { get; set; } = DateTime.Now;
        public DateTime LastObserved { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 决策建议
    /// </summary>
    public class DecisionRecommendation
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public RecommendationType Type { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public string? SuggestedAction { get; set; }
        public List<string> Rationale { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Agent决策状态
    /// </summary>
    public class AgentDecisionState
    {
        public string AgentId { get; set; } = string.Empty;
        public List<Decision> DecisionHistory { get; set; } = new();
        public Dictionary<string, int> DecisionTypeCount { get; set; } = new();
        public double AverageDecisionTime { get; set; }
        public double SuccessRate { get; set; }
        public DateTime LastDecisionTime { get; set; }
        public Dictionary<string, object> Preferences { get; set; } = new();
    }

    /// <summary>
    /// 上下文分析结果
    /// </summary>
    public class ContextAnalysisResult
    {
        public double Complexity { get; set; }
        public Urgency Urgency { get; set; }
        public RiskLevel RiskLevel { get; set; }
        public List<string> RequiredSkills { get; set; } = new();
        public Dictionary<string, object> AvailableResources { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    /// <summary>
    /// 选项评估结果
    /// </summary>
    public class OptionEvaluation
    {
        public double Score { get; set; }
        public double Feasibility { get; set; }
        public List<string> Risks { get; set; } = new();
        public List<string> Benefits { get; set; } = new();
        public string Details { get; set; } = string.Empty;
        public Dictionary<string, double> Criteria { get; set; } = new();
    }

    /// <summary>
    /// 行动执行结果
    /// </summary>
    public class ActionExecutionResult
    {
        public bool Success { get; set; }
        public Dictionary<string, object> Outcomes { get; set; } = new();
        public TimeSpan Cost { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string> CompletedActions { get; set; } = new();
        public List<string> FailedActions { get; set; } = new();
    }

    /// <summary>
    /// 紧急程度
    /// </summary>
    public enum Urgency
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 风险容忍度
    /// </summary>
    public enum RiskTolerance
    {
        VeryLow,
        Low,
        Medium,
        High,
        VeryHigh
    }

    /// <summary>
    /// 风险级别
    /// </summary>
    public enum RiskLevel
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 决策状态
    /// </summary>
    public enum DecisionStatus
    {
        Pending,
        Executing,
        Completed,
        Failed,
        Cancelled
    }

    // ExecutionStatus枚举已在AutonomousPlanningModels.cs中定义


}

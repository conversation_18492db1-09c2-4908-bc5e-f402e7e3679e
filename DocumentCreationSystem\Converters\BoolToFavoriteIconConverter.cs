using System.Globalization;
using System.Windows.Data;
using MaterialDesignThemes.Wpf;

namespace DocumentCreationSystem.Converters;

/// <summary>
/// 布尔值到收藏图标的转换器
/// </summary>
public class BoolToFavoriteIconConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool isFavorite)
        {
            return isFavorite ? PackIconKind.Heart : PackIconKind.HeartOutline;
        }
        
        return PackIconKind.HeartOutline;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

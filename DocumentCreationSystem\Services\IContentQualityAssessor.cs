using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 内容质量评估器接口
    /// </summary>
    public interface IContentQualityAssessor
    {
        /// <summary>
        /// 评估逻辑连贯性
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>质量分数 (0-100)</returns>
        Task<QualityScore> AssessLogicalCoherenceAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 评估语言质量
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>质量分数 (0-100)</returns>
        Task<QualityScore> AssessLanguageQualityAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 评估事实准确性
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>质量分数 (0-100)</returns>
        Task<QualityScore> AssessFactualAccuracyAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 评估结构完整性
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>质量分数 (0-100)</returns>
        Task<QualityScore> AssessStructuralIntegrityAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 综合质量评估
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>综合质量评估结果</returns>
        Task<ComprehensiveQualityAssessment> AssessOverallQualityAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 评估内容创新性
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>创新性分数 (0-100)</returns>
        Task<QualityScore> AssessInnovationAsync(string content, CancellationToken cancellationToken = default);

        /// <summary>
        /// 评估内容可读性
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>可读性分数 (0-100)</returns>
        Task<QualityScore> AssessReadabilityAsync(string content, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 质量分数
    /// </summary>
    public class QualityScore
    {
        /// <summary>
        /// 分数值 (0-100)
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// 置信度 (0-1)
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 详细说明
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// 改进建议
        /// </summary>
        public List<string> Suggestions { get; set; } = new();

        /// <summary>
        /// 评估时间
        /// </summary>
        public DateTime AssessedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 综合质量评估结果
    /// </summary>
    public class ComprehensiveQualityAssessment
    {
        /// <summary>
        /// 总体质量分数 (0-100)
        /// </summary>
        public double OverallScore { get; set; }

        /// <summary>
        /// 各维度分数
        /// </summary>
        public Dictionary<string, QualityScore> DimensionScores { get; set; } = new();

        /// <summary>
        /// 质量等级
        /// </summary>
        public QualityLevel Level { get; set; }

        /// <summary>
        /// 主要优点
        /// </summary>
        public List<string> Strengths { get; set; } = new();

        /// <summary>
        /// 主要问题
        /// </summary>
        public List<string> Weaknesses { get; set; } = new();

        /// <summary>
        /// 改进建议
        /// </summary>
        public List<ImprovementSuggestion> ImprovementSuggestions { get; set; } = new();

        /// <summary>
        /// 评估摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// 质量等级
    /// </summary>
    public enum QualityLevel
    {
        /// <summary>
        /// 优秀 (90-100)
        /// </summary>
        Excellent,

        /// <summary>
        /// 良好 (80-89)
        /// </summary>
        Good,

        /// <summary>
        /// 中等 (70-79)
        /// </summary>
        Average,

        /// <summary>
        /// 需要改进 (60-69)
        /// </summary>
        NeedsImprovement,

        /// <summary>
        /// 较差 (0-59)
        /// </summary>
        Poor
    }

    /// <summary>
    /// 改进建议
    /// </summary>
    public class ImprovementSuggestion
    {
        /// <summary>
        /// 建议ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 建议类型
        /// </summary>
        public SuggestionType Type { get; set; }

        /// <summary>
        /// 建议标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 建议描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 优先级 (1-5, 5最高)
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 影响的内容区域
        /// </summary>
        public List<string> AffectedAreas { get; set; } = new();

        /// <summary>
        /// 预期改进效果
        /// </summary>
        public string ExpectedImprovement { get; set; } = string.Empty;

        /// <summary>
        /// 实施难度 (1-5, 5最难)
        /// </summary>
        public int ImplementationDifficulty { get; set; }

        /// <summary>
        /// 具体操作步骤
        /// </summary>
        public List<string> ActionSteps { get; set; } = new();
    }

    /// <summary>
    /// 建议类型
    /// </summary>
    public enum SuggestionType
    {
        /// <summary>
        /// 结构改进
        /// </summary>
        Structure,

        /// <summary>
        /// 内容改进
        /// </summary>
        Content,

        /// <summary>
        /// 语言改进
        /// </summary>
        Language,

        /// <summary>
        /// 逻辑改进
        /// </summary>
        Logic,

        /// <summary>
        /// 格式改进
        /// </summary>
        Format,

        /// <summary>
        /// 创新性改进
        /// </summary>
        Innovation,

        /// <summary>
        /// 可读性改进
        /// </summary>
        Readability
    }

    /// <summary>
    /// 质量指标
    /// </summary>
    public class QualityMetrics
    {
        /// <summary>
        /// 逻辑连贯性分数
        /// </summary>
        public double LogicalCoherence { get; set; }

        /// <summary>
        /// 语言质量分数
        /// </summary>
        public double LanguageQuality { get; set; }

        /// <summary>
        /// 结构完整性分数
        /// </summary>
        public double StructuralIntegrity { get; set; }

        /// <summary>
        /// 创新性分数
        /// </summary>
        public double Innovation { get; set; }

        /// <summary>
        /// 可读性分数
        /// </summary>
        public double Readability { get; set; }

        /// <summary>
        /// 事实准确性分数
        /// </summary>
        public double FactualAccuracy { get; set; }

        /// <summary>
        /// 总体分数
        /// </summary>
        public double OverallScore => (LogicalCoherence + LanguageQuality + StructuralIntegrity + 
                                     Innovation + Readability + FactualAccuracy) / 6.0;

        /// <summary>
        /// 计算时间
        /// </summary>
        public DateTime CalculatedAt { get; set; } = DateTime.Now;
    }
}

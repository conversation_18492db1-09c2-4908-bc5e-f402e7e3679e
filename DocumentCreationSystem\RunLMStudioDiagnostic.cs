using System;
using System.Threading.Tasks;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 运行LM Studio诊断的控制台程序
    /// </summary>
    public class RunLMStudioDiagnostic
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("LM Studio 诊断工具");
            Console.WriteLine("==================");
            Console.WriteLine();

            var diagnostic = new LMStudioDiagnosticTool();
            
            try
            {
                var result = await diagnostic.RunDiagnosticAsync();
                
                Console.WriteLine();
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"诊断过程中发生错误: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            finally
            {
                diagnostic.Dispose();
            }
        }
    }
}

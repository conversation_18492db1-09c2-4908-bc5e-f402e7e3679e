using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 提示词配置模型
    /// </summary>
    public class PromptConfig
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 是否为默认配置
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        #region 全书大纲生成提示词

        /// <summary>
        /// 全书大纲生成提示词模板
        /// </summary>
        public string OverallOutlinePrompt { get; set; } = @"你是一位专业的小说创作助手，请根据以下信息生成详细的全书大纲：

书名：{BookTitle}
创作方向：{CreativeDirection}
分卷数量：{VolumeCount}
总章节数：{ChapterCount}
每章字数：{WordsPerChapter}

请按照以下要求生成全书大纲：
1. 整体故事架构要完整，有明确的开端、发展、高潮、结局
2. 每卷都要有独立的主题和冲突，同时与整体故事线保持连贯
3. 角色发展要有层次，主角成长轨迹清晰
4. 世界观设定要自洽，符合所选类型的特点
5. 情节安排要有张弛有度，避免平铺直叙

请以结构化的方式输出大纲，包含：
- 故事背景
- 主要角色
- 分卷概述
- 核心冲突
- 结局走向";

        #endregion

        #region 世界设定生成提示词

        /// <summary>
        /// 世界设定生成提示词模板
        /// </summary>
        public string WorldSettingPrompt { get; set; } = @"你是一位专业的世界观设计师，请根据以下全书大纲生成详细的世界设定：

全书大纲：
{OverallOutline}

请生成以下世界设定文件：
1. 世界观设定管理.txt - 整体世界观、历史背景、地理环境
2. 角色设定管理.txt - 主要角色的详细设定
3. 关系网络.txt - 角色之间的关系网络
4. 修炼体系设定管理.txt - 力量体系、等级划分
5. 功法技能设定管理.txt - 具体的功法和技能
6. 武器装备设定管理.txt - 武器装备系统
7. 门派势力设定管理.txt - 各种组织势力
8. 政治体系设定管理.txt - 政治制度和权力结构
9. 司法体系设定管理.txt - 法律制度
10. 商业体系设定管理.txt - 经济贸易系统

每个设定文件都要：
- 内容详细具体，有可操作性
- 与故事情节紧密结合
- 保持内部逻辑一致性
- 为后续创作提供充分支撑";

        #endregion

        #region 卷宗大纲生成提示词

        /// <summary>
        /// 卷宗大纲生成提示词模板
        /// </summary>
        public string VolumeOutlinePrompt { get; set; } = @"你是一位专业的小说创作助手，请根据全书大纲和世界设定生成第{VolumeNumber}卷的详细大纲：

全书大纲：
{OverallOutline}

世界设定摘要：
{WorldSettingSummary}

卷宗信息：
- 卷数：第{VolumeNumber}卷（共{TotalVolumes}卷）
- 章节范围：第{StartChapter}章 - 第{EndChapter}章
- 预计字数：{EstimatedWords}字

请按照以下要求生成卷宗大纲：
1. 明确本卷的核心主题和主要冲突
2. 详细规划章节分布和情节发展
3. 确保与前后卷的衔接自然
4. 角色发展要有明显进步
5. 世界观展现要有层次

输出格式：
- 卷宗标题
- 核心主题
- 主要冲突
- 章节规划（每章简要概述）
- 角色发展
- 关键情节点";

        #endregion

        #region 章节细纲生成提示词

        /// <summary>
        /// 章节细纲生成提示词模板
        /// </summary>
        public string ChapterOutlinePrompt { get; set; } = @"你是一位专业的小说创作助手，现在需要为第{ChapterNumber}章创作详细的章节细纲：

书名：《{BookTitle}》
卷宗信息：第{VolumeNumber}卷 - {VolumeTitle}
章节位置：第{ChapterNumber}章（第{VolumeNumber}卷第{ChapterInVolume}章）
目标字数：{WordsPerChapter}字

卷宗大纲：
{VolumeOutline}

世界设定参考：
{WorldSettings}

前文情节：
{PreviousOutlines}

{PreviousChapterEnding}

请按照以下要求生成章节细纲：
1. 明确本章的核心任务和推进目标
2. 设计具体的场景和情节发展
3. 确保与前文的自然衔接
4. 角色行为要符合设定和逻辑
5. 情节要有张力和吸引力

输出格式：
- 章节标题
- 核心情节
- 场景设定
- 角色动作
- 冲突设计
- 章节结尾";

        #endregion

        #region 章节正文生成提示词

        /// <summary>
        /// 章节正文生成提示词模板
        /// </summary>
        public string ChapterContentPrompt { get; set; } = @"你是一位专业的小说作家，请根据以下章节细纲创作正文内容：

章节细纲：
{ChapterOutline}

世界设定参考：
{WorldSettings}

前文内容摘要：
{PreviousContent}

创作要求：
1. 目标字数：{WordsPerChapter}字
2. 写作风格：{WritingStyle}
3. 语言要生动形象，符合小说文体
4. 对话要自然真实，符合角色性格
5. 情节推进要流畅，节奏把控得当
6. 细节描写要丰富，增强代入感

注意事项：
- 严格按照细纲内容创作，不要偏离主线
- 保持与前文的连贯性和一致性
- 确保角色行为符合设定
- 情节发展要合理有逻辑

请直接输出章节正文内容：";

        #endregion

        #region 时间线更新提示词

        /// <summary>
        /// 时间线更新提示词模板
        /// </summary>
        public string TimelineUpdatePrompt { get; set; } = @"你是一位专业的故事时间线管理员，请根据最新的章节内容更新时间线：

当前章节：第{ChapterNumber}章
章节内容：
{ChapterContent}

现有时间线：
{CurrentTimeline}

请按照以下要求更新时间线：
1. 提取本章的关键事件和时间点
2. 更新角色状态和位置变化
3. 记录重要的情节发展
4. 维护时间线的连贯性

输出格式：
【第{ChapterNumber}章时间线更新】
- 时间：[具体时间或时间段]
- 地点：[主要场景]
- 主要事件：[关键情节]
- 角色状态：[主要角色的状态变化]
- 重要信息：[需要记录的关键信息]";

        #endregion
    }
}

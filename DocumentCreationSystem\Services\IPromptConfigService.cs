using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 提示词配置服务接口
    /// </summary>
    public interface IPromptConfigService
    {
        /// <summary>
        /// 获取所有提示词配置
        /// </summary>
        Task<List<PromptConfig>> GetAllConfigsAsync();

        /// <summary>
        /// 根据ID获取提示词配置
        /// </summary>
        Task<PromptConfig?> GetConfigByIdAsync(int id);

        /// <summary>
        /// 获取默认提示词配置
        /// </summary>
        Task<PromptConfig> GetDefaultConfigAsync();

        /// <summary>
        /// 保存提示词配置
        /// </summary>
        Task<PromptConfig> SaveConfigAsync(PromptConfig config);

        /// <summary>
        /// 更新提示词配置
        /// </summary>
        Task<PromptConfig> UpdateConfigAsync(PromptConfig config);

        /// <summary>
        /// 删除提示词配置
        /// </summary>
        Task<bool> DeleteConfigAsync(int id);

        /// <summary>
        /// 设置默认配置
        /// </summary>
        Task<bool> SetDefaultConfigAsync(int id);

        /// <summary>
        /// 创建默认配置
        /// </summary>
        Task<PromptConfig> CreateDefaultConfigAsync();

        /// <summary>
        /// 导出配置到文件
        /// </summary>
        Task<bool> ExportConfigAsync(int id, string filePath);

        /// <summary>
        /// 从文件导入配置
        /// </summary>
        Task<PromptConfig?> ImportConfigAsync(string filePath);

        /// <summary>
        /// 验证配置的完整性
        /// </summary>
        Task<bool> ValidateConfigAsync(PromptConfig config);

        /// <summary>
        /// 获取配置的预览信息
        /// </summary>
        Task<string> GetConfigPreviewAsync(int id);
    }
}

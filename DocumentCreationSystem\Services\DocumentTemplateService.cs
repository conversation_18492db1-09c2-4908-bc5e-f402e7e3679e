using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 文档模板服务 - 提供各种中文文档模板
    /// </summary>
    public interface IDocumentTemplateService
    {
        /// <summary>
        /// 获取章节模板
        /// </summary>
        string GetChapterTemplate(string bookTitle, int chapterNumber, string chapterTitle);
        
        /// <summary>
        /// 获取大纲模板
        /// </summary>
        string GetOutlineTemplate(string bookTitle, string outlineType);
        
        /// <summary>
        /// 获取角色设定模板
        /// </summary>
        string GetCharacterTemplate(string characterName);
        
        /// <summary>
        /// 获取世界设定模板
        /// </summary>
        string GetWorldSettingTemplate(string settingType);
        
        /// <summary>
        /// 获取时间线模板
        /// </summary>
        string GetTimelineTemplate(string bookTitle, int? volumeNumber = null);
        
        /// <summary>
        /// 获取项目说明模板
        /// </summary>
        string GetProjectReadmeTemplate(string projectName, string projectType);
    }

    public class DocumentTemplateService : IDocumentTemplateService
    {
        private readonly ILogger<DocumentTemplateService> _logger;

        public DocumentTemplateService(ILogger<DocumentTemplateService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 获取章节模板
        /// </summary>
        public string GetChapterTemplate(string bookTitle, int chapterNumber, string chapterTitle)
        {
            var template = new StringBuilder();
            template.AppendLine($"# {chapterTitle}");
            template.AppendLine();
            template.AppendLine($"**作品：** {bookTitle}");
            template.AppendLine($"**章节：** 第{chapterNumber}章");
            template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
            template.AppendLine();
            template.AppendLine("---");
            template.AppendLine();
            template.AppendLine("## 章节内容");
            template.AppendLine();
            template.AppendLine("（在此处开始编写章节内容...）");
            template.AppendLine();
            template.AppendLine();
            template.AppendLine("---");
            template.AppendLine();
            template.AppendLine("## 创作备注");
            template.AppendLine();
            template.AppendLine("- **字数目标：** 6500字");
            template.AppendLine("- **情节要点：** ");
            template.AppendLine("- **角色发展：** ");
            template.AppendLine("- **伏笔设置：** ");
            template.AppendLine("- **下章预告：** ");

            return template.ToString();
        }

        /// <summary>
        /// 获取大纲模板
        /// </summary>
        public string GetOutlineTemplate(string bookTitle, string outlineType)
        {
            var template = new StringBuilder();
            
            switch (outlineType.ToLower())
            {
                case "overall":
                    template.AppendLine($"# {bookTitle} - 总体大纲");
                    template.AppendLine();
                    template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
                    template.AppendLine();
                    template.AppendLine("## 作品基本信息");
                    template.AppendLine("- **作品类型：** ");
                    template.AppendLine("- **预计字数：** ");
                    template.AppendLine("- **预计章节数：** ");
                    template.AppendLine("- **更新频率：** ");
                    template.AppendLine();
                    template.AppendLine("## 故事梗概");
                    template.AppendLine("（简述整个故事的核心情节...）");
                    template.AppendLine();
                    template.AppendLine("## 主要角色");
                    template.AppendLine("### 主角");
                    template.AppendLine("- **姓名：** ");
                    template.AppendLine("- **性格：** ");
                    template.AppendLine("- **背景：** ");
                    template.AppendLine();
                    template.AppendLine("### 配角");
                    template.AppendLine("（列出重要配角...）");
                    template.AppendLine();
                    template.AppendLine("## 分卷规划");
                    template.AppendLine("### 第一卷：");
                    template.AppendLine("- **卷名：** ");
                    template.AppendLine("- **章节范围：** ");
                    template.AppendLine("- **主要情节：** ");
                    break;
                    
                case "volume":
                    template.AppendLine($"# {bookTitle} - 卷宗大纲");
                    template.AppendLine();
                    template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
                    template.AppendLine();
                    template.AppendLine("## 卷宗信息");
                    template.AppendLine("- **卷数：** ");
                    template.AppendLine("- **卷名：** ");
                    template.AppendLine("- **章节范围：** ");
                    template.AppendLine("- **预计字数：** ");
                    template.AppendLine();
                    template.AppendLine("## 卷宗主题");
                    template.AppendLine("（本卷的核心主题和发展方向...）");
                    template.AppendLine();
                    template.AppendLine("## 主要情节");
                    template.AppendLine("（详细描述本卷的主要情节发展...）");
                    template.AppendLine();
                    template.AppendLine("## 角色发展");
                    template.AppendLine("（本卷中主要角色的成长和变化...）");
                    break;
                    
                case "chapter":
                    template.AppendLine($"# {bookTitle} - 章节细纲");
                    template.AppendLine();
                    template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
                    template.AppendLine();
                    template.AppendLine("## 章节信息");
                    template.AppendLine("- **章节号：** ");
                    template.AppendLine("- **章节名：** ");
                    template.AppendLine("- **所属卷宗：** ");
                    template.AppendLine("- **预计字数：** 6500字");
                    template.AppendLine();
                    template.AppendLine("## 情节要点");
                    template.AppendLine("1. **开场：** ");
                    template.AppendLine("2. **发展：** ");
                    template.AppendLine("3. **高潮：** ");
                    template.AppendLine("4. **结尾：** ");
                    template.AppendLine();
                    template.AppendLine("## 角色出场");
                    template.AppendLine("- **主要角色：** ");
                    template.AppendLine("- **次要角色：** ");
                    template.AppendLine();
                    template.AppendLine("## 场景设定");
                    template.AppendLine("- **主要场景：** ");
                    template.AppendLine("- **时间：** ");
                    template.AppendLine("- **环境描述：** ");
                    break;
            }

            return template.ToString();
        }

        /// <summary>
        /// 获取角色设定模板
        /// </summary>
        public string GetCharacterTemplate(string characterName)
        {
            var template = new StringBuilder();
            template.AppendLine($"# 角色设定 - {characterName}");
            template.AppendLine();
            template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
            template.AppendLine();
            template.AppendLine("## 基本信息");
            template.AppendLine($"- **姓名：** {characterName}");
            template.AppendLine("- **性别：** ");
            template.AppendLine("- **年龄：** ");
            template.AppendLine("- **身份：** ");
            template.AppendLine("- **职业：** ");
            template.AppendLine();
            template.AppendLine("## 外貌特征");
            template.AppendLine("- **身高：** ");
            template.AppendLine("- **体型：** ");
            template.AppendLine("- **发色：** ");
            template.AppendLine("- **眼色：** ");
            template.AppendLine("- **特殊标记：** ");
            template.AppendLine();
            template.AppendLine("## 性格特点");
            template.AppendLine("- **主要性格：** ");
            template.AppendLine("- **优点：** ");
            template.AppendLine("- **缺点：** ");
            template.AppendLine("- **习惯动作：** ");
            template.AppendLine("- **口头禅：** ");
            template.AppendLine();
            template.AppendLine("## 背景故事");
            template.AppendLine("- **出生地：** ");
            template.AppendLine("- **家庭背景：** ");
            template.AppendLine("- **成长经历：** ");
            template.AppendLine("- **重要事件：** ");
            template.AppendLine();
            template.AppendLine("## 能力设定");
            template.AppendLine("- **特殊能力：** ");
            template.AppendLine("- **技能专长：** ");
            template.AppendLine("- **武器装备：** ");
            template.AppendLine("- **实力等级：** ");
            template.AppendLine();
            template.AppendLine("## 人际关系");
            template.AppendLine("- **家人：** ");
            template.AppendLine("- **朋友：** ");
            template.AppendLine("- **敌人：** ");
            template.AppendLine("- **恋人：** ");
            template.AppendLine();
            template.AppendLine("## 角色发展");
            template.AppendLine("- **初期状态：** ");
            template.AppendLine("- **成长目标：** ");
            template.AppendLine("- **发展轨迹：** ");
            template.AppendLine("- **最终状态：** ");

            return template.ToString();
        }

        /// <summary>
        /// 获取世界设定模板
        /// </summary>
        public string GetWorldSettingTemplate(string settingType)
        {
            var template = new StringBuilder();
            
            switch (settingType.ToLower())
            {
                case "worldview":
                    template.AppendLine("# 世界观设定");
                    template.AppendLine();
                    template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
                    template.AppendLine();
                    template.AppendLine("## 世界基本信息");
                    template.AppendLine("- **世界名称：** ");
                    template.AppendLine("- **世界类型：** ");
                    template.AppendLine("- **时代背景：** ");
                    template.AppendLine("- **科技水平：** ");
                    template.AppendLine();
                    template.AppendLine("## 地理环境");
                    template.AppendLine("- **大陆分布：** ");
                    template.AppendLine("- **主要国家：** ");
                    template.AppendLine("- **重要城市：** ");
                    template.AppendLine("- **特殊地形：** ");
                    template.AppendLine();
                    template.AppendLine("## 种族设定");
                    template.AppendLine("- **人类：** ");
                    template.AppendLine("- **其他种族：** ");
                    template.AppendLine();
                    template.AppendLine("## 力量体系");
                    template.AppendLine("- **修炼体系：** ");
                    template.AppendLine("- **等级划分：** ");
                    template.AppendLine("- **特殊能力：** ");
                    break;
                    
                case "cultivation":
                    template.AppendLine("# 修炼体系设定");
                    template.AppendLine();
                    template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
                    template.AppendLine();
                    template.AppendLine("## 修炼基础");
                    template.AppendLine("- **修炼原理：** ");
                    template.AppendLine("- **能量来源：** ");
                    template.AppendLine("- **修炼条件：** ");
                    template.AppendLine();
                    template.AppendLine("## 境界划分");
                    template.AppendLine("### 初级境界");
                    template.AppendLine("1. **境界名称：** ");
                    template.AppendLine("   - **特征：** ");
                    template.AppendLine("   - **能力：** ");
                    template.AppendLine();
                    template.AppendLine("## 修炼功法");
                    template.AppendLine("- **基础功法：** ");
                    template.AppendLine("- **高级功法：** ");
                    template.AppendLine("- **禁忌功法：** ");
                    break;
            }

            return template.ToString();
        }

        /// <summary>
        /// 获取时间线模板
        /// </summary>
        public string GetTimelineTemplate(string bookTitle, int? volumeNumber = null)
        {
            var template = new StringBuilder();
            
            if (volumeNumber.HasValue)
            {
                template.AppendLine($"# {bookTitle} - 第{volumeNumber}卷时间线");
            }
            else
            {
                template.AppendLine($"# {bookTitle} - 总时间线");
            }
            
            template.AppendLine();
            template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
            template.AppendLine();
            template.AppendLine("## 时间线说明");
            template.AppendLine("本文档记录故事发展的时间顺序和重要事件。");
            template.AppendLine();
            template.AppendLine("## 主要事件");
            template.AppendLine();
            template.AppendLine("### 第1章");
            template.AppendLine("- **时间：** ");
            template.AppendLine("- **地点：** ");
            template.AppendLine("- **主要事件：** ");
            template.AppendLine("- **参与角色：** ");
            template.AppendLine("- **重要影响：** ");
            template.AppendLine();
            template.AppendLine("## 角色状态变化");
            template.AppendLine("### 主角");
            template.AppendLine("- **初始状态：** ");
            template.AppendLine("- **重要变化：** ");
            template.AppendLine("- **当前状态：** ");
            template.AppendLine();
            template.AppendLine("## 世界状态变化");
            template.AppendLine("- **政治局势：** ");
            template.AppendLine("- **重大事件：** ");
            template.AppendLine("- **环境变化：** ");

            return template.ToString();
        }

        /// <summary>
        /// 获取项目说明模板
        /// </summary>
        public string GetProjectReadmeTemplate(string projectName, string projectType)
        {
            var template = new StringBuilder();
            template.AppendLine($"# {projectName}");
            template.AppendLine();
            template.AppendLine($"**项目类型：** {projectType}");
            template.AppendLine($"**创建时间：** {DateTime.Now:yyyy年MM月dd日 HH:mm}");
            template.AppendLine();
            template.AppendLine("## 项目简介");
            template.AppendLine("（在此处添加项目的简要描述...）");
            template.AppendLine();
            template.AppendLine("## 文件夹结构");
            template.AppendLine("```");
            template.AppendLine("├── 文档/          # 项目文档");
            template.AppendLine("├── 资源/          # 项目资源文件");
            template.AppendLine("├── 备份/          # 备份文件");
            template.AppendLine("└── 导出/          # 导出文件");
            
            if (projectType == "Novel")
            {
                template.AppendLine("├── 章节/          # 小说章节");
                template.AppendLine("├── 角色设定/      # 角色设定文档");
                template.AppendLine("├── 大纲/          # 故事大纲");
                template.AppendLine("├── 世界设定/      # 世界观设定");
                template.AppendLine("├── 时间线/        # 故事时间线");
                template.AppendLine("├── 情节设定/      # 情节设定");
                template.AppendLine("├── 地图/          # 世界地图");
                template.AppendLine("├── 势力设定/      # 势力关系");
                template.AppendLine("├── 物品设定/      # 物品道具");
                template.AppendLine("├── 技能设定/      # 技能体系");
                template.AppendLine("└── 关系网络/      # 人物关系");
            }
            
            template.AppendLine("```");
            template.AppendLine();
            template.AppendLine("## 使用说明");
            template.AppendLine("1. 在对应文件夹中创建和编辑文档");
            template.AppendLine("2. 使用AI助手功能辅助创作");
            template.AppendLine("3. 定期备份重要文件");
            template.AppendLine();
            template.AppendLine("## 注意事项");
            template.AppendLine("- 请保持文件命名的规范性");
            template.AppendLine("- 建议定期保存和备份");
            template.AppendLine("- 可以使用版本控制管理文档变更");

            return template.ToString();
        }
    }
}

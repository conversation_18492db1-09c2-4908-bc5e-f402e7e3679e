<Window x:Class="DocumentCreationSystem.Views.ToolStatisticsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="工具使用统计" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           VerticalAlignment="Center" 
                                           Width="24" Height="24"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="工具使用统计" 
                             FontSize="18" 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 统计内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="24">
                <!-- 概览统计 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="概览统计" FontSize="16" FontWeight="Medium" Margin="0,0,0,16"/>
                        
                        <UniformGrid Columns="4" Rows="1">
                            <StackPanel Margin="8">
                                <TextBlock Text="总使用次数" FontWeight="Medium" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalUsageText"
                                         Text="0"
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Margin="8">
                                <TextBlock Text="总执行时间" FontWeight="Medium" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TotalTimeText"
                                         Text="0s"
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Margin="8">
                                <TextBlock Text="成功率" FontWeight="Medium" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="SuccessRateText"
                                         Text="0%"
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Margin="8">
                                <TextBlock Text="活跃工具数" FontWeight="Medium" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="ActiveToolsText"
                                         Text="0"
                                         FontSize="24"
                                         FontWeight="Bold"
                                         Foreground="{DynamicResource PrimaryHueMidBrush}"
                                         HorizontalAlignment="Center"/>
                            </StackPanel>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 最常用工具 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="最常用工具 (Top 5)" FontSize="16" FontWeight="Medium" Margin="0,0,0,16"/>
                        <ListBox x:Name="MostUsedToolsListBox" 
                               Style="{StaticResource MaterialDesignListBox}"
                               MaxHeight="200"/>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 工具使用详情 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="工具使用详情" FontSize="16" FontWeight="Medium" Margin="0,0,0,16"/>
                        <DataGrid x:Name="ToolUsageDataGrid"
                                Style="{StaticResource MaterialDesignDataGrid}"
                                AutoGenerateColumns="False"
                                IsReadOnly="True"
                                MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="工具ID" Binding="{Binding ToolId}" Width="150"/>
                                <DataGridTextColumn Header="使用次数" Binding="{Binding UsageCount}" Width="100"/>
                                <DataGridTextColumn Header="总执行时间" Binding="{Binding TotalTime}" Width="120"/>
                                <DataGridTextColumn Header="平均执行时间" Binding="{Binding AverageTime}" Width="120"/>
                                <DataGridTextColumn Header="最后使用" Binding="{Binding LastUsed}" Width="150"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- 统计信息 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16">
                        <TextBlock Text="统计信息" FontSize="16" FontWeight="Medium" Margin="0,0,0,16"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" Margin="0,0,16,0">
                                <TextBlock Text="统计期间" FontWeight="Medium" Margin="0,0,0,8"/>
                                <TextBlock x:Name="PeriodText" Text="最近30天"/>
                                
                                <TextBlock Text="项目ID" FontWeight="Medium" Margin="0,16,0,8"/>
                                <TextBlock x:Name="ProjectIdText" Text="N/A"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="生成时间" FontWeight="Medium" Margin="0,0,0,8"/>
                                <TextBlock x:Name="GeneratedAtText" Text="N/A"/>
                                
                                <TextBlock Text="数据版本" FontWeight="Medium" Margin="0,16,0,8"/>
                                <TextBlock Text="1.0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>
        
        <!-- 按钮栏 -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,16,0,0">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Right" 
                      Margin="24,16">
                <Button Content="导出统计" 
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="ExportStatistics_Click"/>
                <Button Content="刷新" 
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Margin="0,0,8,0"
                      Click="RefreshStatistics_Click"/>
                <Button Content="关闭" 
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="Close_Click"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>

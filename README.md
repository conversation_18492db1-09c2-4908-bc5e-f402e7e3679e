# 文档管理及AI创作系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/)
[![Platform](https://img.shields.io/badge/Platform-Windows-lightgrey.svg)](https://www.microsoft.com/windows/)

一个基于 .NET 8.0 和 WPF 的现代化文档管理和AI辅助创作系统，集成多种AI模型，提供智能写作、项目管理和文档编辑功能。

由于接下来的主要精力会投入到本职工作上，不会有太多时间投入到该项目上，在小说创作功能上还有很多BUG没有处理，再例如在论文创作过程中对各种模型的思维链没有进行较好的过滤，还有其它一些还没来得及测试的功能和BUG，就只能交给有缘人处理了。

## ✨ 主要特性

### 🎨 现代化界面
- **Material Design** 风格的用户界面
- **响应式布局** 支持窗口大小调整
- **三栏布局** 项目导航 + 文档编辑 + AI助手
- **深色/浅色主题** 支持

### 📝 文档编辑
- **富文本编辑器** 基于AvalonEdit
- **多格式支持** .docx、.txt、.md等格式
- **实时保存** 自动保存和手动保存
- **语法高亮** 支持多种文档格式

### 🤖 AI智能助手
- **多AI平台支持**
  - 智谱AI (GLM系列模型)
  - DeepSeek (deepseek-chat)
  - LM Studio (本地模型)
  - Ollama (本地模型)
- **智能创作功能**
  - 文本润色和扩写
  - 按要求创作内容
  - 从光标处续写
  - 一致性检查
- **小说创作工具**
  - 大纲生成
  - 章节创作
  - 一键写书
  - 6500字章节自动分割

### 📁 项目管理
- **项目文件夹管理** 创建和组织项目
- **历史项目** 快速访问最近项目
- **文件操作** 复制、粘贴、删除等
- **项目工具** 专用的项目管理工具

### 🔧 高级功能
- **向量化搜索** 基于BGE-M3模型的文档检索
- **模型配置** 灵活的AI模型配置界面
- **参数调节** 创意度、最大字数等参数
- **进度跟踪** 创作进度可视化

### 🔒 隐私安全
- **完全本地化** 所有数据存储在本地，确保隐私安全
- **开源技术栈** 避免商业版权风险
- **离线运行** 支持完全离线的创作环境

## 🚀 快速开始

### 系统要求
- Windows 10/11
- .NET 8.0 Runtime
- 4GB+ RAM
- 2GB+ 可用磁盘空间

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/your-username/document-creation-system.git
cd document-creation-system
```

2. **安装依赖**
```bash
dotnet restore
```

3. **编译项目**
```bash
dotnet build
```

4. **运行应用**
```bash
dotnet run --project DocumentCreationSystem
```

### 配置AI服务

首次运行时，请通过 **AI模型 → 模型配置** 菜单配置您的AI服务：

#### 智谱AI配置
1. 获取API密钥：访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 选择模型：GLM-4-Flash、GLM-4V-Flash等
3. 输入API密钥和基础URL

#### DeepSeek配置
1. 获取API密钥：访问 [DeepSeek平台](https://platform.deepseek.com/)
2. 使用模型：deepseek-chat
3. 配置OpenAI兼容格式

#### 本地模型配置
- **LM Studio**：启动LM Studio服务器，配置本地地址
- **Ollama**：安装Ollama并下载模型，系统会自动检测

## 📖 使用指南

### 创建项目
1. 点击 **文件 → 新建项目**
2. 输入项目名称和描述
3. 选择项目类型和保存位置
4. 开始创作！

### AI辅助写作
1. **文本润色**：选中文本，点击"润色选中文本"
2. **内容扩写**：选中要扩写的内容，点击"扩写内容"
3. **续写功能**：将光标放在要续写的位置，点击"从光标处续写"
4. **按要求创作**：点击"按要求创作"，输入创作要求

### 小说创作
1. **生成大纲**：点击"生成大纲"，输入小说主题
2. **创作章节**：基于大纲创作具体章节内容
3. **一键写书**：自动化的小说创作流程
4. **进度跟踪**：查看创作进度和字数统计

## 🏗️ 项目结构

```
DocumentCreationSystem/
├── Controls/           # 自定义控件
├── Converters/         # 数据转换器
├── Models/            # 数据模型
├── Services/          # 业务服务
│   ├── AI/           # AI服务实现
│   ├── Data/         # 数据存储服务
│   └── Project/      # 项目管理服务
├── ViewModels/        # 视图模型
├── Views/            # 窗口和对话框
└── Resources/        # 资源文件
```

## 🔧 技术栈

- **框架**：.NET 8.0, WPF
- **UI库**：Material Design In XAML
- **编辑器**：AvalonEdit
- **依赖注入**：Microsoft.Extensions.DependencyInjection
- **日志**：Microsoft.Extensions.Logging
- **配置**：Microsoft.Extensions.Configuration
- **文档处理**：DocumentFormat.OpenXml
- **数据存储**：Entity Framework Core + SQLite
- **HTTP客户端**：System.Net.Http
- **JSON处理**：Newtonsoft.Json

## 🎯 核心功能

### 1. 项目管理
- 创建和管理写作项目
- 支持多种项目类型
- 项目配置和元数据管理

### 2. 文档管理
- 支持多种文档格式 (.docx, .txt, .md)
- 实时文件监控和同步
- 版本控制和备份

### 3. AI辅助创作
- 智能文本生成
- 内容润色和优化
- 创意扩展和补充

### 4. 小说专业功能
- 全书大纲规划
- 章节结构管理
- 角色设定跟踪
- 情节一致性检查

### 5. 智能搜索
- 基于语义的内容搜索
- 相关内容推荐
- 上下文关联分析

## 📸 界面预览

### 主界面
- 三栏布局：项目导航 + 文档编辑 + AI助手
- Material Design风格界面
- 响应式布局设计

### AI配置界面
- 多平台AI模型配置
- 参数调节和测试
- 模型切换和管理

### 项目管理
- 项目文件夹创建和管理
- 历史项目快速访问
- 文件操作和组织

## 🚀 开发指南

### 代码结构
```csharp
// AI服务使用示例
var aiService = serviceProvider.GetRequiredService<IAIService>();
var result = await aiService.GenerateTextAsync("创作一个科幻小说开头");

// 文档服务使用示例
var documentService = serviceProvider.GetRequiredService<IDocumentService>();
await documentService.SaveDocumentAsync(filePath, content);

// 向量服务使用示例
var vectorService = serviceProvider.GetRequiredService<IVectorService>();
var searchResults = await vectorService.SearchSimilarAsync(query, topK: 5);
```

### 扩展开发
- 实现自定义AI服务提供者
- 添加新的文档格式支持
- 开发自定义项目工具

## 🔮 未来规划

### 短期目标 (1-3个月)
- [x] 完善WPF用户界面
- [ ] 集成真实的向量模型
- [ ] 添加更多AI服务提供商
- [ ] 完善错误处理和日志

### 中期目标 (3-6个月)
- [ ] Web界面开发
- [ ] 云端同步功能
- [ ] 协作编辑支持
- [ ] 插件系统

### 长期目标 (6-12个月)
- [ ] 移动端应用
- [ ] 多语言支持
- [ ] 高级AI功能
- [ ] 商业化版本

## 🐛 已知问题

- 部分AI服务可能需要网络连接
- 大文档处理时可能存在性能问题
- 某些功能仍在开发中

## 📋 更新日志

### v1.0.0 (2025-01-09)
- ✅ 完整的WPF GUI界面
- ✅ 多AI平台集成（智谱AI、DeepSeek、LM Studio、Ollama）
- ✅ 项目管理和文档编辑功能
- ✅ AI辅助创作工具
- ✅ 小说创作专用功能
- ✅ 向量化搜索支持

## 🤝 贡献

欢迎贡献代码、报告问题或提出建议！

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 贡献指南
- 遵循现有的代码风格
- 添加适当的测试
- 更新相关文档
- 确保所有测试通过

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

-着重感谢智谱平台开放的免费API，在本软件的创建初期起到了很大的帮助。

## 📞 支持

如果您遇到问题或有任何建议，请留言并摆好姿势，等待偶遇的大佬来解决。

## 🌟 Star History

如果这个项目对您有帮助，请给我们一个 ⭐！

---

**让AI成为您创作路上的最佳伙伴！** 🚀✨

*Built with ❤️ by the Document Creation System Team*

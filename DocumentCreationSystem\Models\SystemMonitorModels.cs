namespace DocumentCreationSystem.Models;

/// <summary>
/// 当前AI模型信息
/// </summary>
public class CurrentModelInfo
{
    /// <summary>
    /// 平台名称（Ollama、LM Studio、智谱AI、DeepSeek）
    /// </summary>
    public string Platform { get; set; } = string.Empty;

    /// <summary>
    /// 模型名称
    /// </summary>
    public string ModelName { get; set; } = string.Empty;

    /// <summary>
    /// 模型状态（在线、离线、连接中）
    /// </summary>
    public string Status { get; set; } = "离线";

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; } = false;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

/// <summary>
/// 系统资源信息
/// </summary>
public class SystemResourceInfo
{
    /// <summary>
    /// CPU信息
    /// </summary>
    public CpuInfo Cpu { get; set; } = new();

    /// <summary>
    /// 内存信息
    /// </summary>
    public MemoryInfo Memory { get; set; } = new();

    /// <summary>
    /// GPU信息
    /// </summary>
    public GpuInfo Gpu { get; set; } = new();

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdated { get; set; } = DateTime.Now;
}

/// <summary>
/// CPU使用信息
/// </summary>
public class CpuInfo
{
    /// <summary>
    /// CPU使用率（百分比）
    /// </summary>
    public double UsagePercentage { get; set; } = 0.0;

    /// <summary>
    /// CPU核心数
    /// </summary>
    public int CoreCount { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// CPU名称
    /// </summary>
    public string Name { get; set; } = "未知";

    /// <summary>
    /// 温度（如果可获取）
    /// </summary>
    public double? Temperature { get; set; }
}

/// <summary>
/// 内存使用信息
/// </summary>
public class MemoryInfo
{
    /// <summary>
    /// 总内存（MB）
    /// </summary>
    public long TotalMemoryMB { get; set; } = 0;

    /// <summary>
    /// 已使用内存（MB）
    /// </summary>
    public long UsedMemoryMB { get; set; } = 0;

    /// <summary>
    /// 可用内存（MB）
    /// </summary>
    public long AvailableMemoryMB { get; set; } = 0;

    /// <summary>
    /// 内存使用率（百分比）
    /// </summary>
    public double UsagePercentage { get; set; } = 0.0;

    /// <summary>
    /// 当前进程内存使用（MB）
    /// </summary>
    public long ProcessMemoryMB { get; set; } = 0;

    /// <summary>
    /// 总内存（别名，兼容性）
    /// </summary>
    public long Total => TotalMemoryMB;

    /// <summary>
    /// 可用内存（别名，兼容性）
    /// </summary>
    public long Available => AvailableMemoryMB;
}

/// <summary>
/// GPU使用信息
/// </summary>
public class GpuInfo
{
    /// <summary>
    /// GPU名称
    /// </summary>
    public string Name { get; set; } = "未检测到GPU";

    /// <summary>
    /// GPU使用率（百分比）
    /// </summary>
    public double UsagePercentage { get; set; } = 0.0;

    /// <summary>
    /// GPU内存总量（MB）
    /// </summary>
    public long TotalMemoryMB { get; set; } = 0;

    /// <summary>
    /// GPU已使用内存（MB）
    /// </summary>
    public long UsedMemoryMB { get; set; } = 0;

    /// <summary>
    /// GPU温度（如果可获取）
    /// </summary>
    public double? Temperature { get; set; }

    /// <summary>
    /// 是否可用
    /// </summary>
    public bool IsAvailable { get; set; } = false;

    /// <summary>
    /// 驱动版本
    /// </summary>
    public string DriverVersion { get; set; } = "未知";
}

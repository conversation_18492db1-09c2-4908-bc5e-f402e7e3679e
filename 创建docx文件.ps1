# 创建一个简单的docx文件用于测试
$word = New-Object -ComObject Word.Application
$word.Visible = $false

try {
    $doc = $word.Documents.Add()
    $selection = $word.Selection
    
    $selection.TypeText("这是一个测试的Word文档")
    $selection.TypeParagraph()
    $selection.TypeText("用于验证项目文件夹扫描功能")
    $selection.TypeParagraph()
    $selection.TypeText("文档包含中文内容，测试docx文件在项目导航中的显示。")
    
    $docPath = Join-Path (Get-Location) "测试文档.docx"
    $doc.SaveAs([ref]$docPath)
    $doc.Close()
    
    Write-Host "成功创建docx文件: $docPath"
}
catch {
    Write-Host "创建docx文件失败: $($_.Exception.Message)"
}
finally {
    $word.Quit()
    [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
}

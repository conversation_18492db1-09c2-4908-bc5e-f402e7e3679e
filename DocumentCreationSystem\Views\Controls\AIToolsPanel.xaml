<UserControl x:Class="DocumentCreationSystem.Views.Controls.AIToolsPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="400">
    
    <UserControl.Resources>
        <Style x:Key="ToolCategoryHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#2196F3"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>
        
        <Style x:Key="ToolButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="#2196F3" Padding="15,10">
            <TextBlock Text="AI工具箱" FontSize="16" FontWeight="Bold" Foreground="White"/>
        </Border>

        <Border Grid.Row="1" Background="#F8F9FA" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="SearchTextBox" 
                         Grid.Column="0"
                         Padding="8"
                         BorderBrush="#E0E0E0"
                         BorderThickness="1"
                         FontSize="12"/>
                
                <Button x:Name="SearchButton" 
                        Grid.Column="1" 
                        Content="搜索" 
                        Padding="8"
                        Margin="5,0,0,0"
                        Background="#2196F3"
                        Foreground="White"
                        BorderThickness="0"
                        Click="SearchButton_Click"/>
            </Grid>
        </Border>

        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" Padding="10">
            <StackPanel x:Name="ToolsContainer">
                <TextBlock Text="代码搜索" Style="{StaticResource ToolCategoryHeaderStyle}"/>
                
                <Button x:Name="SearchCodebaseButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="search-codebase"
                        Content="代码库搜索"/>
                
                <Button x:Name="SearchRegexButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="search-by-regex"
                        Content="正则表达式搜索"/>

                <TextBlock Text="文件操作" Style="{StaticResource ToolCategoryHeaderStyle}"/>
                
                <Button x:Name="ViewFilesButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="view-files"
                        Content="批量查看文件"/>
                
                <Button x:Name="ListDirButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="list-dir"
                        Content="目录结构浏览"/>
                
                <Button x:Name="WriteFileButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="write-to-file"
                        Content="文件创建/覆写"/>
                
                <Button x:Name="UpdateFileButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="update-file"
                        Content="文件编辑"/>

                <TextBlock Text="开发辅助" Style="{StaticResource ToolCategoryHeaderStyle}"/>
                
                <Button x:Name="RunCommandButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="run-command"
                        Content="命令行执行"/>
                
                <Button x:Name="OpenPreviewButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="open-preview"
                        Content="本地服务预览"/>

                <TextBlock Text="网络工具" Style="{StaticResource ToolCategoryHeaderStyle}"/>
                
                <Button x:Name="WebSearchButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="web-search"
                        Content="联网搜索"/>

                <TextBlock Text="MCP集成" Style="{StaticResource ToolCategoryHeaderStyle}"/>
                
                <Button x:Name="ExcelAutomationButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="excel-automation"
                        Content="Excel自动化"/>
                
                <Button x:Name="BlenderAutomationButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="blender-automation"
                        Content="Blender三维建模"/>
                
                <Button x:Name="BrowserAutomationButton" 
                        Style="{StaticResource ToolButtonStyle}"
                        Click="ToolButton_Click"
                        Tag="browser-automation"
                        Content="浏览器自动化"/>
            </StackPanel>
        </ScrollViewer>

        <Border Grid.Row="3" Background="#F8F9FA" Padding="10,5">
            <TextBlock x:Name="StatusText" 
                       Text="就绪" 
                       FontSize="10" 
                       Foreground="#666"
                       HorizontalAlignment="Center"/>
        </Border>
    </Grid>
</UserControl>

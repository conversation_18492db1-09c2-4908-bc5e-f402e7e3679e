namespace DocumentCreationSystem.Models.Reasoning
{
    /// <summary>
    /// 推理链 - 表示推理过程的步骤序列
    /// </summary>
    public class ReasoningChain
    {
        /// <summary>
        /// 推理链ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 推理步骤列表
        /// </summary>
        public List<ReasoningStep> Steps { get; set; } = new();

        /// <summary>
        /// 起始事实
        /// </summary>
        public List<ReasoningFact> InitialFacts { get; set; } = new();

        /// <summary>
        /// 目标事实
        /// </summary>
        public ReasoningFact? Goal { get; set; }

        /// <summary>
        /// 推理链的总体置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 推理链长度
        /// </summary>
        public int Length => Steps.Count;

        /// <summary>
        /// 推理链是否完整
        /// </summary>
        public bool IsComplete { get; set; } = false;

        /// <summary>
        /// 推理链创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 推理链的复杂度评分
        /// </summary>
        public double ComplexityScore { get; set; } = 0.0;

        /// <summary>
        /// 推理链的可信度评分
        /// </summary>
        public double CredibilityScore { get; set; } = 1.0;
    }

    /// <summary>
    /// 推理步骤 - 推理链中的单个推理步骤
    /// </summary>
    public class ReasoningStep
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 步骤序号
        /// </summary>
        public int StepNumber { get; set; } = 0;

        /// <summary>
        /// 使用的规则
        /// </summary>
        public ReasoningRule? Rule { get; set; }

        /// <summary>
        /// 输入事实
        /// </summary>
        public List<ReasoningFact> InputFacts { get; set; } = new();

        /// <summary>
        /// 输出事实
        /// </summary>
        public List<ReasoningFact> OutputFacts { get; set; } = new();

        /// <summary>
        /// 步骤类型
        /// </summary>
        public StepType Type { get; set; } = StepType.RuleApplication;

        /// <summary>
        /// 步骤置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 步骤描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 步骤执行时间
        /// </summary>
        public DateTime ExecutedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 步骤的上下文信息
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 推理步骤类型
    /// </summary>
    public enum StepType
    {
        /// <summary>
        /// 规则应用
        /// </summary>
        RuleApplication,

        /// <summary>
        /// 事实匹配
        /// </summary>
        FactMatching,

        /// <summary>
        /// 假设生成
        /// </summary>
        HypothesisGeneration,

        /// <summary>
        /// 冲突解决
        /// </summary>
        ConflictResolution,

        /// <summary>
        /// 不确定性处理
        /// </summary>
        UncertaintyHandling,

        /// <summary>
        /// 默认推理
        /// </summary>
        DefaultReasoning
    }

    /// <summary>
    /// 一致性检查结果
    /// </summary>
    public class ConsistencyCheckResult
    {
        /// <summary>
        /// 检查ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 是否一致
        /// </summary>
        public bool IsConsistent { get; set; } = true;

        /// <summary>
        /// 冲突列表
        /// </summary>
        public List<FactConflict> Conflicts { get; set; } = new();

        /// <summary>
        /// 检查的事实数量
        /// </summary>
        public int FactsChecked { get; set; } = 0;

        /// <summary>
        /// 检查时间（毫秒）
        /// </summary>
        public long CheckTimeMs { get; set; } = 0;

        /// <summary>
        /// 检查时间戳
        /// </summary>
        public DateTime CheckedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 一致性评分 (0.0 - 1.0)
        /// </summary>
        public double ConsistencyScore { get; set; } = 1.0;
    }

    /// <summary>
    /// 事实冲突
    /// </summary>
    public class FactConflict
    {
        /// <summary>
        /// 冲突ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 冲突的事实列表
        /// </summary>
        public List<ReasoningFact> ConflictingFacts { get; set; } = new();

        /// <summary>
        /// 冲突类型
        /// </summary>
        public ConflictType Type { get; set; } = ConflictType.Contradiction;

        /// <summary>
        /// 冲突描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 冲突严重程度 (0.0 - 1.0)
        /// </summary>
        public double Severity { get; set; } = 1.0;

        /// <summary>
        /// 建议的解决方案
        /// </summary>
        public List<ConflictResolution> SuggestedResolutions { get; set; } = new();
    }

    /// <summary>
    /// 冲突类型
    /// </summary>
    public enum ConflictType
    {
        /// <summary>
        /// 直接矛盾
        /// </summary>
        Contradiction,

        /// <summary>
        /// 不一致性
        /// </summary>
        Inconsistency,

        /// <summary>
        /// 循环依赖
        /// </summary>
        CircularDependency,

        /// <summary>
        /// 类型冲突
        /// </summary>
        TypeConflict,

        /// <summary>
        /// 时间冲突
        /// </summary>
        TemporalConflict
    }

    /// <summary>
    /// 冲突解决方案
    /// </summary>
    public class ConflictResolution
    {
        /// <summary>
        /// 解决方案ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 解决方案类型
        /// </summary>
        public ResolutionType Type { get; set; } = ResolutionType.RemoveFact;

        /// <summary>
        /// 解决方案描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 要删除的事实ID列表
        /// </summary>
        public List<string> FactsToRemove { get; set; } = new();

        /// <summary>
        /// 要修改的事实ID列表
        /// </summary>
        public List<string> FactsToModify { get; set; } = new();

        /// <summary>
        /// 解决方案的置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 解决方案的成本（复杂度）
        /// </summary>
        public double Cost { get; set; } = 1.0;
    }

    /// <summary>
    /// 解决方案类型
    /// </summary>
    public enum ResolutionType
    {
        /// <summary>
        /// 删除事实
        /// </summary>
        RemoveFact,

        /// <summary>
        /// 修改事实
        /// </summary>
        ModifyFact,

        /// <summary>
        /// 降低置信度
        /// </summary>
        ReduceConfidence,

        /// <summary>
        /// 添加约束
        /// </summary>
        AddConstraint,

        /// <summary>
        /// 时间分离
        /// </summary>
        TemporalSeparation
    }

    /// <summary>
    /// 推理解释
    /// </summary>
    public class ReasoningExplanation
    {
        /// <summary>
        /// 解释ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 推理结果ID
        /// </summary>
        public string ReasoningResultId { get; set; } = string.Empty;

        /// <summary>
        /// 自然语言解释
        /// </summary>
        public string NaturalLanguageExplanation { get; set; } = string.Empty;

        /// <summary>
        /// 步骤解释列表
        /// </summary>
        public List<StepExplanation> StepExplanations { get; set; } = new();

        /// <summary>
        /// 关键决策点
        /// </summary>
        public List<DecisionPoint> KeyDecisionPoints { get; set; } = new();

        /// <summary>
        /// 解释的详细程度
        /// </summary>
        public ExplanationLevel Level { get; set; } = ExplanationLevel.Medium;

        /// <summary>
        /// 解释生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 步骤解释
    /// </summary>
    public class StepExplanation
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public string StepId { get; set; } = string.Empty;

        /// <summary>
        /// 步骤解释文本
        /// </summary>
        public string Explanation { get; set; } = string.Empty;

        /// <summary>
        /// 使用的规则解释
        /// </summary>
        public string? RuleExplanation { get; set; }

        /// <summary>
        /// 为什么选择这个规则
        /// </summary>
        public string? RuleSelectionReason { get; set; }
    }

    /// <summary>
    /// 决策点
    /// </summary>
    public class DecisionPoint
    {
        /// <summary>
        /// 决策点ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 决策描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 可选方案
        /// </summary>
        public List<string> Alternatives { get; set; } = new();

        /// <summary>
        /// 选择的方案
        /// </summary>
        public string SelectedAlternative { get; set; } = string.Empty;

        /// <summary>
        /// 选择理由
        /// </summary>
        public string SelectionReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 解释详细程度
    /// </summary>
    public enum ExplanationLevel
    {
        /// <summary>
        /// 简要解释
        /// </summary>
        Brief,

        /// <summary>
        /// 中等详细
        /// </summary>
        Medium,

        /// <summary>
        /// 详细解释
        /// </summary>
        Detailed,

        /// <summary>
        /// 专家级解释
        /// </summary>
        Expert
    }

    /// <summary>
    /// 推理统计信息
    /// </summary>
    public class ReasoningStatistics
    {
        /// <summary>
        /// 统计ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 总事实数量
        /// </summary>
        public int TotalFacts { get; set; } = 0;

        /// <summary>
        /// 总规则数量
        /// </summary>
        public int TotalRules { get; set; } = 0;

        /// <summary>
        /// 推理查询总数
        /// </summary>
        public int TotalQueries { get; set; } = 0;

        /// <summary>
        /// 成功推理次数
        /// </summary>
        public int SuccessfulReasoning { get; set; } = 0;

        /// <summary>
        /// 失败推理次数
        /// </summary>
        public int FailedReasoning { get; set; } = 0;

        /// <summary>
        /// 平均推理时间（毫秒）
        /// </summary>
        public double AverageReasoningTimeMs { get; set; } = 0.0;

        /// <summary>
        /// 最长推理时间（毫秒）
        /// </summary>
        public long MaxReasoningTimeMs { get; set; } = 0;

        /// <summary>
        /// 最短推理时间（毫秒）
        /// </summary>
        public long MinReasoningTimeMs { get; set; } = long.MaxValue;

        /// <summary>
        /// 平均推理链长度
        /// </summary>
        public double AverageChainLength { get; set; } = 0.0;

        /// <summary>
        /// 最长推理链长度
        /// </summary>
        public int MaxChainLength { get; set; } = 0;

        /// <summary>
        /// 规则使用统计
        /// </summary>
        public Dictionary<string, int> RuleUsageCount { get; set; } = new();

        /// <summary>
        /// 统计生成时间
        /// </summary>
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
    }
}

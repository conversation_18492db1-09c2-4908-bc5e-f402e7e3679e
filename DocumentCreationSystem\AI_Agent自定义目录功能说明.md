# AI Agent自定义目录功能说明

## 功能概述

AI Agent对话框现在支持用户自定义选择项目目录，允许用户灵活地选择任意目录作为AI助手的工作范围。

## 新增功能

### 🗂️ **自定义目录选择**

#### 1. **浏览目录按钮**
- **位置**: 项目选择下拉框右侧
- **图标**: 文件夹打开图标 (FolderOpen)
- **功能**: 打开目录选择对话框

#### 2. **目录选择方式**

##### 方式一：文件对话框选择
- 使用WPF的OpenFileDialog
- 用户可以导航到任意目录
- 选择目录后自动创建项目信息

##### 方式二：手动输入路径
- 如果文件对话框选择失败，会弹出手动输入对话框
- 用户可以直接输入完整的目录路径
- 支持路径验证

#### 3. **智能项目分析**
系统会自动分析选择的目录，识别项目类型：

- **小说项目**: 包含"章节"、"角色"等关键词的文件
- **Markdown项目**: 超过50%的文件为.md格式
- **代码项目**: 包含.cs、.js、.py等代码文件
- **文档项目**: 包含.docx、.pdf等文档文件
- **混合项目**: 包含多种类型文件
- **未知类型**: 无法识别的项目类型

#### 4. **项目信息生成**
自动生成的项目信息包括：

- **项目名称**: `[自定义] 目录名`
- **项目路径**: 完整目录路径
- **项目类型**: 自动识别的类型
- **文件统计**: 总文件数、Markdown文件数
- **项目大小**: 计算总文件大小
- **活跃状态**: 基于最近30天的文件修改情况
- **主要文件**: 根据项目类型提取的重要文件
- **项目标签**: 包含"自定义目录"和项目类型标签

## 使用方法

### 步骤1：打开AI Agent对话框
1. 在主界面点击AI Agent按钮
2. 对话框会显示当前的项目选择区域

### 步骤2：选择自定义目录
1. 点击项目选择下拉框右侧的"浏览目录"按钮（文件夹图标）
2. 在弹出的文件选择对话框中导航到目标目录
3. 选择任意文件，系统会自动使用该文件所在的目录

### 步骤3：确认选择
1. 系统会自动分析目录并创建项目信息
2. 新项目会添加到项目列表的顶部
3. 自动选择新添加的项目
4. 显示确认消息

### 步骤4：开始使用
1. AI助手现在可以访问选择的目录
2. 可以执行文件操作、内容分析等功能
3. 所有工具调用都限制在选择的目录范围内

## 界面更新

### 项目选择区域布局
```
[选择项目:] [项目下拉框] [刷新] [浏览目录] [清除]
```

### 按钮说明
- **刷新按钮**: 重新扫描和加载项目列表
- **浏览目录按钮**: 选择自定义目录（新增）
- **清除按钮**: 清除当前项目选择

### 项目显示格式
- **系统扫描的项目**: `项目名 (项目类型)`
- **自定义目录项目**: `[自定义] 目录名 (项目类型)`

## 技术实现

### 核心方法

#### 1. `BrowseDirectory_Click`
- 处理浏览目录按钮点击事件
- 显示文件选择对话框
- 处理目录选择逻辑

#### 2. `CreateCustomProjectInfo`
- 分析目录结构和文件类型
- 生成完整的ProjectInfo对象
- 计算项目统计信息

#### 3. `AnalyzeProjectType`
- 基于文件扩展名和文件名模式识别项目类型
- 支持多种项目类型的智能识别

#### 4. `GetMainFiles`
- 根据项目类型提取主要文件
- 按文件重要性和修改时间排序

#### 5. `AddCustomProjectToList`
- 将自定义项目添加到下拉列表
- 处理重复项目的更新逻辑

### 数据结构

#### ProjectInfo扩展
```csharp
public class ProjectInfo
{
    public string Name { get; set; }           // [自定义] 前缀标识
    public string Path { get; set; }           // 完整目录路径
    public ProjectType Type { get; set; }      // 自动识别的类型
    public List<string> Tags { get; set; }     // 包含"自定义目录"标签
    // ... 其他属性
}
```

## 安全特性

### 路径验证
- 验证目录是否存在
- 防止访问系统敏感目录
- 路径规范化处理

### 访问控制
- 工具调用限制在选择的目录范围内
- 防止越权访问其他目录
- 安全的文件操作

### 错误处理
- 完善的异常捕获和处理
- 用户友好的错误提示
- 日志记录用于问题诊断

## 使用场景

### 1. **临时项目分析**
- 快速分析任意目录的文档结构
- 无需创建正式项目即可使用AI助手

### 2. **多项目切换**
- 在不同项目目录间快速切换
- 保持项目列表的灵活性

### 3. **外部目录操作**
- 处理不在标准项目目录中的文件
- 支持网络驱动器和外部存储

### 4. **开发调试**
- 测试AI助手在不同目录结构下的表现
- 验证工具功能的兼容性

## 注意事项

### 性能考虑
- 大型目录的分析可能需要一些时间
- 建议选择文件数量适中的目录

### 权限要求
- 确保对选择的目录有读写权限
- 某些系统目录可能无法访问

### 兼容性
- 支持本地文件系统和网络驱动器
- 兼容Windows路径格式

## 后续优化

### 计划改进
1. **目录收藏功能**: 保存常用的自定义目录
2. **批量目录选择**: 同时选择多个目录
3. **目录监控**: 实时监控目录变化
4. **云存储支持**: 支持OneDrive、Google Drive等

### 用户反馈
欢迎用户提供使用反馈和改进建议，帮助我们不断完善这个功能。

---

**更新时间**: 2025-08-04  
**功能状态**: 已实现并测试通过  
**兼容性**: Windows 10/11, .NET 8.0

using DocumentCreationSystem.Models.Reasoning;
using DocumentCreationSystem.Services.ReasoningEngine;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 推理引擎集成测试 - 测试推理引擎与Agent系统的集成
    /// </summary>
    public class TestReasoningEngineIntegration
    {
        private readonly ILogger<TestReasoningEngineIntegration> _logger;
        private readonly IReasoningEngine _reasoningEngine;
        private readonly IReasoningEngineInitializer _initializer;

        public TestReasoningEngineIntegration()
        {
            // 使用应用程序的服务提供者
            if (App.ServiceProvider == null)
            {
                throw new InvalidOperationException("应用程序服务提供者未初始化");
            }

            _logger = App.ServiceProvider.GetRequiredService<ILogger<TestReasoningEngineIntegration>>();
            _reasoningEngine = App.ServiceProvider.GetRequiredService<IReasoningEngine>();
            _initializer = App.ServiceProvider.GetRequiredService<IReasoningEngineInitializer>();
        }

        /// <summary>
        /// 运行集成测试
        /// </summary>
        public async Task RunIntegrationTestsAsync()
        {
            try
            {
                _logger.LogInformation("=== 推理引擎集成测试开始 ===");

                // 初始化推理引擎
                await InitializeReasoningEngineAsync();

                // 测试Agent知识推理
                await TestAgentKnowledgeReasoningAsync();

                // 测试用户意图推理
                await TestUserIntentReasoningAsync();

                // 测试创作辅助推理
                await TestCreativeAssistanceReasoningAsync();

                // 测试问题解决推理
                await TestProblemSolvingReasoningAsync();

                // 测试学习和适应
                await TestLearningAndAdaptationAsync();

                _logger.LogInformation("=== 推理引擎集成测试完成 ===");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "推理引擎集成测试失败");
                throw;
            }
        }

        /// <summary>
        /// 初始化推理引擎
        /// </summary>
        private async Task InitializeReasoningEngineAsync()
        {
            _logger.LogInformation("--- 初始化推理引擎 ---");
            
            await _initializer.InitializeAsync(_reasoningEngine);
            
            // 添加Agent相关的知识
            await AddAgentKnowledgeAsync();
            
            var stats = await _reasoningEngine.GetStatisticsAsync();
            _logger.LogInformation($"初始化完成 - 事实数量: {stats.TotalFacts}, 规则数量: {stats.TotalRules}");
        }

        /// <summary>
        /// 添加Agent相关知识
        /// </summary>
        private async Task AddAgentKnowledgeAsync()
        {
            // 添加Agent能力相关事实
            var agentFacts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "AI Agent",
                    Predicate = "具有",
                    Object = "工具调用能力",
                    Confidence = 1.0,
                    Type = FactType.Assertion,
                    Source = "Agent系统",
                    Tags = new List<string> { "Agent", "能力", "工具" }
                },
                new ReasoningFact
                {
                    Subject = "AI Agent",
                    Predicate = "具有",
                    Object = "对话能力",
                    Confidence = 1.0,
                    Type = FactType.Assertion,
                    Source = "Agent系统",
                    Tags = new List<string> { "Agent", "能力", "对话" }
                },
                new ReasoningFact
                {
                    Subject = "AI Agent",
                    Predicate = "具有",
                    Object = "学习能力",
                    Confidence = 0.8,
                    Type = FactType.Assertion,
                    Source = "Agent系统",
                    Tags = new List<string> { "Agent", "能力", "学习" }
                },
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "需要",
                    Object = "文档创作帮助",
                    Confidence = 0.9,
                    Type = FactType.Observation,
                    Source = "用户行为分析",
                    Tags = new List<string> { "用户", "需求", "创作" }
                },
                new ReasoningFact
                {
                    Subject = "文档创作",
                    Predicate = "需要",
                    Object = "结构化思维",
                    Confidence = 0.95,
                    Type = FactType.Assertion,
                    Source = "创作理论",
                    Tags = new List<string> { "创作", "思维", "结构" }
                }
            };

            await _reasoningEngine.AddFactsAsync(agentFacts);

            // 添加Agent推理规则
            var agentRules = new List<ReasoningRule>
            {
                new ReasoningRule
                {
                    Id = "agent_capability_inference",
                    Name = "Agent能力推理",
                    Description = "如果Agent具有某种能力，且用户需要该能力相关的帮助，则Agent可以提供帮助",
                    Confidence = 0.9,
                    Type = RuleType.Deductive,
                    Conditions = new List<RuleCondition>
                    {
                        new RuleCondition
                        {
                            Type = ConditionType.FactMatch,
                            Pattern = new FactPattern
                            {
                                SubjectPattern = "AI Agent",
                                PredicatePattern = "具有"
                            }
                        },
                        new RuleCondition
                        {
                            Type = ConditionType.FactMatch,
                            Pattern = new FactPattern
                            {
                                SubjectPattern = "用户",
                                PredicatePattern = "需要"
                            }
                        }
                    },
                    Conclusions = new List<RuleConclusion>
                    {
                        new RuleConclusion
                        {
                            Type = ConclusionType.AddFact,
                            NewFact = new ReasoningFact
                            {
                                Subject = "AI Agent",
                                Predicate = "可以帮助",
                                Object = "用户",
                                Type = FactType.Derived
                            },
                            Confidence = 0.85
                        }
                    },
                    Tags = new List<string> { "Agent", "能力匹配", "用户服务" }
                },
                new ReasoningRule
                {
                    Id = "tool_selection_rule",
                    Name = "工具选择规则",
                    Description = "如果用户有特定需求，且存在相应工具，则应该使用该工具",
                    Confidence = 0.95,
                    Type = RuleType.Deductive,
                    Conditions = new List<RuleCondition>
                    {
                        new RuleCondition
                        {
                            Type = ConditionType.FactMatch,
                            Pattern = new FactPattern
                            {
                                SubjectPattern = "用户",
                                PredicatePattern = "需要"
                            }
                        }
                    },
                    Conclusions = new List<RuleConclusion>
                    {
                        new RuleConclusion
                        {
                            Type = ConclusionType.ExecuteAction,
                            Action = "select_appropriate_tool",
                            Confidence = 0.9
                        }
                    },
                    Tags = new List<string> { "工具选择", "需求匹配" }
                }
            };

            await _reasoningEngine.AddRulesAsync(agentRules);
        }

        /// <summary>
        /// 测试Agent知识推理
        /// </summary>
        private async Task TestAgentKnowledgeReasoningAsync()
        {
            _logger.LogInformation("--- 测试Agent知识推理 ---");

            // 查询Agent的能力
            var capabilityQuery = new FactQuery
            {
                Subject = "AI Agent",
                Predicate = "具有",
                MaxResults = 10
            };

            var capabilities = await _reasoningEngine.QueryFactsAsync(capabilityQuery);
            _logger.LogInformation($"Agent具有 {capabilities.Count} 种能力:");
            foreach (var capability in capabilities)
            {
                _logger.LogInformation($"  - {capability.Object} (置信度: {capability.Confidence:F2})");
            }

            // 推理Agent可以提供的服务
            var serviceQuery = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 3,
                MinConfidence = 0.7,
                IncludeReasoningChain = true
            };

            var serviceResult = await _reasoningEngine.ReasonAsync(serviceQuery);
            _logger.LogInformation($"推理出 {serviceResult.DerivedFacts.Count} 个服务能力");

            foreach (var service in serviceResult.DerivedFacts)
            {
                _logger.LogInformation($"  服务: {service}");
            }
        }

        /// <summary>
        /// 测试用户意图推理
        /// </summary>
        private async Task TestUserIntentReasoningAsync()
        {
            _logger.LogInformation("--- 测试用户意图推理 ---");

            // 添加用户行为事实
            var userBehaviorFacts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "打开了",
                    Object = "文档编辑器",
                    Confidence = 1.0,
                    Type = FactType.Observation,
                    Source = "用户行为监控"
                },
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "搜索了",
                    Object = "论文模板",
                    Confidence = 1.0,
                    Type = FactType.Observation,
                    Source = "用户行为监控"
                },
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "询问了",
                    Object = "如何写论文",
                    Confidence = 1.0,
                    Type = FactType.Observation,
                    Source = "对话记录"
                }
            };

            await _reasoningEngine.AddFactsAsync(userBehaviorFacts);

            // 添加意图推理规则
            var intentRule = new ReasoningRule
            {
                Id = "paper_writing_intent",
                Name = "论文写作意图推理",
                Description = "如果用户打开文档编辑器、搜索论文模板并询问如何写论文，则用户意图是写论文",
                Confidence = 0.95,
                Type = RuleType.Inductive,
                Conditions = new List<RuleCondition>
                {
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            SubjectPattern = "用户",
                            PredicatePattern = "打开了",
                            ObjectPattern = "文档编辑器"
                        }
                    },
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            SubjectPattern = "用户",
                            PredicatePattern = "搜索了",
                            ObjectPattern = ".*模板.*",
                            UseRegex = true
                        }
                    }
                },
                Conclusions = new List<RuleConclusion>
                {
                    new RuleConclusion
                    {
                        Type = ConclusionType.AddFact,
                        NewFact = new ReasoningFact
                        {
                            Subject = "用户",
                            Predicate = "意图是",
                            Object = "写论文",
                            Type = FactType.Derived
                        },
                        Confidence = 0.9
                    }
                }
            };

            await _reasoningEngine.AddRuleAsync(intentRule);

            // 执行意图推理
            var intentQuery = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 2,
                MinConfidence = 0.8,
                IncludeReasoningChain = true
            };

            var intentResult = await _reasoningEngine.ReasonAsync(intentQuery);
            _logger.LogInformation($"意图推理结果: {intentResult.IsSuccess}");

            if (intentResult.IsSuccess)
            {
                foreach (var intent in intentResult.DerivedFacts.Where(f => f.Predicate == "意图是"))
                {
                    _logger.LogInformation($"  推断意图: {intent.Subject} -> {intent.Object} (置信度: {intent.Confidence:F2})");
                }
            }

            // 清理测试数据
            foreach (var fact in userBehaviorFacts)
            {
                await _reasoningEngine.RemoveFactAsync(fact.Id);
            }
            await _reasoningEngine.RemoveRuleAsync(intentRule.Id);
        }

        /// <summary>
        /// 测试创作辅助推理
        /// </summary>
        private async Task TestCreativeAssistanceReasoningAsync()
        {
            _logger.LogInformation("--- 测试创作辅助推理 ---");

            // 添加创作相关知识
            var creativeKnowledge = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "小说创作",
                    Predicate = "需要",
                    Object = "人物设定",
                    Confidence = 0.95,
                    Type = FactType.Assertion,
                    Source = "创作理论"
                },
                new ReasoningFact
                {
                    Subject = "小说创作",
                    Predicate = "需要",
                    Object = "情节大纲",
                    Confidence = 0.9,
                    Type = FactType.Assertion,
                    Source = "创作理论"
                },
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "正在创作",
                    Object = "科幻小说",
                    Confidence = 1.0,
                    Type = FactType.Observation,
                    Source = "项目信息"
                }
            };

            await _reasoningEngine.AddFactsAsync(creativeKnowledge);

            // 推理创作建议
            var creativeQuery = new ReasoningQuery
            {
                Goal = new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "需要",
                    Object = "创作建议"
                },
                Type = ReasoningType.Backward,
                MaxDepth = 3,
                MinConfidence = 0.7
            };

            var creativeResult = await _reasoningEngine.ReasonAsync(creativeQuery);
            _logger.LogInformation($"创作辅助推理结果: {creativeResult.IsSuccess}");

            if (creativeResult.ReasoningChain != null)
            {
                _logger.LogInformation($"推理链长度: {creativeResult.ReasoningChain.Length}");
                foreach (var step in creativeResult.ReasoningChain.Steps)
                {
                    _logger.LogInformation($"  {step.Description}");
                }
            }

            // 清理测试数据
            foreach (var fact in creativeKnowledge)
            {
                await _reasoningEngine.RemoveFactAsync(fact.Id);
            }
        }

        /// <summary>
        /// 测试问题解决推理
        /// </summary>
        private async Task TestProblemSolvingReasoningAsync()
        {
            _logger.LogInformation("--- 测试问题解决推理 ---");

            // 模拟问题场景
            var problemFacts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "遇到了",
                    Object = "写作瓶颈",
                    Confidence = 1.0,
                    Type = FactType.Observation,
                    Source = "用户反馈"
                },
                new ReasoningFact
                {
                    Subject = "写作瓶颈",
                    Predicate = "可能由于",
                    Object = "缺乏灵感",
                    Confidence = 0.7,
                    Type = FactType.Hypothesis,
                    Source = "问题分析"
                },
                new ReasoningFact
                {
                    Subject = "缺乏灵感",
                    Predicate = "可以通过",
                    Object = "阅读相关资料",
                    Confidence = 0.8,
                    Type = FactType.Assertion,
                    Source = "解决方案库"
                }
            };

            await _reasoningEngine.AddFactsAsync(problemFacts);

            // 添加问题解决规则
            var solutionRule = new ReasoningRule
            {
                Id = "problem_solving_rule",
                Name = "问题解决规则",
                Description = "如果用户遇到问题，且存在解决方案，则建议使用该解决方案",
                Confidence = 0.85,
                Type = RuleType.Deductive,
                Conditions = new List<RuleCondition>
                {
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            SubjectPattern = "用户",
                            PredicatePattern = "遇到了"
                        }
                    }
                },
                Conclusions = new List<RuleConclusion>
                {
                    new RuleConclusion
                    {
                        Type = ConclusionType.AddFact,
                        NewFact = new ReasoningFact
                        {
                            Subject = "系统",
                            Predicate = "建议",
                            Object = "提供解决方案",
                            Type = FactType.Derived
                        },
                        Confidence = 0.8
                    }
                }
            };

            await _reasoningEngine.AddRuleAsync(solutionRule);

            // 执行问题解决推理
            var solutionQuery = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 4,
                MinConfidence = 0.6,
                IncludeReasoningChain = true
            };

            var solutionResult = await _reasoningEngine.ReasonAsync(solutionQuery);
            _logger.LogInformation($"问题解决推理结果: {solutionResult.IsSuccess}");

            foreach (var solution in solutionResult.DerivedFacts)
            {
                _logger.LogInformation($"  解决方案: {solution}");
            }

            // 清理测试数据
            foreach (var fact in problemFacts)
            {
                await _reasoningEngine.RemoveFactAsync(fact.Id);
            }
            await _reasoningEngine.RemoveRuleAsync(solutionRule.Id);
        }

        /// <summary>
        /// 测试学习和适应
        /// </summary>
        private async Task TestLearningAndAdaptationAsync()
        {
            _logger.LogInformation("--- 测试学习和适应 ---");

            // 模拟用户反馈学习
            var feedbackFacts = new List<ReasoningFact>
            {
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "喜欢",
                    Object = "详细的大纲",
                    Confidence = 0.9,
                    Type = FactType.Observation,
                    Source = "用户反馈"
                },
                new ReasoningFact
                {
                    Subject = "用户",
                    Predicate = "不喜欢",
                    Object = "过于简单的建议",
                    Confidence = 0.8,
                    Type = FactType.Observation,
                    Source = "用户反馈"
                }
            };

            await _reasoningEngine.AddFactsAsync(feedbackFacts);

            // 添加学习规则
            var learningRule = new ReasoningRule
            {
                Id = "preference_learning_rule",
                Name = "偏好学习规则",
                Description = "根据用户反馈调整服务策略",
                Confidence = 0.9,
                Type = RuleType.Inductive,
                Conditions = new List<RuleCondition>
                {
                    new RuleCondition
                    {
                        Type = ConditionType.FactMatch,
                        Pattern = new FactPattern
                        {
                            SubjectPattern = "用户",
                            PredicatePattern = "喜欢"
                        }
                    }
                },
                Conclusions = new List<RuleConclusion>
                {
                    new RuleConclusion
                    {
                        Type = ConclusionType.AddFact,
                        NewFact = new ReasoningFact
                        {
                            Subject = "系统",
                            Predicate = "应该提供",
                            Object = "详细服务",
                            Type = FactType.Derived
                        },
                        Confidence = 0.85
                    }
                }
            };

            await _reasoningEngine.AddRuleAsync(learningRule);

            // 执行学习推理
            var learningQuery = new ReasoningQuery
            {
                Type = ReasoningType.Forward,
                MaxDepth = 2,
                MinConfidence = 0.7,
                IncludeReasoningChain = true
            };

            var learningResult = await _reasoningEngine.ReasonAsync(learningQuery);
            _logger.LogInformation($"学习推理结果: {learningResult.IsSuccess}");

            foreach (var adaptation in learningResult.DerivedFacts)
            {
                _logger.LogInformation($"  适应策略: {adaptation}");
            }

            // 检查一致性
            var allFacts = await _reasoningEngine.GetAllFactsAsync();
            var consistencyResult = await _reasoningEngine.CheckConsistencyAsync(allFacts);
            _logger.LogInformation($"知识库一致性: {consistencyResult.IsConsistent} (评分: {consistencyResult.ConsistencyScore:F2})");

            // 清理测试数据
            foreach (var fact in feedbackFacts)
            {
                await _reasoningEngine.RemoveFactAsync(fact.Id);
            }
            await _reasoningEngine.RemoveRuleAsync(learningRule.Id);
        }

        /// <summary>
        /// 主程序入口
        /// </summary>
        public static async Task Main(string[] args)
        {
            try
            {
                // 注意：这个测试需要在应用程序启动后运行
                Console.WriteLine("推理引擎集成测试需要在主应用程序中运行");
                Console.WriteLine("请在MainWindow中调用TestReasoningEngineIntegration.RunIntegrationTestsAsync()");
                
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }
    }
}

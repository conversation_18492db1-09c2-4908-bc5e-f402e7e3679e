using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// SOP（标准作业程序）生成请求模型
    /// </summary>
    public class SOPGenerationRequest
    {
        /// <summary>
        /// SOP标题（可留空由AI生成）
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// SOP类型
        /// </summary>
        [Required]
        public string SOPType { get; set; } = string.Empty;

        /// <summary>
        /// 业务领域
        /// </summary>
        [Required]
        public string BusinessDomain { get; set; } = string.Empty;

        /// <summary>
        /// 目标字数
        /// </summary>
        [Range(1000, 50000)]
        public int TargetWordCount { get; set; } = 5000;

        /// <summary>
        /// 详细程度（0.1-1.0）
        /// </summary>
        [Range(0.1, 1.0)]
        public float DetailLevel { get; set; } = 0.7f;

        /// <summary>
        /// 规范化程度（0.1-1.0）
        /// </summary>
        [Range(0.1, 1.0)]
        public float StandardizationLevel { get; set; } = 0.8f;

        /// <summary>
        /// 素材文件路径列表
        /// </summary>
        public List<string> MaterialFiles { get; set; } = new();

        /// <summary>
        /// SOP结构配置
        /// </summary>
        public SOPStructure Structure { get; set; } = new();

        /// <summary>
        /// 输出格式
        /// </summary>
        public string OutputFormat { get; set; } = "Word文档";

        /// <summary>
        /// 是否保存到项目文件夹
        /// </summary>
        public bool SaveToProject { get; set; } = true;

        /// <summary>
        /// 部门信息
        /// </summary>
        public string? Department { get; set; }

        /// <summary>
        /// 制定人
        /// </summary>
        public string? Author { get; set; }

        /// <summary>
        /// 审核人
        /// </summary>
        public string? Reviewer { get; set; }

        /// <summary>
        /// 批准人
        /// </summary>
        public string? Approver { get; set; }

        /// <summary>
        /// 用户附加要求
        /// </summary>
        public string? UserRequirements { get; set; }
    }

    /// <summary>
    /// SOP结构配置
    /// </summary>
    public class SOPStructure
    {
        /// <summary>
        /// 包含目的和范围
        /// </summary>
        public bool IncludePurposeAndScope { get; set; } = true;

        /// <summary>
        /// 包含职责分工
        /// </summary>
        public bool IncludeResponsibilities { get; set; } = true;

        /// <summary>
        /// 包含操作流程
        /// </summary>
        public bool IncludeProcedures { get; set; } = true;

        /// <summary>
        /// 包含质量控制
        /// </summary>
        public bool IncludeQualityControl { get; set; } = true;

        /// <summary>
        /// 包含风险控制
        /// </summary>
        public bool IncludeRiskControl { get; set; } = true;

        /// <summary>
        /// 包含记录管理
        /// </summary>
        public bool IncludeRecordManagement { get; set; } = true;

        /// <summary>
        /// 包含培训要求
        /// </summary>
        public bool IncludeTrainingRequirements { get; set; } = true;

        /// <summary>
        /// 包含相关文件
        /// </summary>
        public bool IncludeRelatedDocuments { get; set; } = true;

        /// <summary>
        /// 包含附录
        /// </summary>
        public bool IncludeAppendices { get; set; } = true;

        /// <summary>
        /// 包含修订历史
        /// </summary>
        public bool IncludeRevisionHistory { get; set; } = true;
    }

    /// <summary>
    /// SOP素材分析结果
    /// </summary>
    public class SOPMaterialAnalysisResult
    {
        /// <summary>
        /// 主要业务流程
        /// </summary>
        public List<string> MainProcesses { get; set; } = new();

        /// <summary>
        /// 关键操作步骤
        /// </summary>
        public List<string> KeyOperations { get; set; } = new();

        /// <summary>
        /// 质量控制点
        /// </summary>
        public List<string> QualityControlPoints { get; set; } = new();

        /// <summary>
        /// 风险识别点
        /// </summary>
        public List<string> RiskPoints { get; set; } = new();

        /// <summary>
        /// 相关角色和职责
        /// </summary>
        public List<string> RolesAndResponsibilities { get; set; } = new();

        /// <summary>
        /// 关键术语和定义
        /// </summary>
        public List<string> KeyTerms { get; set; } = new();

        /// <summary>
        /// 相关标准和规范
        /// </summary>
        public List<string> Standards { get; set; } = new();

        /// <summary>
        /// 工具和设备
        /// </summary>
        public List<string> ToolsAndEquipment { get; set; } = new();

        /// <summary>
        /// 输入输出要求
        /// </summary>
        public List<string> InputOutputRequirements { get; set; } = new();

        /// <summary>
        /// 性能指标
        /// </summary>
        public List<string> PerformanceIndicators { get; set; } = new();
    }

    /// <summary>
    /// SOP大纲
    /// </summary>
    public class SOPOutline
    {
        /// <summary>
        /// SOP标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节大纲列表
        /// </summary>
        public List<SOPSectionOutline> SectionOutlines { get; set; } = new();

        /// <summary>
        /// 预估总字数
        /// </summary>
        public int EstimatedTotalWordCount { get; set; }
    }

    /// <summary>
    /// SOP章节大纲
    /// </summary>
    public class SOPSectionOutline
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 章节要点
        /// </summary>
        public List<string> KeyPoints { get; set; } = new();

        /// <summary>
        /// 预估字数
        /// </summary>
        public int EstimatedWordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// SOP章节
    /// </summary>
    public class SOPSection
    {
        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 章节类型
        /// </summary>
        public string SectionType { get; set; } = string.Empty;

        /// <summary>
        /// 实际字数
        /// </summary>
        public int WordCount { get; set; }

        /// <summary>
        /// 章节顺序
        /// </summary>
        public int Order { get; set; }
    }

    /// <summary>
    /// SOP生成结果
    /// </summary>
    public class SOPGenerationResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// SOP标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 完整SOP内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 章节列表
        /// </summary>
        public List<SOPSection> Sections { get; set; } = new();

        /// <summary>
        /// 生成统计信息
        /// </summary>
        public SOPGenerationStatistics? Statistics { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 保存的文件路径
        /// </summary>
        public string? SavedFilePath { get; set; }
    }

    /// <summary>
    /// SOP生成统计信息
    /// </summary>
    public class SOPGenerationStatistics
    {
        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount { get; set; }

        /// <summary>
        /// 生成耗时
        /// </summary>
        public TimeSpan GenerationTime { get; set; }

        /// <summary>
        /// 素材文件数量
        /// </summary>
        public int MaterialFilesCount { get; set; }

        /// <summary>
        /// 章节数量
        /// </summary>
        public int SectionsCount { get; set; }

        /// <summary>
        /// 素材利用率
        /// </summary>
        public double MaterialUtilizationRate { get; set; }

        /// <summary>
        /// 质量评分（0-100）
        /// </summary>
        public int QualityScore { get; set; }
    }
}

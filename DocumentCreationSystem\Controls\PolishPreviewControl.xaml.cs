using DocumentCreationSystem.Helpers;
using System;
using System.Windows;
using System.Windows.Controls;

namespace DocumentCreationSystem.Controls
{
    /// <summary>
    /// 润色预览控件
    /// </summary>
    public partial class PolishPreviewControl : System.Windows.Controls.UserControl
    {
        /// <summary>
        /// 用户接受润色结果事件
        /// </summary>
        public event EventHandler<PolishResultEventArgs>? PolishAccepted;

        /// <summary>
        /// 用户拒绝润色结果事件
        /// </summary>
        public event EventHandler<PolishResultEventArgs>? PolishRejected;

        /// <summary>
        /// 关闭预览事件
        /// </summary>
        public event EventHandler? PreviewClosed;

        /// <summary>
        /// 重新润色事件
        /// </summary>
        public event EventHandler<RePolishEventArgs>? RePolishRequested;

        private string _originalText = string.Empty;
        private string _polishedText = string.Empty;
        private int _selectionStart;
        private int _selectionLength;
        private bool _isComparisonMode = false;

        public PolishPreviewControl()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 显示润色预览
        /// </summary>
        /// <param name="originalText">原文</param>
        /// <param name="polishedText">润色后文本</param>
        /// <param name="selectionStart">选择开始位置</param>
        /// <param name="selectionLength">选择长度</param>
        /// <param name="useFloatingControl">是否使用悬浮控件模式</param>
        public void ShowPolishPreview(string originalText, string polishedText, 
            int selectionStart, int selectionLength, bool useFloatingControl = false)
        {
            _originalText = originalText;
            _polishedText = polishedText;
            _selectionStart = selectionStart;
            _selectionLength = selectionLength;

            OriginalTextBlock.Text = originalText;
            PolishedTextBlock.Text = polishedText;

            if (useFloatingControl)
            {
                PreviewContainer.Visibility = Visibility.Collapsed;
                FloatingControl.Visibility = Visibility.Visible;
            }
            else
            {
                PreviewContainer.Visibility = Visibility.Visible;
                FloatingControl.Visibility = Visibility.Collapsed;
            }

            Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 隐藏润色预览
        /// </summary>
        public void HidePolishPreview()
        {
            Visibility = Visibility.Collapsed;
            PreviewContainer.Visibility = Visibility.Collapsed;
            FloatingControl.Visibility = Visibility.Collapsed;
        }

        private void AcceptButton_Click(object sender, RoutedEventArgs e)
        {
            var args = new PolishResultEventArgs
            {
                OriginalText = _originalText,
                PolishedText = _polishedText,
                SelectionStart = _selectionStart,
                SelectionLength = _selectionLength,
                IsAccepted = true
            };

            PolishAccepted?.Invoke(this, args);
            HidePolishPreview();
        }

        private void RejectButton_Click(object sender, RoutedEventArgs e)
        {
            var args = new PolishResultEventArgs
            {
                OriginalText = _originalText,
                PolishedText = _polishedText,
                SelectionStart = _selectionStart,
                SelectionLength = _selectionLength,
                IsAccepted = false
            };

            PolishRejected?.Invoke(this, args);
            HidePolishPreview();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            HidePolishPreview();
            PreviewClosed?.Invoke(this, EventArgs.Empty);
        }

        private void RePolish_Click(object sender, RoutedEventArgs e)
        {
            var args = new RePolishEventArgs
            {
                OriginalText = _originalText,
                PolishType = PolishTypeComboBox?.SelectedItem?.ToString() ?? "语言润色",
                Intensity = (int)(PolishIntensitySlider?.Value ?? 3)
            };

            RePolishRequested?.Invoke(this, args);
        }

        private void Compare_Click(object sender, RoutedEventArgs e)
        {
            _isComparisonMode = !_isComparisonMode;

            if (_isComparisonMode)
            {
                ComparisonGrid.Visibility = Visibility.Visible;
                ((FrameworkElement)PolishedTextBlock.Parent).Visibility = Visibility.Collapsed;
                CompareButton.Content = "普通模式";

                CompareOriginalText.Text = _originalText;
                ComparePolishedText.Text = _polishedText;
            }
            else
            {
                ComparisonGrid.Visibility = Visibility.Collapsed;
                ((FrameworkElement)PolishedTextBlock.Parent).Visibility = Visibility.Visible;
                CompareButton.Content = "对比模式";
            }
        }

        private void Copy_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Windows.Clipboard.SetText(_polishedText);
                // 可以添加一个提示消息
            }
            catch (Exception ex)
            {
                // 处理复制失败的情况
                MessageHelper.ShowError(
                    "复制润色文本失败。",
                    ex,
                    "复制错误",
                    Window.GetWindow(this));
            }
        }

        /// <summary>
        /// 更新字数变化显示
        /// </summary>
        private void UpdateWordCountChange()
        {
            var originalCount = _originalText.Length;
            var polishedCount = _polishedText.Length;
            var change = polishedCount - originalCount;

            if (change > 0)
            {
                WordCountChangeText.Text = $"增加 {change} 字";
                WordCountChangeText.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
            }
            else if (change < 0)
            {
                WordCountChangeText.Text = $"减少 {Math.Abs(change)} 字";
                WordCountChangeText.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red);
            }
            else
            {
                WordCountChangeText.Text = "字数无变化";
                WordCountChangeText.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray);
            }
        }

        /// <summary>
        /// 设置润色说明
        /// </summary>
        public void SetExplanation(string explanation)
        {
            if (!string.IsNullOrEmpty(explanation))
            {
                ExplanationText.Text = explanation;
                ExplanationExpander.IsExpanded = true;
            }
        }
    }

    /// <summary>
    /// 润色结果事件参数
    /// </summary>
    public class PolishResultEventArgs : EventArgs
    {
        /// <summary>
        /// 原文
        /// </summary>
        public string OriginalText { get; set; } = string.Empty;

        /// <summary>
        /// 润色后文本
        /// </summary>
        public string PolishedText { get; set; } = string.Empty;

        /// <summary>
        /// 选择开始位置
        /// </summary>
        public int SelectionStart { get; set; }

        /// <summary>
        /// 选择长度
        /// </summary>
        public int SelectionLength { get; set; }

        /// <summary>
        /// 是否接受润色结果
        /// </summary>
        public bool IsAccepted { get; set; }
    }

    /// <summary>
    /// 重新润色事件参数
    /// </summary>
    public class RePolishEventArgs : EventArgs
    {
        /// <summary>
        /// 原文
        /// </summary>
        public string OriginalText { get; set; } = string.Empty;

        /// <summary>
        /// 润色类型
        /// </summary>
        public string PolishType { get; set; } = string.Empty;

        /// <summary>
        /// 润色强度 (1-5)
        /// </summary>
        public int Intensity { get; set; } = 3;
    }
}

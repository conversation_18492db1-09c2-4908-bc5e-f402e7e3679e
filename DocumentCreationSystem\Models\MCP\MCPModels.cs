using System.Text.Json.Serialization;

namespace DocumentCreationSystem.Models.MCP
{
    /// <summary>
    /// MCP协议版本
    /// </summary>
    public static class MCPVersion
    {
        public const string Current = "2024-11-05";
    }

    /// <summary>
    /// MCP消息基类
    /// </summary>
    public abstract class MCPMessage
    {
        [JsonPropertyName("jsonrpc")]
        public string JsonRpc { get; set; } = "2.0";
    }

    /// <summary>
    /// MCP请求消息
    /// </summary>
    public class MCPRequest : MCPMessage
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        [JsonPropertyName("method")]
        public string Method { get; set; } = string.Empty;

        [JsonPropertyName("params")]
        public object? Params { get; set; }
    }

    /// <summary>
    /// MCP响应消息
    /// </summary>
    public class MCPResponse : MCPMessage
    {
        [JsonPropertyName("id")]
        public string Id { get; set; } = string.Empty;

        [JsonPropertyName("result")]
        public object? Result { get; set; }

        [JsonPropertyName("error")]
        public MCPError? Error { get; set; }
    }

    /// <summary>
    /// MCP通知消息
    /// </summary>
    public class MCPNotification : MCPMessage
    {
        [JsonPropertyName("method")]
        public string Method { get; set; } = string.Empty;

        [JsonPropertyName("params")]
        public object? Params { get; set; }
    }

    /// <summary>
    /// MCP错误信息
    /// </summary>
    public class MCPError
    {
        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("data")]
        public object? Data { get; set; }
    }

    /// <summary>
    /// MCP工具定义
    /// </summary>
    public class MCPTool
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("inputSchema")]
        public MCPSchema InputSchema { get; set; } = new();
    }

    /// <summary>
    /// MCP资源定义
    /// </summary>
    public class MCPResource
    {
        [JsonPropertyName("uri")]
        public string Uri { get; set; } = string.Empty;

        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("mimeType")]
        public string? MimeType { get; set; }
    }

    /// <summary>
    /// MCP提示模板定义
    /// </summary>
    public class MCPPromptTemplate
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("arguments")]
        public List<MCPPromptArgument> Arguments { get; set; } = new();
    }

    /// <summary>
    /// MCP提示参数
    /// </summary>
    public class MCPPromptArgument
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("required")]
        public bool Required { get; set; }
    }

    /// <summary>
    /// MCP JSON Schema定义
    /// </summary>
    public class MCPSchema
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "object";

        [JsonPropertyName("properties")]
        public Dictionary<string, MCPSchemaProperty> Properties { get; set; } = new();

        [JsonPropertyName("required")]
        public List<string> Required { get; set; } = new();

        [JsonPropertyName("additionalProperties")]
        public bool AdditionalProperties { get; set; } = false;
    }

    /// <summary>
    /// MCP Schema属性
    /// </summary>
    public class MCPSchemaProperty
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "string";

        [JsonPropertyName("description")]
        public string? Description { get; set; }

        [JsonPropertyName("enum")]
        public List<string>? Enum { get; set; }

        [JsonPropertyName("default")]
        public object? Default { get; set; }

        [JsonPropertyName("items")]
        public MCPSchemaProperty? Items { get; set; }

        [JsonPropertyName("properties")]
        public Dictionary<string, MCPSchemaProperty>? Properties { get; set; }
    }

    /// <summary>
    /// MCP工具调用请求
    /// </summary>
    public class MCPToolCallRequest
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("arguments")]
        public Dictionary<string, object> Arguments { get; set; } = new();
    }

    /// <summary>
    /// MCP工具调用结果
    /// </summary>
    public class MCPToolCallResult
    {
        [JsonPropertyName("content")]
        public List<MCPContent> Content { get; set; } = new();

        [JsonPropertyName("isError")]
        public bool IsError { get; set; }
    }

    /// <summary>
    /// MCP内容
    /// </summary>
    public class MCPContent
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } = "text";

        [JsonPropertyName("text")]
        public string? Text { get; set; }

        [JsonPropertyName("data")]
        public string? Data { get; set; }

        [JsonPropertyName("mimeType")]
        public string? MimeType { get; set; }
    }

    /// <summary>
    /// MCP服务器信息
    /// </summary>
    public class MCPServerInfo
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        [JsonPropertyName("protocolVersion")]
        public string ProtocolVersion { get; set; } = MCPVersion.Current;
    }

    /// <summary>
    /// MCP客户端信息
    /// </summary>
    public class MCPClientInfo
    {
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// MCP初始化请求
    /// </summary>
    public class MCPInitializeRequest
    {
        [JsonPropertyName("protocolVersion")]
        public string ProtocolVersion { get; set; } = MCPVersion.Current;

        [JsonPropertyName("capabilities")]
        public MCPClientCapabilities Capabilities { get; set; } = new();

        [JsonPropertyName("clientInfo")]
        public MCPClientInfo ClientInfo { get; set; } = new();
    }

    /// <summary>
    /// MCP初始化响应
    /// </summary>
    public class MCPInitializeResponse
    {
        [JsonPropertyName("protocolVersion")]
        public string ProtocolVersion { get; set; } = MCPVersion.Current;

        [JsonPropertyName("capabilities")]
        public MCPServerCapabilities Capabilities { get; set; } = new();

        [JsonPropertyName("serverInfo")]
        public MCPServerInfo ServerInfo { get; set; } = new();
    }

    /// <summary>
    /// MCP客户端能力
    /// </summary>
    public class MCPClientCapabilities
    {
        [JsonPropertyName("roots")]
        public MCPRootsCapability? Roots { get; set; }

        [JsonPropertyName("sampling")]
        public object? Sampling { get; set; }
    }

    /// <summary>
    /// MCP服务器能力
    /// </summary>
    public class MCPServerCapabilities
    {
        [JsonPropertyName("tools")]
        public object? Tools { get; set; }

        [JsonPropertyName("resources")]
        public object? Resources { get; set; }

        [JsonPropertyName("prompts")]
        public object? Prompts { get; set; }

        [JsonPropertyName("logging")]
        public object? Logging { get; set; }
    }

    /// <summary>
    /// MCP根目录能力
    /// </summary>
    public class MCPRootsCapability
    {
        [JsonPropertyName("listChanged")]
        public bool ListChanged { get; set; } = true;
    }

    /// <summary>
    /// MCP错误代码
    /// </summary>
    public static class MCPErrorCodes
    {
        public const int ParseError = -32700;
        public const int InvalidRequest = -32600;
        public const int MethodNotFound = -32601;
        public const int InvalidParams = -32602;
        public const int InternalError = -32603;
        public const int ServerError = -32000;
    }

    /// <summary>
    /// MCP方法名称
    /// </summary>
    public static class MCPMethods
    {
        public const string Initialize = "initialize";
        public const string Initialized = "initialized";
        public const string ListTools = "tools/list";
        public const string CallTool = "tools/call";
        public const string ListResources = "resources/list";
        public const string ReadResource = "resources/read";
        public const string ListPrompts = "prompts/list";
        public const string GetPrompt = "prompts/get";
        public const string SetLevel = "logging/setLevel";
        public const string Ping = "ping";
    }
}

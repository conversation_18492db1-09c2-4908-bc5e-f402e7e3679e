<Window x:Class="DocumentCreationSystem.Views.PromptConfigDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="提示词设定" Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <materialDesign:PackIcon Kind="MessageText" Height="24" Width="24"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="提示词设定管理" VerticalAlignment="Center"
                         FontSize="18" FontWeight="Medium"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="NewConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Margin="0,0,4,0"/>
                                <TextBlock Text="新建配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ImportConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Import" Margin="0,0,4,0"/>
                                <TextBlock Text="导入配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="ExportConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Export" Margin="0,0,4,0"/>
                                <TextBlock Text="导出配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧配置列表 -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="配置列表" FontWeight="Medium" 
                             Margin="16,16,16,8" FontSize="14"/>

                    <ListBox x:Name="ConfigListBox" Grid.Row="1" 
                           SelectionChanged="ConfigListBox_SelectionChanged"
                           Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="8">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <TextBlock Grid.Row="0" Text="{Binding Name}" FontWeight="Medium"/>
                                    <TextBlock Grid.Row="1" Text="{Binding Description}" 
                                             FontSize="12" Opacity="0.7" TextWrapping="Wrap"/>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" 
                              HorizontalAlignment="Center" Margin="8">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="设为默认" Click="SetDefault_Click">
                            <materialDesign:PackIcon Kind="Star"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="删除配置" Click="DeleteConfig_Click">
                            <materialDesign:PackIcon Kind="Delete"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧配置编辑区域 -->
            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <!-- 基本信息 -->
                        <TextBlock Text="基本信息" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="ConfigNameTextBox" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="配置名称"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,8,0"/>
                            
                            <CheckBox x:Name="IsDefaultCheckBox" Grid.Column="1"
                                    Content="设为默认配置" Margin="8,0,0,0"
                                    VerticalAlignment="Center"/>
                        </Grid>

                        <TextBox x:Name="ConfigDescriptionTextBox"
                               materialDesign:HintAssist.Hint="配置描述"
                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,24"/>

                        <!-- 提示词配置选项卡 -->
                        <TabControl x:Name="PromptTabControl" Height="400">
                            <TabItem Header="全书大纲">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="OverallOutlinePromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="全书大纲生成提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                            
                            <TabItem Header="世界设定">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="WorldSettingPromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="世界设定生成提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                            
                            <TabItem Header="卷宗大纲">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="VolumeOutlinePromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="卷宗大纲生成提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                            
                            <TabItem Header="章节细纲">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="ChapterOutlinePromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="章节细纲生成提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                            
                            <TabItem Header="章节正文">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="ChapterContentPromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="章节正文生成提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                            
                            <TabItem Header="时间线更新">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <TextBox x:Name="TimelineUpdatePromptTextBox"
                                           TextWrapping="Wrap" AcceptsReturn="True"
                                           VerticalScrollBarVisibility="Auto"
                                           materialDesign:HintAssist.Hint="时间线更新提示词模板"
                                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                           MinHeight="350" Margin="8"/>
                                </ScrollViewer>
                            </TabItem>
                        </TabControl>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                  HorizontalAlignment="Right" Margin="16">
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                  Margin="0,0,8,0"
                  Click="Save_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,4,0"/>
                        <TextBlock Text="保存"/>
                    </StackPanel>
                </Button.Content>
            </Button>
            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                  Click="Cancel_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Margin="0,0,4,0"/>
                        <TextBlock Text="取消"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>
    </Grid>
</Window>

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 内容质量检查结果
    /// </summary>
    public class ContentQualityResult
    {
        public bool IsValid { get; set; }
        public List<string> Issues { get; set; } = new List<string>();
        public List<string> Warnings { get; set; } = new List<string>();
        public int WordCount { get; set; }
        public ContentType DetectedType { get; set; }
        public string CleanedContent { get; set; } = string.Empty;
    }

    /// <summary>
    /// 内容类型
    /// </summary>
    public enum ContentType
    {
        Unknown,
        NovelContent,      // 小说正文
        Outline,           // 大纲
        Summary,           // 摘要
        Analysis,          // 分析
        ErrorMessage,      // 错误信息
        Empty              // 空内容
    }

    /// <summary>
    /// 内容质量检查服务
    /// </summary>
    public class ContentQualityService
    {
        private readonly ILogger<ContentQualityService> _logger;

        public ContentQualityService(ILogger<ContentQualityService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 检查章节内容质量
        /// </summary>
        public ContentQualityResult CheckChapterContent(string content, int expectedWordCount = 6500)
        {
            var result = new ContentQualityResult();

            if (string.IsNullOrWhiteSpace(content))
            {
                result.IsValid = false;
                result.Issues.Add("内容为空");
                result.DetectedType = ContentType.Empty;
                return result;
            }

            // 清理内容
            result.CleanedContent = CleanContent(content);
            result.WordCount = CountChineseCharacters(result.CleanedContent);

            // 检测内容类型
            result.DetectedType = DetectContentType(result.CleanedContent);

            // 检查基本质量问题
            CheckBasicQuality(result);

            // 检查小说正文规范
            if (result.DetectedType == ContentType.NovelContent)
            {
                CheckNovelContentStandards(result, expectedWordCount);
            }
            else
            {
                result.Issues.Add($"检测到的内容类型不是小说正文: {result.DetectedType}");
                result.IsValid = false;
            }

            return result;
        }

        /// <summary>
        /// 检查大纲内容质量
        /// </summary>
        public ContentQualityResult CheckOutlineContent(string content)
        {
            var result = new ContentQualityResult();

            if (string.IsNullOrWhiteSpace(content))
            {
                result.IsValid = false;
                result.Issues.Add("大纲内容为空");
                result.DetectedType = ContentType.Empty;
                return result;
            }

            result.CleanedContent = CleanContent(content);
            result.WordCount = CountChineseCharacters(result.CleanedContent);
            result.DetectedType = DetectContentType(result.CleanedContent);

            CheckBasicQuality(result);

            // 检查大纲特定要求
            if (result.DetectedType == ContentType.Outline)
            {
                CheckOutlineStandards(result);
            }
            else if (result.DetectedType == ContentType.NovelContent)
            {
                result.Warnings.Add("检测到的内容似乎是小说正文而不是大纲");
            }

            return result;
        }

        /// <summary>
        /// 清理内容
        /// </summary>
        private string CleanContent(string content)
        {
            var cleaned = content;

            // 移除思维链标签和output标签
            var thinkingChainPatterns = new[]
            {
                @"<thinking>.*?</thinking>",
                @"<think>.*?</think>",
                @"<o>.*?</o>",  // 完全移除o标签及其内容
                @"<output>.*?</output>",  // 完全移除output标签及其内容
                @"<ouput>.*?</ouput>",   // 处理常见拼写错误
                @"<response>.*?</response>",  // 移除response标签
            };

            foreach (var pattern in thinkingChainPatterns)
            {
                cleaned = Regex.Replace(cleaned, pattern, "", RegexOptions.Singleline | RegexOptions.IgnoreCase);
            }

            // 额外清理可能遗漏的output标签（不区分大小写，处理各种空格情况）
            cleaned = CleanOutputTagsComprehensive(cleaned);

            // 移除AI内部推理过程
            var internalProcessingPatterns = new[]
            {
                @"1\.\s*思考过程[：:]?.*?(?=2\.|$)",
                @"2\.\s*最终答案[：:]?.*?(?=\n\n|$)",
                @"思考过程[：:]?\s*.*?(?=最终答案|\n\n|$)",
                @"分析用户需求.*?(?=\n\n|$)",
                @"具体分析步骤如下.*?(?=\n\n|$)"
            };

            foreach (var pattern in internalProcessingPatterns)
            {
                cleaned = Regex.Replace(cleaned, pattern, "", RegexOptions.Singleline | RegexOptions.IgnoreCase);
            }

            // 移除错误信息
            var errorPatterns = new[]
            {
                @"由于您提供的.*?缺少具体的情节描述.*?请您补充完整章节的相关信息后再提出请求。",
                @"<\|begin_of_box\|>.*?<\|end_of_box\|>",
                @"（此处为完整的第.*?章小说章节内容.*?）",
                @"\*\*（此处省略.*?\)\*\*",
                @"（此处省略.*?）",
                @"此处省略部分重复内容"
            };

            foreach (var pattern in errorPatterns)
            {
                cleaned = Regex.Replace(cleaned, pattern, "", RegexOptions.Singleline | RegexOptions.IgnoreCase);
            }

            // 清理多余的空行
            cleaned = Regex.Replace(cleaned, @"\n\s*\n\s*\n", "\n\n", RegexOptions.Multiline);
            cleaned = Regex.Replace(cleaned, @"^\s+", "", RegexOptions.Multiline);

            return cleaned.Trim();
        }

        /// <summary>
        /// 全面清理output标签
        /// </summary>
        private string CleanOutputTagsComprehensive(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            try
            {
                // 移除所有可能的output标签变体（不区分大小写，处理各种空格情况）
                var outputPatterns = new[]
                {
                    @"<\s*output\s*>.*?<\s*/\s*output\s*>",  // 标准output标签
                    @"<\s*ouput\s*>.*?<\s*/\s*ouput\s*>",   // 拼写错误的output
                    @"<\s*OUTPUT\s*>.*?<\s*/\s*OUTPUT\s*>", // 大写OUTPUT
                    @"<\s*Output\s*>.*?<\s*/\s*Output\s*>", // 首字母大写
                    @"<\s*response\s*>.*?<\s*/\s*response\s*>", // response标签
                    @"<\s*RESPONSE\s*>.*?<\s*/\s*RESPONSE\s*>", // 大写RESPONSE
                    @"<\s*o\s*>.*?<\s*/\s*o\s*>",           // 简化的o标签
                    @"<\s*O\s*>.*?<\s*/\s*O\s*>",           // 大写O标签
                };

                foreach (var pattern in outputPatterns)
                {
                    content = Regex.Replace(content, pattern, "", RegexOptions.Singleline | RegexOptions.IgnoreCase);
                }

                // 移除单独的开始或结束标签（防止不匹配的标签）
                var singleTagPatterns = new[]
                {
                    @"<\s*/?output\s*>",
                    @"<\s*/?ouput\s*>",
                    @"<\s*/?response\s*>",
                    @"<\s*/?o\s*>",
                };

                foreach (var pattern in singleTagPatterns)
                {
                    content = Regex.Replace(content, pattern, "", RegexOptions.IgnoreCase);
                }

                // 清理可能产生的多余空行
                content = Regex.Replace(content, @"\n\s*\n\s*\n", "\n\n");

                _logger.LogDebug("已全面清理内容中的output标签");
                return content.Trim();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "全面清理output标签时发生错误");
                return content; // 如果清理失败，返回原内容
            }
        }

        /// <summary>
        /// 公共方法：清理章节内容中的output标签
        /// </summary>
        public string CleanOutputTags(string content)
        {
            return CleanOutputTagsComprehensive(content);
        }

        /// <summary>
        /// 检测内容类型
        /// </summary>
        private ContentType DetectContentType(string content)
        {
            if (string.IsNullOrWhiteSpace(content))
                return ContentType.Empty;

            // 检查是否包含错误信息
            var errorIndicators = new[]
            {
                "由于您提供的",
                "缺少具体的情节描述",
                "请您补充完整章节",
                "生成失败",
                "错误信息"
            };

            if (errorIndicators.Any(indicator => content.Contains(indicator)))
                return ContentType.ErrorMessage;

            // 检查是否是大纲格式
            var outlineIndicators = new[]
            {
                @"###\s*",
                @"\*\*.*?\*\*",
                @"第.*?章.*?：",
                @"章节.*?：",
                @"核心亮点",
                @"场景与氛围",
                @"人物动态",
                @"剧情发展"
            };

            var outlineMatches = outlineIndicators.Count(pattern => Regex.IsMatch(content, pattern));
            if (outlineMatches >= 2)
                return ContentType.Outline;

            // 检查是否是分析内容
            var analysisIndicators = new[]
            {
                "分析",
                "总结",
                "要点",
                "关键",
                "核心要素"
            };

            var analysisMatches = analysisIndicators.Count(indicator => content.Contains(indicator));
            if (analysisMatches >= 2 && content.Length < 1000)
                return ContentType.Analysis;

            // 检查是否是小说正文
            var novelIndicators = new[]
            {
                "说道", "说着", "想到", "看到", "听到",
                """, """, "'", "'",
                "心中", "眼中", "脸上",
                "突然", "慢慢", "渐渐"
            };

            var novelMatches = novelIndicators.Count(indicator => content.Contains(indicator));
            if (novelMatches >= 3)
                return ContentType.NovelContent;

            return ContentType.Unknown;
        }

        /// <summary>
        /// 检查基本质量
        /// </summary>
        private void CheckBasicQuality(ContentQualityResult result)
        {
            var content = result.CleanedContent;

            // 检查长度
            if (content.Length < 100)
            {
                result.Issues.Add("内容过短，可能不完整");
            }

            // 检查是否包含占位符
            var placeholders = new[]
            {
                "...", "省略", "待续", "未完成", "TODO", "占位符"
            };

            foreach (var placeholder in placeholders)
            {
                if (content.Contains(placeholder))
                {
                    result.Warnings.Add($"包含占位符内容: {placeholder}");
                }
            }

            // 检查重复内容
            var lines = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var duplicateLines = lines.GroupBy(line => line.Trim())
                                    .Where(group => group.Count() > 3 && group.Key.Length > 10)
                                    .Select(group => group.Key);

            foreach (var duplicateLine in duplicateLines)
            {
                result.Warnings.Add($"检测到重复内容: {duplicateLine.Substring(0, Math.Min(50, duplicateLine.Length))}...");
            }
        }

        /// <summary>
        /// 检查小说正文标准
        /// </summary>
        private void CheckNovelContentStandards(ContentQualityResult result, int expectedWordCount)
        {
            var content = result.CleanedContent;

            // 检查字数
            var wordCountRatio = (double)result.WordCount / expectedWordCount;
            if (wordCountRatio < 0.8)
            {
                result.Issues.Add($"字数不足，当前{result.WordCount}字，期望{expectedWordCount}字");
            }
            else if (wordCountRatio > 1.2)
            {
                result.Warnings.Add($"字数超标，当前{result.WordCount}字，期望{expectedWordCount}字");
            }

            // 检查对话格式
            var dialogueCount = Regex.Matches(content, @"[""].*?[""]").Count;
            if (dialogueCount == 0 && result.WordCount > 1000)
            {
                result.Warnings.Add("缺少对话内容，可能影响小说的生动性");
            }

            // 检查段落结构
            var paragraphs = content.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            if (paragraphs.Length < 5 && result.WordCount > 2000)
            {
                result.Warnings.Add("段落过少，建议增加段落分割");
            }

            // 检查是否包含标题或结构性标记
            var structuralMarkers = new[]
            {
                @"^第.*?章",
                @"^###",
                @"^\*\*.*?\*\*$",
                @"^章节.*?："
            };

            foreach (var marker in structuralMarkers)
            {
                if (Regex.IsMatch(content, marker, RegexOptions.Multiline))
                {
                    result.Issues.Add("包含标题或结构性标记，不符合小说正文格式");
                    break;
                }
            }

            result.IsValid = result.Issues.Count == 0;
        }

        /// <summary>
        /// 检查大纲标准
        /// </summary>
        private void CheckOutlineStandards(ContentQualityResult result)
        {
            var content = result.CleanedContent;

            // 大纲应该有清晰的结构
            var hasStructure = Regex.IsMatch(content, @"###|第.*?章|\*\*.*?\*\*");
            if (!hasStructure)
            {
                result.Warnings.Add("大纲缺少清晰的结构标记");
            }

            result.IsValid = result.Issues.Count == 0;
        }

        /// <summary>
        /// 计算中文字符数
        /// </summary>
        private int CountChineseCharacters(string text)
        {
            if (string.IsNullOrEmpty(text))
                return 0;

            return text.Where(c => c >= 0x4e00 && c <= 0x9fff).Count();
        }
    }
}

<Window x:Class="DocumentCreationSystem.Views.CreativeRequestDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="按要求创作" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <materialDesign:PackIcon Kind="Creation" Height="24" Width="24"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="AI创作助手" VerticalAlignment="Center"
                         FontSize="18" FontWeight="Medium"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button x:Name="TemplateButton" Content="使用模板" 
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,8,0" Click="Template_Click"/>
                    <Button x:Name="HistoryButton" Content="历史记录" 
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Click="History_Click"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="24">
                
                <!-- 创作类型选择 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock Text="创作类型" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <UniformGrid Columns="3" Rows="2">
                            <RadioButton x:Name="ChapterRadio" Content="章节创作" 
                                       GroupName="CreationType" IsChecked="True"
                                       Margin="8" Checked="CreationType_Checked"/>
                            <RadioButton x:Name="ContinueRadio" Content="续写内容" 
                                       GroupName="CreationType"
                                       Margin="8" Checked="CreationType_Checked"/>
                            <RadioButton x:Name="PolishRadio" Content="润色修改" 
                                       GroupName="CreationType"
                                       Margin="8" Checked="CreationType_Checked"/>
                            <RadioButton x:Name="ExpandRadio" Content="内容扩写" 
                                       GroupName="CreationType"
                                       Margin="8" Checked="CreationType_Checked"/>
                            <RadioButton x:Name="OutlineRadio" Content="大纲生成" 
                                       GroupName="CreationType"
                                       Margin="8" Checked="CreationType_Checked"/>
                            <RadioButton x:Name="CustomRadio" Content="自定义创作" 
                                       GroupName="CreationType"
                                       Margin="8" Checked="CreationType_Checked"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 创作要求输入 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2" Margin="0,0,0,16">
                    <StackPanel Margin="16">
                        <TextBlock x:Name="RequestTitle" Text="章节创作要求" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <!-- 基础要求 -->
                        <TextBox x:Name="BasicRequestTextBox"
                               materialDesign:HintAssist.Hint="请详细描述您的创作要求..."
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               MinLines="4"
                               MaxLines="8"
                               Margin="0,0,0,16"/>

                        <!-- 高级选项 -->
                        <Expander Header="高级选项" IsExpanded="False" Margin="0,0,0,16">
                            <StackPanel Margin="16,8,0,0">
                                <!-- 字数要求 -->
                                <Grid Margin="0,0,0,16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="目标字数:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBox Grid.Column="1" x:Name="TargetWordCountTextBox"
                                           Text="2000" Width="100"
                                           materialDesign:HintAssist.Hint="字数"/>
                                    
                                    <TextBlock Grid.Column="2" Text="写作风格:" VerticalAlignment="Center" Margin="16,0,8,0"/>
                                    <ComboBox Grid.Column="3" x:Name="WritingStyleComboBox"
                                            materialDesign:HintAssist.Hint="选择风格">
                                        <ComboBoxItem Content="自然流畅" IsSelected="True"/>
                                        <ComboBoxItem Content="文学性强"/>
                                        <ComboBoxItem Content="通俗易懂"/>
                                        <ComboBoxItem Content="古典雅致"/>
                                        <ComboBoxItem Content="现代简洁"/>
                                    </ComboBox>
                                </Grid>

                                <!-- 情感基调 -->
                                <Grid Margin="0,0,0,16">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" Text="情感基调:" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <ComboBox Grid.Column="1" x:Name="EmotionalToneComboBox"
                                            materialDesign:HintAssist.Hint="选择基调">
                                        <ComboBoxItem Content="中性平和" IsSelected="True"/>
                                        <ComboBoxItem Content="轻松愉快"/>
                                        <ComboBoxItem Content="紧张刺激"/>
                                        <ComboBoxItem Content="深沉严肃"/>
                                        <ComboBoxItem Content="温馨感人"/>
                                        <ComboBoxItem Content="悬疑神秘"/>
                                    </ComboBox>
                                    
                                    <TextBlock Grid.Column="2" Text="创作温度:" VerticalAlignment="Center" Margin="16,0,8,0"/>
                                    <Slider Grid.Column="3" x:Name="TemperatureSlider"
                                          Minimum="0.1" Maximum="1.5" Value="0.8"
                                          TickFrequency="0.1" IsSnapToTickEnabled="True"
                                          materialDesign:SliderAssist.OnlyShowFocusVisualWhileDragging="True"/>
                                </Grid>

                                <!-- 参考内容 -->
                                <TextBox x:Name="ReferenceContentTextBox"
                                       materialDesign:HintAssist.Hint="参考内容或上下文（可选）"
                                       materialDesign:HintAssist.IsFloating="True"
                                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                       AcceptsReturn="True"
                                       TextWrapping="Wrap"
                                       MinLines="3"
                                       MaxLines="6"/>
                            </StackPanel>
                        </Expander>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 预览和生成 -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp2">
                    <StackPanel Margin="16">
                        <DockPanel Margin="0,0,0,16">
                            <TextBlock Text="生成预览" FontWeight="Medium" FontSize="16" VerticalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                                <Button x:Name="GenerateButton" Content="开始生成" 
                                      Style="{StaticResource MaterialDesignRaisedButton}"
                                      Margin="0,0,8,0" Click="Generate_Click"/>
                                <Button x:Name="RegenerateButton" Content="重新生成" 
                                      Style="{StaticResource MaterialDesignOutlinedButton}"
                                      IsEnabled="False" Click="Regenerate_Click"/>
                            </StackPanel>
                        </DockPanel>
                        
                        <!-- 生成进度 -->
                        <ProgressBar x:Name="GenerationProgressBar" 
                                   IsIndeterminate="False" 
                                   Visibility="Collapsed"
                                   Margin="0,0,0,16"/>
                        
                        <!-- 生成结果 -->
                        <TextBox x:Name="GeneratedContentTextBox"
                               materialDesign:HintAssist.Hint="生成的内容将显示在这里..."
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               VerticalScrollBarVisibility="Auto"
                               MinHeight="200"
                               MaxHeight="400"
                               IsReadOnly="True"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="16">
            <DockPanel>
                <TextBlock x:Name="StatusText" Text="就绪" VerticalAlignment="Center" 
                         DockPanel.Dock="Left" Foreground="White"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Content="取消" Style="{StaticResource MaterialDesignFlatButton}"
                          Margin="0,0,8,0" Click="Cancel_Click"/>
                    <Button x:Name="ApplyButton" Content="应用到编辑器" 
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          IsEnabled="False" Click="Apply_Click"/>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>
    </Grid>
</Window>

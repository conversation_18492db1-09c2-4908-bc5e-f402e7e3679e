using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;
using Microsoft.Extensions.Logging;
using System;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试 Qwen 思维链格式支持
    /// </summary>
    public class TestQwenThinkingChain
    {
        private readonly IThinkingChainService _thinkingChainService;

        public TestQwenThinkingChain()
        {
            // 创建日志工厂
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));
            
            var logger = loggerFactory.CreateLogger<ThinkingChainService>();
            _thinkingChainService = new ThinkingChainService(logger);
        }

        public static void Main(string[] args)
        {
            var test = new TestQwenThinkingChain();
            test.RunTests();
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        public void RunTests()
        {
            Console.WriteLine("=== Qwen 思维链格式测试 ===\n");

            TestQwenFormat();
            TestMixedFormats();
            TestEdgeCases();

            Console.WriteLine("=== 所有测试完成 ===");
        }

        /// <summary>
        /// 测试 Qwen 格式 (...</think>)
        /// </summary>
        private void TestQwenFormat()
        {
            Console.WriteLine("1. 测试 Qwen 思维链格式 (...</think>)");
            Console.WriteLine("----------------------------------------");
            
            var input = @"我需要分析这个问题。

首先，让我理解用户的需求。用户想要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。

然后，我需要查看当前的实现，看看如何添加这种新格式的支持。

最后，我需要修改正则表达式和相关的处理逻辑。
</think>

根据您的需求，我需要在现有的思维链过滤功能中添加对 ...</think> 格式的支持。这是 Qwen 模型使用的思维链格式。

让我为您实现这个功能：

1. 添加新的正则表达式模式
2. 更新解析逻辑
3. 测试新格式的支持";

            var filter = new ThinkingChainFilter
            {
                EnableFilter = true,
                Mode = FilterMode.RemoveThinkingChain
            };

            var result = _thinkingChainService.ProcessThinkingChainResponse(input, filter);
            
            Console.WriteLine($"处理状态: {result.Status}");
            Console.WriteLine($"包含思维链: {result.ContainsThinkingChain}");
            Console.WriteLine($"思维链步骤数: {result.ThinkingStepsCount}");
            Console.WriteLine($"处理后内容长度: {result.ProcessedContent.Length}");
            Console.WriteLine();
            
            Console.WriteLine("处理后内容:");
            Console.WriteLine(result.ProcessedContent.Trim());
            Console.WriteLine();
            
            if (result.OriginalResponse.HasThinkingChain)
            {
                Console.WriteLine("提取的思维链内容:");
                Console.WriteLine(result.OriginalResponse.ThinkingChain);
                Console.WriteLine();
            }
        }

        /// <summary>
        /// 测试混合格式
        /// </summary>
        private void TestMixedFormats()
        {
            Console.WriteLine("2. 测试混合格式支持");
            Console.WriteLine("----------------------------------------");
            
            // 测试标准格式
            TestFormat("标准格式 (<think> + <o>)", @"<think>
这是标准格式的思考过程
</think>

<o>
这是标准格式的输出
</o>");

            // 测试传统格式
            TestFormat("传统格式 (<thinking>)", @"<thinking>
这是传统格式的思考过程
</thinking>

这是传统格式的输出");

            // 测试 Qwen 格式
            TestFormat("Qwen 格式 (...</think>)", @"这是 Qwen 格式的思考过程
分析问题的各个方面
</think>

这是 Qwen 格式的输出");
        }

        /// <summary>
        /// 测试边界情况
        /// </summary>
        private void TestEdgeCases()
        {
            Console.WriteLine("3. 测试边界情况");
            Console.WriteLine("----------------------------------------");
            
            // 测试空内容
            TestFormat("空思维链", @"</think>

这是输出内容");

            // 测试多行思维链
            TestFormat("多行思维链", @"第一行思考
第二行思考
第三行思考
更多的思考内容...
</think>

这是最终的输出结果");

            // 测试包含特殊字符
            TestFormat("包含特殊字符", @"思考过程中包含 <标签> 和 ""引号"" 以及其他特殊字符：!@#$%^&*()
</think>

输出结果");
        }

        private void TestFormat(string formatName, string input)
        {
            Console.WriteLine($"测试 {formatName}:");
            
            var filter = new ThinkingChainFilter
            {
                EnableFilter = true,
                Mode = FilterMode.RemoveThinkingChain
            };

            var result = _thinkingChainService.ProcessThinkingChainResponse(input, filter);
            
            Console.WriteLine($"  包含思维链: {result.ContainsThinkingChain}");
            Console.WriteLine($"  处理状态: {result.Status}");
            Console.WriteLine($"  处理后内容: {result.ProcessedContent.Trim()}");
            Console.WriteLine();
        }
    }
}

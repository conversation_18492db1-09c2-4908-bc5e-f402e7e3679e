using DocumentCreationSystem.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.IO;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 世界设定服务
    /// </summary>
    public class WorldSettingService
    {
        private readonly ILogger<WorldSettingService> _logger;
        private readonly IDataStorageService _dataStorage;
        private readonly IAIService _aiService;

        public WorldSettingService(
            ILogger<WorldSettingService> logger,
            IDataStorageService dataStorage,
            IAIService aiService)
        {
            _logger = logger;
            _dataStorage = dataStorage;
            _aiService = aiService;
        }

        /// <summary>
        /// 生成世界设定
        /// </summary>
        public async Task<WorldSetting> GenerateWorldSettingAsync(int novelProjectId, string creativeDirection)
        {
            try
            {
                _logger.LogInformation($"开始生成世界设定，小说项目ID: {novelProjectId}");

                var worldSetting = new WorldSetting
                {
                    NovelProjectId = novelProjectId,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // 生成世界观设定
                worldSetting.WorldView = await GenerateWorldViewAsync(creativeDirection);

                // 生成修炼体系
                worldSetting.Cultivation = await GenerateCultivationSystemAsync(creativeDirection);

                // 生成政治体系
                worldSetting.Political = await GeneratePoliticalSystemAsync(creativeDirection);

                // 生成货币体系
                worldSetting.Currency = await GenerateCurrencySystemAsync(creativeDirection);

                // 生成商业体系
                worldSetting.Business = await GenerateBusinessSystemAsync(creativeDirection);

                // 生成种族类别
                worldSetting.Races = await GenerateRaceCategoriesAsync(creativeDirection);

                // 生成功法体系
                worldSetting.Techniques = await GenerateTechniqueSystemAsync(creativeDirection);

                // 生成装备体系
                worldSetting.Equipment = await GenerateEquipmentSystemAsync(creativeDirection);

                // 生成宠物体系
                worldSetting.Pets = await GeneratePetSystemAsync(creativeDirection);

                // 生成地图结构
                worldSetting.Map = await GenerateMapStructureAsync(creativeDirection);

                // 生成维度结构
                worldSetting.Dimensions = await GenerateDimensionStructureAsync(creativeDirection);

                // 生成灵宝体系
                worldSetting.Treasures = await GenerateTreasureSystemAsync(creativeDirection);

                // 生成生民体系
                worldSetting.Population = await GeneratePopulationSystemAsync(creativeDirection);

                // 生成司法体系
                worldSetting.Judicial = await GenerateJudicialSystemAsync(creativeDirection);

                // 生成职业体系
                worldSetting.Professions = await GenerateProfessionSystemAsync(creativeDirection);

                // 生成经济系统
                worldSetting.Economy = await GenerateEconomicSystemAsync(creativeDirection);

                // 生成关系网络
                worldSetting.Relationships = await GenerateRelationshipNetworkAsync(creativeDirection);

                // 生成新增的详细设定
                _logger.LogInformation("开始生成详细角色设定...");
                worldSetting.Characters = await GenerateCharacterSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成势力设定...");
                worldSetting.Factions = await GenerateFactionSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成功法详细设定...");
                worldSetting.TechniqueDetails = await GenerateTechniqueSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成秘境设定...");
                worldSetting.SecretRealms = await GenerateSecretRealmSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成详细地图设定...");
                worldSetting.DetailedMaps = await GenerateDetailedMapSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成历史事件设定...");
                worldSetting.HistoricalEvents = await GenerateHistoricalEventSettingsAsync(creativeDirection);

                _logger.LogInformation("开始生成传说神话设定...");
                worldSetting.LegendsMyths = await GenerateLegendMythSettingsAsync(creativeDirection);

                _logger.LogInformation($"世界设定生成完成，小说项目ID: {novelProjectId}");
                return worldSetting;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成世界设定失败，小说项目ID: {novelProjectId}");
                throw;
            }
        }

        /// <summary>
        /// 保存世界设定到项目文件夹
        /// </summary>
        public async Task SaveWorldSettingToProjectAsync(WorldSetting worldSetting, string projectPath)
        {
            try
            {
                var settingsPath = Path.Combine(projectPath, "设定");
                if (!Directory.Exists(settingsPath))
                {
                    Directory.CreateDirectory(settingsPath);
                }

                var worldSettingFile = Path.Combine(settingsPath, "世界设定.json");
                var json = JsonConvert.SerializeObject(worldSetting, Formatting.Indented);
                await File.WriteAllTextAsync(worldSettingFile, json);

                _logger.LogInformation($"世界设定已保存到: {worldSettingFile}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存世界设定到项目文件夹失败");
                throw;
            }
        }

        /// <summary>
        /// 从项目文件夹加载世界设定
        /// </summary>
        public async Task<WorldSetting?> LoadWorldSettingFromProjectAsync(string projectPath)
        {
            try
            {
                var worldSettingFile = Path.Combine(projectPath, "设定", "世界设定.json");
                if (!File.Exists(worldSettingFile))
                {
                    return null;
                }

                var json = await File.ReadAllTextAsync(worldSettingFile);
                var worldSetting = JsonConvert.DeserializeObject<WorldSetting>(json);

                _logger.LogInformation($"世界设定已从项目文件夹加载: {worldSettingFile}");
                return worldSetting;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "从项目文件夹加载世界设定失败");
                return null;
            }
        }

        /// <summary>
        /// 更新小说项目的世界设定信息
        /// </summary>
        public async Task UpdateNovelProjectWorldSettingAsync(int novelProjectId, WorldSetting worldSetting)
        {
            try
            {
                var novelProject = await _dataStorage.GetNovelProjectAsync(novelProjectId);
                if (novelProject == null)
                {
                    throw new InvalidOperationException($"小说项目不存在: {novelProjectId}");
                }

                // 将世界设定序列化为JSON并保存到NovelProject
                novelProject.DetailedWorldSettings = JsonConvert.SerializeObject(worldSetting);
                novelProject.UpdatedAt = DateTime.Now;

                await _dataStorage.UpdateNovelProjectAsync(novelProject);

                _logger.LogInformation($"小说项目世界设定已更新，项目ID: {novelProjectId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"更新小说项目世界设定失败，项目ID: {novelProjectId}");
                throw;
            }
        }

        /// <summary>
        /// 生成世界观设定
        /// </summary>
        private async Task<WorldViewSetting> GenerateWorldViewAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的世界观设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 地理环境：包括大陆、海洋、山脉、河流等地理特征
2. 历史背景：重要的历史事件、朝代更替、重大变革
3. 文化特色：民族文化、宗教信仰、传统习俗
4. 自然法则：这个世界的基本物理法则、魔法法则等
5. 时间体系：历法、时间计算方式
6. 语言文字：主要语言、文字系统

请用详细的描述，每个方面至少200字。";

            var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
            
            // 解析AI响应并构建WorldViewSetting对象
            return ParseWorldViewResponse(response);
        }

        /// <summary>
        /// 生成修炼体系
        /// </summary>
        private async Task<CultivationSystem> GenerateCultivationSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的修炼体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 能力体系描述：修炼的基本原理和能力来源
2. 等级划分：从低到高的修炼境界，每个境界的特点
3. 修炼方法：不同的修炼方式和技巧
4. 突破条件：境界突破的条件和要求
5. 修炼资源：需要的材料、环境、机缘等

请详细描述每个等级的名称、特点和能力。";

            var response = await _aiService.GenerateTextAsync(prompt, 3000, 0.8f);
            
            return ParseCultivationSystemResponse(response);
        }

        /// <summary>
        /// 生成政治体系
        /// </summary>
        private async Task<PoliticalSystem> GeneratePoliticalSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的政治体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 主要国家：3-5个主要国家，包括名称、政治制度、统治者
2. 重要势力：宗门、家族、组织等，包括名称、类型、影响力
3. 组织结构：政府机构、权力层级
4. 权力分配：不同阶层的权力和职责
5. 法律体系：主要法律条文和执行机制

请详细描述每个国家和势力的特点。";

            var response = await _aiService.GenerateTextAsync(prompt, 3000, 0.8f);
            
            return ParsePoliticalSystemResponse(response);
        }

        /// <summary>
        /// 生成货币体系
        /// </summary>
        private async Task<CurrencySystem> GenerateCurrencySystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的货币体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 货币制度：基本的货币体系和价值标准
2. 货币类型：不同类型的货币，如金币、银币、灵石等
3. 汇率体系：不同货币之间的兑换比例
4. 金融机构：银行、钱庄等金融组织
5. 支付方式：交易和支付的常用方式

请详细描述每种货币的价值和用途。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            
            return ParseCurrencySystemResponse(response);
        }

        /// <summary>
        /// 生成商业体系
        /// </summary>
        private async Task<BusinessSystem> GenerateBusinessSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的商业体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 主要贸易路线：重要的商业通道和贸易路线
2. 商会组织：商人组织、商会联盟
3. 市场机制：交易规则、价格形成机制
4. 商业法规：贸易相关的法律法规
5. 主要商品：热门的贸易商品和特产

请详细描述商业活动的特点和规则。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            
            return ParseBusinessSystemResponse(response);
        }

        /// <summary>
        /// 生成种族类别
        /// </summary>
        private async Task<List<RaceCategory>> GenerateRaceCategoriesAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的种族类别：

创作方向：{creativeDirection}

请生成3-5个不同的种族，每个种族包括：
1. 种族名称
2. 种族特性：外貌、体质、寿命等
3. 分布地区：主要居住区域
4. 与其他种族关系：友好、敌对、中立等
5. 文化差异：独特的文化特点
6. 天赋能力：种族特有的能力

请详细描述每个种族的特点。";

            var response = await _aiService.GenerateTextAsync(prompt, 2500, 0.8f);
            return ParseRaceCategoriesResponse(response);
        }

        /// <summary>
        /// 生成功法体系
        /// </summary>
        private async Task<TechniqueSystem> GenerateTechniqueSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的功法体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 功法分类：不同类型的功法，如攻击、防御、辅助等
2. 修炼方法：功法的修炼方式和要求
3. 技能招式：具体的技能和招式
4. 传承体系：功法的传承方式和等级

请详细描述功法的分类和特点。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseTechniqueSystemResponse(response);
        }

        /// <summary>
        /// 生成装备体系
        /// </summary>
        private async Task<EquipmentSystem> GenerateEquipmentSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的装备体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 装备分类：武器、防具、饰品等类型
2. 强化系统：装备强化的方式和等级
3. 套装效果：套装装备的特殊效果
4. 制作配方：装备制作的材料和方法

请详细描述装备的分类和强化机制。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseEquipmentSystemResponse(response);
        }

        /// <summary>
        /// 生成宠物体系
        /// </summary>
        private async Task<PetSystem> GeneratePetSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的宠物体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 宠物分类：不同类型的宠物和灵兽
2. 进化系统：宠物成长和进化机制
3. 技能培养：宠物技能的学习和提升
4. 繁殖机制：宠物繁殖和遗传规则

请详细描述宠物的特点和培养方式。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParsePetSystemResponse(response);
        }

        /// <summary>
        /// 生成地图结构
        /// </summary>
        private async Task<MapStructure> GenerateMapStructureAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的地图结构：

创作方向：{creativeDirection}

请生成以下内容：
1. 地图层级：不同层次的地图区域
2. 地形特征：山脉、河流、森林、沙漠等
3. 区域划分：不同的地理区域和特点
4. 特殊地点：重要的地标和特殊场所

请详细描述地图的结构和特色地点。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseMapStructureResponse(response);
        }

        /// <summary>
        /// 生成维度结构
        /// </summary>
        private async Task<DimensionStructure> GenerateDimensionStructureAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的维度结构：

创作方向：{creativeDirection}

请生成以下内容：
1. 多维度世界：不同维度的世界描述
2. 空间层次：维度的层级结构
3. 维度法则：各维度的特殊规则
4. 传送机制：维度间的传送方式

请详细描述维度的特点和规则。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseDimensionStructureResponse(response);
        }

        /// <summary>
        /// 生成灵宝体系
        /// </summary>
        private async Task<TreasureSystem> GenerateTreasureSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的灵宝体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 灵宝品级：不同等级的灵宝分类
2. 器灵系统：灵宝器灵的特点和能力
3. 炼制方法：灵宝的制作和炼制过程
4. 传承历史：灵宝的历史和传说

请详细描述灵宝的分类和特点。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseTreasureSystemResponse(response);
        }

        /// <summary>
        /// 生成生民体系
        /// </summary>
        private async Task<PopulationSystem> GeneratePopulationSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的生民体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 人口统计：各地区人口分布情况
2. 社会阶层：不同的社会等级和阶层
3. 生活方式：普通民众的生活方式
4. 文化习俗：民间的传统习俗
5. 不同维度的人口分布：各维度的人口特点

请详细描述社会结构和民众生活。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParsePopulationSystemResponse(response);
        }

        /// <summary>
        /// 生成司法体系
        /// </summary>
        private async Task<JudicialSystem> GenerateJudicialSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的司法体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 法院体系：司法机构的组织结构
2. 审判程序：案件审理的流程
3. 执法机构：负责执法的组织
4. 法律条文：主要的法律规定
5. 不同维度的司法制度：各维度的司法特点

请详细描述司法制度和执法机制。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseJudicialSystemResponse(response);
        }

        /// <summary>
        /// 生成职业体系
        /// </summary>
        private async Task<ProfessionSystem> GenerateProfessionSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的职业体系：

创作方向：{creativeDirection}

请生成以下内容：
1. 职业分类：不同类型的职业和工作
2. 技能要求：各职业所需的技能
3. 晋升路径：职业发展的路径
4. 行业组织：职业相关的组织机构
5. 不同维度的职业结构：各维度的职业特点

请详细描述职业的分类和发展机制。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseProfessionSystemResponse(response);
        }

        /// <summary>
        /// 生成经济系统
        /// </summary>
        private async Task<EconomicSystem> GenerateEconomicSystemAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的经济系统：

创作方向：{creativeDirection}

请生成以下内容：
1. 资源分配：资源的分配机制
2. 交易方式：常见的交易和贸易方式
3. 经济政策：政府的经济管理政策
4. 主要产业：重要的经济产业
5. 经济周期：经济发展的周期性特点

请详细描述经济运行机制。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseEconomicSystemResponse(response);
        }

        /// <summary>
        /// 生成关系网络
        /// </summary>
        private async Task<RelationshipNetwork> GenerateRelationshipNetworkAsync(string creativeDirection)
        {
            var prompt = $@"根据以下创作方向，生成详细的关系网络：

创作方向：{creativeDirection}

请生成以下内容：
1. 势力关系：主要势力之间的关系
2. 家族关系：重要家族之间的关系
3. 组织间关系：不同组织的合作关系
4. 关系图谱：整体的关系网络描述

请详细描述各种关系的特点和影响。";

            var response = await _aiService.GenerateTextAsync(prompt, 2000, 0.8f);
            return ParseRelationshipNetworkResponse(response);
        }

        /// <summary>
        /// 解析世界观设定响应
        /// </summary>
        private WorldViewSetting ParseWorldViewResponse(string response)
        {
            // 这里需要实现解析AI响应的逻辑
            // 可以使用正则表达式或其他方法来提取结构化信息
            return new WorldViewSetting
            {
                Geography = ExtractSection(response, "地理环境"),
                History = ExtractSection(response, "历史背景"),
                Culture = ExtractSection(response, "文化特色"),
                NaturalLaws = ExtractSection(response, "自然法则"),
                TimeSystem = ExtractSection(response, "时间体系"),
                Languages = ExtractSection(response, "语言文字")
            };
        }

        /// <summary>
        /// 从响应中提取指定章节的内容
        /// </summary>
        private string ExtractSection(string response, string sectionName)
        {
            // 简单的文本提取逻辑，实际实现可能需要更复杂的解析
            var lines = response.Split('\n');
            var sectionLines = new List<string>();
            bool inSection = false;

            foreach (var line in lines)
            {
                if (line.Contains(sectionName))
                {
                    inSection = true;
                    continue;
                }

                if (inSection)
                {
                    if (line.Trim().StartsWith("1.") || line.Trim().StartsWith("2.") || 
                        line.Trim().StartsWith("3.") || line.Trim().StartsWith("4.") || 
                        line.Trim().StartsWith("5.") || line.Trim().StartsWith("6."))
                    {
                        if (sectionLines.Count > 0) break; // 遇到下一个编号，结束当前章节
                    }
                    sectionLines.Add(line);
                }
            }

            return string.Join("\n", sectionLines).Trim();
        }

        /// <summary>
        /// 解析修炼体系响应
        /// </summary>
        private CultivationSystem ParseCultivationSystemResponse(string response)
        {
            return new CultivationSystem
            {
                AbilitySystem = ExtractSection(response, "能力体系"),
                Methods = ExtractListItems(response, "修炼方法"),
                BreakthroughConditions = ExtractSection(response, "突破条件"),
                Resources = ExtractListItems(response, "修炼资源"),
                Levels = ParseCultivationLevels(response)
            };
        }

        /// <summary>
        /// 解析政治体系响应
        /// </summary>
        private PoliticalSystem ParsePoliticalSystemResponse(string response)
        {
            return new PoliticalSystem
            {
                Nations = ParseNations(response),
                Factions = ParseFactions(response),
                OrganizationStructure = ExtractSection(response, "组织结构"),
                PowerDistribution = ExtractSection(response, "权力分配"),
                LegalSystem = ExtractSection(response, "法律体系")
            };
        }

        /// <summary>
        /// 解析货币体系响应
        /// </summary>
        private CurrencySystem ParseCurrencySystemResponse(string response)
        {
            return new CurrencySystem
            {
                MonetarySystem = ExtractSection(response, "货币制度"),
                Currencies = ParseCurrencies(response),
                ExchangeRates = ExtractSection(response, "汇率体系"),
                FinancialInstitutions = ExtractListItems(response, "金融机构"),
                PaymentMethods = ExtractListItems(response, "支付方式")
            };
        }

        /// <summary>
        /// 解析商业体系响应
        /// </summary>
        private BusinessSystem ParseBusinessSystemResponse(string response)
        {
            return new BusinessSystem
            {
                TradeRoutes = ExtractListItems(response, "贸易路线"),
                MerchantGuilds = ExtractListItems(response, "商会组织"),
                MarketMechanism = ExtractSection(response, "市场机制"),
                BusinessRegulations = ExtractSection(response, "商业法规"),
                MainGoods = ExtractListItems(response, "主要商品")
            };
        }

        /// <summary>
        /// 解析种族类别响应
        /// </summary>
        private List<RaceCategory> ParseRaceCategoriesResponse(string response)
        {
            var races = new List<RaceCategory>();
            // 简化的解析逻辑，实际实现需要更复杂的文本分析
            var sections = response.Split(new[] { "种族名称", "种族：" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var section in sections.Skip(1))
            {
                var race = new RaceCategory
                {
                    Name = ExtractFirstLine(section),
                    Characteristics = ExtractListItems(section, "种族特性"),
                    Distribution = ExtractSection(section, "分布地区"),
                    Relationships = ExtractSection(section, "关系"),
                    CulturalDifferences = ExtractSection(section, "文化差异"),
                    TalentAbilities = ExtractListItems(section, "天赋能力")
                };
                races.Add(race);
            }

            return races;
        }

        /// <summary>
        /// 解析功法体系响应
        /// </summary>
        private TechniqueSystem ParseTechniqueSystemResponse(string response)
        {
            return new TechniqueSystem
            {
                Categories = ParseTechniqueCategories(response),
                CultivationMethods = ExtractSection(response, "修炼方法"),
                Skills = ParseSkills(response),
                InheritanceSystem = ExtractSection(response, "传承体系")
            };
        }

        /// <summary>
        /// 解析装备体系响应
        /// </summary>
        private EquipmentSystem ParseEquipmentSystemResponse(string response)
        {
            return new EquipmentSystem
            {
                Categories = ParseEquipmentCategories(response),
                EnhancementSystem = ExtractSection(response, "强化系统"),
                SetEffects = ParseSetEffects(response),
                CraftingRecipes = ParseCraftingRecipes(response)
            };
        }

        /// <summary>
        /// 解析宠物体系响应
        /// </summary>
        private PetSystem ParsePetSystemResponse(string response)
        {
            return new PetSystem
            {
                Categories = ParsePetCategories(response),
                EvolutionSystem = ExtractSection(response, "进化系统"),
                SkillTraining = ExtractSection(response, "技能培养"),
                BreedingMechanism = ExtractSection(response, "繁殖机制")
            };
        }

        /// <summary>
        /// 解析地图结构响应
        /// </summary>
        private MapStructure ParseMapStructureResponse(string response)
        {
            return new MapStructure
            {
                Levels = ParseMapLevels(response),
                TerrainFeatures = ParseTerrainFeatures(response),
                Regions = ParseRegions(response),
                SpecialLocations = ParseSpecialLocations(response)
            };
        }

        /// <summary>
        /// 解析维度结构响应
        /// </summary>
        private DimensionStructure ParseDimensionStructureResponse(string response)
        {
            return new DimensionStructure
            {
                MultidimensionalWorld = ExtractSection(response, "多维度世界"),
                SpatialLayers = ParseSpatialLayers(response),
                DimensionalLaws = ExtractSection(response, "维度法则"),
                TransportationMechanism = ExtractSection(response, "传送机制")
            };
        }

        /// <summary>
        /// 解析灵宝体系响应
        /// </summary>
        private TreasureSystem ParseTreasureSystemResponse(string response)
        {
            return new TreasureSystem
            {
                Grades = ParseTreasureGrades(response),
                SpiritSystem = ExtractSection(response, "器灵系统"),
                RefinementMethods = ExtractSection(response, "炼制方法"),
                InheritanceHistory = ExtractSection(response, "传承历史")
            };
        }

        /// <summary>
        /// 解析生民体系响应
        /// </summary>
        private PopulationSystem ParsePopulationSystemResponse(string response)
        {
            return new PopulationSystem
            {
                Demographics = ExtractSection(response, "人口统计"),
                SocialClasses = ParseSocialClasses(response),
                Lifestyle = ExtractSection(response, "生活方式"),
                CulturalCustoms = ExtractSection(response, "文化习俗"),
                DimensionalDistribution = ExtractSection(response, "维度的人口分布")
            };
        }

        /// <summary>
        /// 解析司法体系响应
        /// </summary>
        private JudicialSystem ParseJudicialSystemResponse(string response)
        {
            return new JudicialSystem
            {
                CourtSystem = ExtractSection(response, "法院体系"),
                TrialProcedures = ExtractSection(response, "审判程序"),
                LawEnforcementAgencies = ExtractListItems(response, "执法机构"),
                LegalCodes = ExtractSection(response, "法律条文"),
                DimensionalJustice = ExtractSection(response, "维度的司法制度")
            };
        }

        /// <summary>
        /// 解析职业体系响应
        /// </summary>
        private ProfessionSystem ParseProfessionSystemResponse(string response)
        {
            return new ProfessionSystem
            {
                Categories = ParseProfessionCategories(response),
                SkillRequirements = ExtractSection(response, "技能要求"),
                AdvancementPaths = ExtractSection(response, "晋升路径"),
                IndustryOrganizations = ExtractListItems(response, "行业组织"),
                DimensionalProfessions = ExtractSection(response, "维度的职业结构")
            };
        }

        /// <summary>
        /// 解析经济系统响应
        /// </summary>
        private EconomicSystem ParseEconomicSystemResponse(string response)
        {
            return new EconomicSystem
            {
                ResourceAllocation = ExtractSection(response, "资源分配"),
                TradingMethods = ExtractListItems(response, "交易方式"),
                EconomicPolicies = ExtractSection(response, "经济政策"),
                MajorIndustries = ExtractListItems(response, "主要产业"),
                EconomicCycles = ExtractSection(response, "经济周期")
            };
        }

        /// <summary>
        /// 解析关系网络响应
        /// </summary>
        private RelationshipNetwork ParseRelationshipNetworkResponse(string response)
        {
            return new RelationshipNetwork
            {
                FactionRelationships = ParseFactionRelationships(response),
                FamilyRelationships = ParseFamilyRelationships(response),
                OrganizationRelationships = ParseOrganizationRelationships(response),
                RelationshipMap = ExtractSection(response, "关系图谱")
            };
        }

        /// <summary>
        /// 提取列表项目
        /// </summary>
        private List<string> ExtractListItems(string text, string sectionName)
        {
            var items = new List<string>();
            var section = ExtractSection(text, sectionName);

            if (!string.IsNullOrEmpty(section))
            {
                var lines = section.Split('\n');
                foreach (var line in lines)
                {
                    var trimmed = line.Trim();
                    if (!string.IsNullOrEmpty(trimmed))
                    {
                        string content = "";

                        // 处理不同的列表前缀
                        if (trimmed.StartsWith("-") || trimmed.StartsWith("•"))
                        {
                            // 单字符前缀
                            content = trimmed.Length > 1 ? trimmed.Substring(1).Trim() : "";
                        }
                        else if (trimmed.StartsWith("1.") || trimmed.StartsWith("2.") ||
                                trimmed.StartsWith("3.") || trimmed.StartsWith("4.") ||
                                trimmed.StartsWith("5."))
                        {
                            // 双字符前缀
                            content = trimmed.Length > 2 ? trimmed.Substring(2).Trim() : "";
                        }

                        // 只添加非空内容
                        if (!string.IsNullOrEmpty(content))
                        {
                            items.Add(content);
                        }
                    }
                }
            }

            return items;
        }

        /// <summary>
        /// 提取第一行内容
        /// </summary>
        private string ExtractFirstLine(string text)
        {
            var lines = text.Split('\n');
            return lines.FirstOrDefault()?.Trim() ?? "";
        }

        // 简化的解析方法实现，实际项目中需要更详细的实现
        private List<CultivationLevel> ParseCultivationLevels(string response) => new();
        private List<Nation> ParseNations(string response) => new();

        #region 新增详细设定生成方法

        /// <summary>
        /// 生成角色设定
        /// </summary>
        private async Task<CharacterSettings> GenerateCharacterSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的角色设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 主要角色设定（3-5个主角）
   - 角色名称、描述、背景、性格、外貌
   - 初始修炼等级、特殊能力
   - 重要关系、角色发展弧线、关键物品

2. 配角设定（5-8个重要配角）
   - 角色名称、角色定位、描述
   - 与主角的关系、重要程度、出场章节

3. 反派角色设定（2-3个主要反派）
   - 角色名称、反派类型、动机
   - 实力等级、手段、弱点、最终命运

4. 角色命名规则和外貌特征指导原则

5. 性格类型分类体系

请以结构化的方式详细描述每个角色。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseCharacterSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成角色设定失败");
                return new CharacterSettings();
            }
        }

        /// <summary>
        /// 生成势力设定
        /// </summary>
        private async Task<FactionSettings> GenerateFactionSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的势力设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 主要势力设定（3-5个主要势力）
   - 势力名称、类型（宗门、帝国、商会等）
   - 势力描述、领土范围、实力等级
   - 领导层、核心成员、理念信条
   - 资源控制、敌对势力、盟友势力
   - 历史背景

2. 次要势力设定（5-8个次要势力）
   - 势力名称、类型、描述
   - 隶属关系、作用定位

3. 势力关系图谱
   - 敌对关系、联盟关系、中立关系

4. 势力冲突历史
   - 重大冲突事件、原因、结果、影响

5. 当前势力联盟体系

请详细描述每个势力的特色和在故事中的作用。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseFactionSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成势力设定失败");
                return new FactionSettings();
            }
        }

        /// <summary>
        /// 生成功法详细设定
        /// </summary>
        private async Task<TechniqueSettings> GenerateTechniqueSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的功法设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 功法分类体系
   - 攻击类、防御类、辅助类、特殊类等
   - 每类功法的特征和典型效果
   - 修炼难度等级

2. 著名功法列表（10-15个）
   - 功法名称、分类、等级
   - 功法描述、起源、修炼要求
   - 功法效果、稀有程度
   - 已知修炼者、功法弱点

3. 功法等级划分体系
   - 等级名称、描述、威力等级
   - 稀有程度、典型特征

4. 功法传承规则
   - 传承方式、限制条件

5. 功法修炼限制和冲突规则
   - 修炼条件、相互冲突的功法类型

请确保功法体系完整且有层次感。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseTechniqueSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成功法设定失败");
                return new TechniqueSettings();
            }
        }

        /// <summary>
        /// 生成秘境设定
        /// </summary>
        private async Task<SecretRealmSettings> GenerateSecretRealmSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的秘境设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 秘境列表（8-12个秘境）
   - 秘境名称、类型、描述
   - 地理位置、进入方法、危险等级
   - 秘境宝物、守护者、挑战关卡
   - 历史背景、开启条件、时间限制

2. 秘境分类体系
   - 遗迹类、试炼类、天然类等
   - 每类秘境的特征、危险等级
   - 常见奖励类型

3. 秘境开启规则
   - 开启条件、周期性规律
   - 进入人数限制、实力要求

4. 秘境探索指南
   - 探索策略、常见陷阱
   - 生存技巧、团队配合

请确保每个秘境都有独特的特色和挑战。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseSecretRealmSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成秘境设定失败");
                return new SecretRealmSettings();
            }
        }

        /// <summary>
        /// 生成详细地图设定
        /// </summary>
        private async Task<DetailedMapSettings> GenerateDetailedMapSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的地图设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 大陆详细信息（2-3个主要大陆）
   - 大陆名称、地理特征、气候分布
   - 主要区域划分、自然资源
   - 主导势力、文化特色

2. 重要城市设定（10-15个城市）
   - 城市名称、地理位置、城市类型
   - 城市描述、统治势力、特色建筑
   - 人口规模、重要建筑物

3. 危险区域设定（5-8个危险区域）
   - 区域名称、位置、危险类型
   - 危险等级、威胁描述
   - 潜在奖励

4. 交通路线网络
   - 主要路线、起终点、交通方式
   - 行程时间、危险等级、费用

5. 气候分布和地理特征总览

请确保地图设定逻辑合理，地理位置相互呼应。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseDetailedMapSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成详细地图设定失败");
                return new DetailedMapSettings();
            }
        }

        /// <summary>
        /// 生成历史事件设定
        /// </summary>
        private async Task<HistoricalEventSettings> GenerateHistoricalEventSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的历史事件设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 重大历史事件（8-10个事件）
   - 事件名称、时间框架、事件描述
   - 关键人物、涉及势力
   - 事件起因、结果、影响
   - 后续影响和后果

2. 历史时期划分（4-6个时期）
   - 时期名称、时间跨度、时期特征
   - 主要事件、主导势力
   - 文化特色

3. 历史人物（10-12个重要人物）
   - 人物姓名、称号、所处时期
   - 人物描述、主要成就、实力等级
   - 所属势力、历史影响

4. 历史遗迹（6-8个遗迹）
   - 遗迹名称、地理位置、所属时期
   - 遗迹描述、原始用途、当前状态
   - 遗迹宝物、探索难度

请确保历史事件之间有逻辑关联，形成完整的历史脉络。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseHistoricalEventSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成历史事件设定失败");
                return new HistoricalEventSettings();
            }
        }

        /// <summary>
        /// 生成传说神话设定
        /// </summary>
        private async Task<LegendMythSettings> GenerateLegendMythSettingsAsync(string creativeDirection)
        {
            try
            {
                var prompt = $@"基于以下创作方向，生成详细的传说神话设定：

创作方向：{creativeDirection}

请生成以下内容：
1. 创世神话
   - 世界起源的神话故事
   - 创世神祇、创世过程

2. 神祇体系（5-8个神祇）
   - 神祇名称、掌管领域、神祇描述
   - 实力等级、象征符号、信徒群体
   - 神话故事、当前状态

3. 传说故事（8-10个传说）
   - 传说标题、传说类别、故事概要
   - 关键角色、寓意教训
   - 历史依据、流传程度

4. 神器传说（6-8个神器）
   - 神器名称、类型、神器描述
   - 神器能力、起源故事
   - 当前位置、历任主人、诅咒

5. 预言体系（4-6个预言）
   - 预言标题、预言内容、预言者
   - 预言时间、解读难度
   - 应验状态、关键要素

6. 禁忌知识（5-7个禁忌）
   - 知识名称、类型、知识描述
   - 危险等级、后果、起源
   - 已知拥有者、获取限制

请确保神话传说体系完整，与世界观相符。";

                var response = await _aiService.GenerateTextAsync(prompt, 4000, 0.8f);
                return ParseLegendMythSettings(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成传说神话设定失败");
                return new LegendMythSettings();
            }
        }

        #endregion

        #region 新增解析方法

        /// <summary>
        /// 解析角色设定
        /// </summary>
        private CharacterSettings ParseCharacterSettings(string response)
        {
            // 简化实现，实际项目中需要更详细的解析
            var settings = new CharacterSettings();

            // 这里应该实现详细的解析逻辑
            // 暂时返回基本结构

            return settings;
        }

        /// <summary>
        /// 解析势力设定
        /// </summary>
        private FactionSettings ParseFactionSettings(string response)
        {
            var settings = new FactionSettings();
            // 实现解析逻辑
            return settings;
        }

        /// <summary>
        /// 解析功法设定
        /// </summary>
        private TechniqueSettings ParseTechniqueSettings(string response)
        {
            var settings = new TechniqueSettings();
            // 实现解析逻辑
            return settings;
        }

        /// <summary>
        /// 解析秘境设定
        /// </summary>
        private SecretRealmSettings ParseSecretRealmSettings(string response)
        {
            var settings = new SecretRealmSettings();
            // 实现解析逻辑
            return settings;
        }

        /// <summary>
        /// 解析详细地图设定
        /// </summary>
        private DetailedMapSettings ParseDetailedMapSettings(string response)
        {
            var settings = new DetailedMapSettings();
            // 实现解析逻辑
            return settings;
        }

        /// <summary>
        /// 解析历史事件设定
        /// </summary>
        private HistoricalEventSettings ParseHistoricalEventSettings(string response)
        {
            var settings = new HistoricalEventSettings();
            // 实现解析逻辑
            return settings;
        }

        /// <summary>
        /// 解析传说神话设定
        /// </summary>
        private LegendMythSettings ParseLegendMythSettings(string response)
        {
            var settings = new LegendMythSettings();
            // 实现解析逻辑
            return settings;
        }

        #endregion
        private List<Faction> ParseFactions(string response) => new();
        private List<Currency> ParseCurrencies(string response) => new();
        private List<TechniqueCategory> ParseTechniqueCategories(string response) => new();
        private List<Skill> ParseSkills(string response) => new();
        private List<EquipmentCategory> ParseEquipmentCategories(string response) => new();
        private List<SetEffect> ParseSetEffects(string response) => new();
        private List<CraftingRecipe> ParseCraftingRecipes(string response) => new();
        private List<PetCategory> ParsePetCategories(string response) => new();
        private List<MapLevel> ParseMapLevels(string response) => new();
        private List<TerrainFeature> ParseTerrainFeatures(string response) => new();
        private List<Region> ParseRegions(string response) => new();
        private List<SpecialLocation> ParseSpecialLocations(string response) => new();
        private List<SpatialLayer> ParseSpatialLayers(string response) => new();
        private List<TreasureGrade> ParseTreasureGrades(string response) => new();
        private List<SocialClass> ParseSocialClasses(string response) => new();
        private List<ProfessionCategory> ParseProfessionCategories(string response) => new();
        private List<FactionRelationship> ParseFactionRelationships(string response) => new();
        private List<FamilyRelationship> ParseFamilyRelationships(string response) => new();
        private List<OrganizationRelationship> ParseOrganizationRelationships(string response) => new();
    }
}

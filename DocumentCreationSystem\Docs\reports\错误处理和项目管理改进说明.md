# 错误处理和项目管理改进说明

## 问题1：写书过程中的网络请求错误解决方案

### 问题描述
用户在使用一键写书功能时遇到"An error occurred while sending the request"错误，这通常是由于网络连接不稳定、API服务器响应超时或临时服务不可用导致的。

### 解决方案

#### 1. 智谱AI服务改进
- **重试机制**: 添加了最多3次重试，每次重试间隔递增（2秒、4秒、6秒）
- **超时控制**: 设置5分钟的请求超时时间
- **错误分类**: 区分网络错误、超时错误和API错误，提供更详细的错误信息
- **状态码处理**: 解析API返回的错误详情，提供更准确的错误描述

```csharp
// 重试逻辑示例
for (int attempt = 1; attempt <= maxRetries; attempt++)
{
    try
    {
        // 执行API请求
        using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(5));
        var response = await _httpClient.PostAsync(url, content, cts.Token);
        // 处理响应...
    }
    catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException)
    {
        // 超时重试
    }
    catch (HttpRequestException ex) when (attempt < maxRetries)
    {
        // 网络错误重试
        await Task.Delay(retryDelay * attempt);
        continue;
    }
}
```

#### 2. LM Studio服务改进
- **扩展超时**: 设置10分钟超时时间（适应本地模型推理时间）
- **服务器错误重试**: 对5xx服务器错误进行重试
- **连接检测**: 改进模型可用性检测机制
- **详细日志**: 记录每次重试的详细信息

#### 3. 通用改进
- **取消令牌**: 所有网络请求都使用CancellationToken
- **资源管理**: 使用using语句确保资源正确释放
- **错误传播**: 保持原始异常信息，便于调试
- **用户友好**: 提供清晰的错误消息和建议操作

### 使用建议
1. **网络检查**: 确保网络连接稳定
2. **API密钥**: 验证API密钥是否有效且有足够配额
3. **服务状态**: 检查AI服务提供商的服务状态
4. **重试等待**: 如果遇到错误，等待几秒后重试
5. **日志查看**: 查看应用程序日志获取详细错误信息

## 问题2：同名同路径项目文件夹重复问题解决方案

### 问题描述
项目历史记录中出现了两个同名同路径的项目文件夹，这可能导致界面显示混乱和数据不一致。

### 解决方案

#### 1. 重复检测机制
```csharp
public async Task<int> CleanupDuplicateHistoryAsync()
{
    // 按标准化路径分组，找出重复项
    var groupedByPath = _histories
        .GroupBy(h => Path.GetFullPath(h.RootPath)
            .TrimEnd(Path.DirectorySeparatorChar)
            .ToLowerInvariant())
        .Where(g => g.Count() > 1);
    
    // 保留最新访问的记录，删除其他重复项
    foreach (var group in groupedByPath)
    {
        var keepRecord = group.OrderByDescending(h => h.LastAccessTime).First();
        var duplicates = group.Skip(1).ToList();
        
        // 合并访问次数和收藏状态
        keepRecord.AccessCount = group.Sum(h => h.AccessCount);
        keepRecord.IsFavorite = group.Any(h => h.IsFavorite);
        
        // 移除重复项
        foreach (var duplicate in duplicates)
        {
            _histories.Remove(duplicate);
        }
    }
}
```

#### 2. 自动清理功能
- **启动时清理**: 应用程序启动时自动检测和清理重复项
- **加载时清理**: 每次加载历史项目列表时自动清理
- **路径标准化**: 统一路径格式，忽略大小写和路径分隔符差异

#### 3. 手动清理工具
- **检测功能**: 提供检测重复项目的功能
- **用户确认**: 清理前显示详细信息并要求用户确认
- **数据合并**: 智能合并重复项目的访问次数和收藏状态
- **界面按钮**: 在历史项目工具栏添加"清理重复记录"按钮

#### 4. 预防措施
- **路径验证**: 添加项目时验证路径唯一性
- **标准化存储**: 存储前对路径进行标准化处理
- **定期维护**: 定期自动清理无效和重复的记录

### 界面改进
1. **工具栏按钮**: 
   - 刷新历史项目
   - 清理无效记录
   - **清理重复记录** (新增)
   - 清空历史记录

2. **状态提示**: 清理操作时显示进度和结果
3. **确认对话框**: 清理前显示将要删除的项目数量
4. **日志记录**: 记录所有清理操作的详细信息

## 技术实现细节

### 错误处理策略
1. **分层错误处理**: 在服务层、业务层和UI层分别处理不同类型的错误
2. **错误分类**: 区分临时错误（可重试）和永久错误（不可重试）
3. **用户反馈**: 提供清晰的错误消息和解决建议
4. **日志记录**: 记录详细的错误信息用于调试

### 数据一致性保证
1. **原子操作**: 确保数据修改操作的原子性
2. **锁机制**: 使用锁保护共享数据的并发访问
3. **事务处理**: 对关键操作使用事务确保数据一致性
4. **备份恢复**: 提供数据备份和恢复机制

### 性能优化
1. **异步操作**: 所有I/O操作使用异步模式
2. **资源管理**: 及时释放网络连接和文件句柄
3. **缓存机制**: 缓存常用数据减少重复计算
4. **批量操作**: 合并多个小操作为批量操作

## 使用指南

### 网络错误处理
1. 检查网络连接
2. 验证API配置
3. 查看错误日志
4. 尝试重新连接
5. 联系技术支持

### 项目管理维护
1. 定期清理无效项目
2. 检查并清理重复项目
3. 备份重要项目数据
4. 监控存储空间使用
5. 更新项目配置

## 后续改进计划

1. **智能重试**: 根据错误类型调整重试策略
2. **离线模式**: 支持离线工作和数据同步
3. **数据备份**: 自动备份项目数据
4. **性能监控**: 监控系统性能和资源使用
5. **用户体验**: 改进错误提示和操作引导

using System;
using System.Text.RegularExpressions;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试 <output>...</output> 标签支持
    /// </summary>
    public class TestOutputTagSupport
    {
        // 思维链标记模式 - 支持多种格式
        private static readonly Regex ThinkingChainPattern = new(@"<thinking>(.*?)</thinking>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex ThinkChainPattern = new(@"<think>(.*?)</think>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex QwenThinkPattern = new(@"(.*?)</think>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex OutputPattern = new(@"<o>(.*?)</o>", RegexOptions.Singleline | RegexOptions.IgnoreCase);
        private static readonly Regex OutputTagPattern = new(@"<output>(.*?)</output>", RegexOptions.Singleline | RegexOptions.IgnoreCase);

        public static void Main(string[] args)
        {
            Console.WriteLine("=== <output> 标签支持测试 ===\n");

            TestOutputTagFormat();
            TestMixedFormats();
            TestPriorityOrder();

            Console.WriteLine("=== 所有测试完成 ===");
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试 <output>...</output> 格式
        /// </summary>
        private static void TestOutputTagFormat()
        {
            Console.WriteLine("1. 测试 <output> 标签格式");
            Console.WriteLine("----------------------------------------");
            
            var input = @"<think>
我需要分析这个问题。
首先考虑用户的需求...
然后制定解决方案...
</think>

<output>
根据您的需求，我已经成功添加了对 <output>...</output> 格式的支持。
这种格式将优先于 <o>...</o> 格式被识别和处理。
</output>";

            var result = ProcessThinkingChain(input);
            
            Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
            Console.WriteLine($"思维链格式: {result.Format}");
            Console.WriteLine($"处理后内容:");
            Console.WriteLine(result.FinalOutput.Trim());
            Console.WriteLine();
        }

        /// <summary>
        /// 测试混合格式
        /// </summary>
        private static void TestMixedFormats()
        {
            Console.WriteLine("2. 测试混合格式支持");
            Console.WriteLine("----------------------------------------");
            
            // 测试 <output> + <think>
            TestFormat("output + think", @"<think>
思考过程...
</think>

<output>
这是使用 output 标签的最终输出
</output>");

            // 测试 <output> + Qwen 格式
            TestFormat("output + Qwen", @"这是 Qwen 格式的思考过程
分析各种可能性...
</think>

<output>
这是使用 output 标签的最终输出
</output>");

            // 测试传统 <o> 格式
            TestFormat("传统 o 标签", @"<think>
思考过程...
</think>

<o>
这是使用 o 标签的最终输出
</o>");
        }

        /// <summary>
        /// 测试优先级顺序
        /// </summary>
        private static void TestPriorityOrder()
        {
            Console.WriteLine("3. 测试优先级顺序");
            Console.WriteLine("----------------------------------------");
            
            // 测试同时包含 <output> 和 <o> 标签时的优先级
            var input = @"<think>
这是思考过程...
</think>

<output>
这是 output 标签的内容（应该被优先选择）
</output>

<o>
这是 o 标签的内容（应该被忽略）
</o>";

            var result = ProcessThinkingChain(input);
            
            Console.WriteLine($"包含思维链: {result.HasThinkingChain}");
            Console.WriteLine($"思维链格式: {result.Format}");
            Console.WriteLine($"处理后内容:");
            Console.WriteLine(result.FinalOutput.Trim());
            Console.WriteLine();
            
            Console.WriteLine("验证: output 标签应该优先于 o 标签被选择");
            Console.WriteLine($"结果正确: {result.FinalOutput.Contains("output 标签的内容")}");
            Console.WriteLine();
        }

        private static void TestFormat(string formatName, string input)
        {
            Console.WriteLine($"测试 {formatName}:");
            
            var result = ProcessThinkingChain(input);
            
            Console.WriteLine($"  包含思维链: {result.HasThinkingChain}");
            Console.WriteLine($"  思维链格式: {result.Format}");
            Console.WriteLine($"  处理后内容: {result.FinalOutput.Trim()}");
            Console.WriteLine();
        }

        /// <summary>
        /// 处理思维链内容
        /// </summary>
        private static ThinkingChainResult ProcessThinkingChain(string rawContent)
        {
            var result = new ThinkingChainResult
            {
                RawContent = rawContent,
                HasThinkingChain = false,
                Format = "无",
                ThinkingChain = "",
                FinalOutput = rawContent
            };

            if (string.IsNullOrEmpty(rawContent))
                return result;

            // 优先检测 <output>...</output> 格式
            var outputTagMatch = OutputTagPattern.Match(rawContent);
            if (outputTagMatch.Success)
            {
                result.FinalOutput = outputTagMatch.Groups[1].Value.Trim();

                // 检测各种思维链格式
                var thinkMatch = ThinkChainPattern.Match(rawContent);
                var thinkingMatch = ThinkingChainPattern.Match(rawContent);
                var qwenMatch = QwenThinkPattern.Match(rawContent);

                if (thinkMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<think> + <output>";
                    result.ThinkingChain = thinkMatch.Groups[1].Value.Trim();
                }
                else if (thinkingMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<thinking> + <output>";
                    result.ThinkingChain = thinkingMatch.Groups[1].Value.Trim();
                }
                else if (qwenMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "Qwen (...think>) + <output>";
                    result.ThinkingChain = qwenMatch.Groups[1].Value.Trim();
                }

                return result;
            }

            // 检测 <o>...</o> 格式
            var outputMatch = OutputPattern.Match(rawContent);
            if (outputMatch.Success)
            {
                result.FinalOutput = outputMatch.Groups[1].Value.Trim();

                var thinkMatch = ThinkChainPattern.Match(rawContent);
                if (thinkMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<think> + <o>";
                    result.ThinkingChain = thinkMatch.Groups[1].Value.Trim();
                }
                else
                {
                    var thinkingMatch = ThinkingChainPattern.Match(rawContent);
                    if (thinkingMatch.Success)
                    {
                        result.HasThinkingChain = true;
                        result.Format = "<thinking> + <o>";
                        result.ThinkingChain = thinkingMatch.Groups[1].Value.Trim();
                    }
                    else
                    {
                        var qwenMatch = QwenThinkPattern.Match(rawContent);
                        if (qwenMatch.Success)
                        {
                            result.HasThinkingChain = true;
                            result.Format = "Qwen (...think>) + <o>";
                            result.ThinkingChain = qwenMatch.Groups[1].Value.Trim();
                        }
                    }
                }
            }
            else
            {
                // 兼容旧格式：检测各种思维链格式
                var thinkingMatch = ThinkingChainPattern.Match(rawContent);
                var thinkMatch = ThinkChainPattern.Match(rawContent);
                var qwenMatch = QwenThinkPattern.Match(rawContent);

                if (thinkingMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<thinking>";
                    result.ThinkingChain = thinkingMatch.Groups[1].Value.Trim();
                    result.FinalOutput = ThinkingChainPattern.Replace(rawContent, "").Trim();
                }
                else if (thinkMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "<think>";
                    result.ThinkingChain = thinkMatch.Groups[1].Value.Trim();
                    result.FinalOutput = ThinkChainPattern.Replace(rawContent, "").Trim();
                }
                else if (qwenMatch.Success)
                {
                    result.HasThinkingChain = true;
                    result.Format = "Qwen (...think>)";
                    result.ThinkingChain = qwenMatch.Groups[1].Value.Trim();
                    result.FinalOutput = QwenThinkPattern.Replace(rawContent, "").Trim();
                }
            }

            return result;
        }

        /// <summary>
        /// 思维链处理结果
        /// </summary>
        public class ThinkingChainResult
        {
            public string RawContent { get; set; } = "";
            public bool HasThinkingChain { get; set; }
            public string Format { get; set; } = "";
            public string ThinkingChain { get; set; } = "";
            public string FinalOutput { get; set; } = "";
        }
    }
}

using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// AI工具服务接口
/// </summary>
public interface IAIToolsService
{
    /// <summary>
    /// 获取所有可用的AI工具
    /// </summary>
    /// <returns>AI工具列表</returns>
    Task<List<AITool>> GetAvailableToolsAsync();

    /// <summary>
    /// 根据类别获取AI工具
    /// </summary>
    /// <param name="category">工具类别</param>
    /// <returns>指定类别的AI工具列表</returns>
    Task<List<AITool>> GetToolsByCategoryAsync(string category);

    /// <summary>
    /// 执行AI工具
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <param name="parameters">工具参数</param>
    /// <returns>执行结果</returns>
    Task<AIToolExecutionResult> ExecuteToolAsync(string toolId, Dictionary<string, object> parameters);

    /// <summary>
    /// 获取工具的参数定义
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <returns>参数定义列表</returns>
    Task<List<AIToolParameter>> GetToolParametersAsync(string toolId);

    /// <summary>
    /// 验证工具参数
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <param name="parameters">参数</param>
    /// <returns>验证结果</returns>
    Task<AIToolValidationResult> ValidateParametersAsync(string toolId, Dictionary<string, object> parameters);

    /// <summary>
    /// 获取工具使用历史
    /// </summary>
    /// <param name="toolId">工具ID</param>
    /// <param name="limit">限制数量</param>
    /// <returns>使用历史</returns>
    Task<List<AIToolUsageHistory>> GetToolUsageHistoryAsync(string toolId, int limit = 10);

    /// <summary>
    /// 搜索工具
    /// </summary>
    /// <param name="query">搜索查询</param>
    /// <returns>匹配的工具列表</returns>
    Task<List<AITool>> SearchToolsAsync(string query);

    /// <summary>
    /// 获取工具统计信息
    /// </summary>
    /// <returns>工具统计</returns>
    Task<AIToolStatistics> GetToolStatisticsAsync();
}

/// <summary>
/// AI工具定义
/// </summary>
public class AITool
{
    /// <summary>
    /// 工具ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 工具名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 工具描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 工具类别
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 工具图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 工具版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 工具使用说明
    /// </summary>
    public string Usage { get; set; } = string.Empty;

    /// <summary>
    /// 工具标签
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// 使用次数
    /// </summary>
    public int UsageCount { get; set; } = 0;

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTime? LastUsedAt { get; set; }

    /// <summary>
    /// 平均执行时间（毫秒）
    /// </summary>
    public double AverageExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate { get; set; } = 100.0;
}

/// <summary>
/// AI工具参数定义
/// </summary>
public class AIToolParameter
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; } = false;

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 参数描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 可选值列表
    /// </summary>
    public List<string> Options { get; set; } = new();

    /// <summary>
    /// 最小值（数值类型）
    /// </summary>
    public double? MinValue { get; set; }

    /// <summary>
    /// 最大值（数值类型）
    /// </summary>
    public double? MaxValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public string ValidationPattern { get; set; } = string.Empty;
}

/// <summary>
/// AI工具执行结果
/// </summary>
public class AIToolExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 结果消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 结果数据
    /// </summary>
    public Dictionary<string, object> Data { get; set; } = new();

    /// <summary>
    /// 错误详情
    /// </summary>
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 执行日志
    /// </summary>
    public List<string> Logs { get; set; } = new();

    /// <summary>
    /// 工具ID
    /// </summary>
    public string ToolId { get; set; } = string.Empty;

    /// <summary>
    /// 执行时间
    /// </summary>
    public DateTime ExecutedAt { get; set; } = DateTime.Now;
}

/// <summary>
/// AI工具参数验证结果
/// </summary>
public class AIToolValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; } = true;

    /// <summary>
    /// 验证错误
    /// </summary>
    public List<string> Errors { get; set; } = new();

    /// <summary>
    /// 验证警告
    /// </summary>
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// AI工具使用历史
/// </summary>
public class AIToolUsageHistory
{
    /// <summary>
    /// 工具ID
    /// </summary>
    public string ToolId { get; set; } = string.Empty;

    /// <summary>
    /// 使用时间
    /// </summary>
    public DateTime UsedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 使用参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 执行结果
    /// </summary>
    public bool IsSuccess { get; set; } = false;

    /// <summary>
    /// 执行时间（毫秒）
    /// </summary>
    public long ExecutionTimeMs { get; set; } = 0;

    /// <summary>
    /// 错误信息
    /// </summary>
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// AI工具统计信息
/// </summary>
public class AIToolStatistics
{
    /// <summary>
    /// 总工具数
    /// </summary>
    public int TotalTools { get; set; } = 0;

    /// <summary>
    /// 启用的工具数
    /// </summary>
    public int EnabledTools { get; set; } = 0;

    /// <summary>
    /// 总使用次数
    /// </summary>
    public int TotalUsageCount { get; set; } = 0;

    /// <summary>
    /// 平均成功率
    /// </summary>
    public double AverageSuccessRate { get; set; } = 100.0;

    /// <summary>
    /// 最常用的工具
    /// </summary>
    public List<string> MostUsedTools { get; set; } = new();

    /// <summary>
    /// 工具类别统计
    /// </summary>
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
}

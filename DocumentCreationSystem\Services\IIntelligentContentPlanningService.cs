using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services
{
    /// <summary>
    /// 智能内容规划服务接口
    /// </summary>
    public interface IIntelligentContentPlanningService
    {
        /// <summary>
        /// 生成自适应大纲
        /// </summary>
        /// <param name="request">内容请求</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>自适应大纲</returns>
        Task<AdaptiveOutline> GenerateAdaptiveOutlineAsync(
            ContentPlanningRequest request, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 预测内容质量
        /// </summary>
        /// <param name="outline">大纲结构</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>质量预测结果</returns>
        Task<QualityPrediction> PredictContentQualityAsync(
            OutlineStructure outline, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 优化生成计划
        /// </summary>
        /// <param name="plan">生成计划</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>优化后的计划</returns>
        Task<OptimizedGenerationPlan> OptimizeGenerationPlanAsync(
            GenerationPlan plan, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 验证全局一致性
        /// </summary>
        /// <param name="content">文档内容</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>一致性报告</returns>
        Task<ConsistencyReport> ValidateGlobalConsistencyAsync(
            DocumentContent content, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 智能字数分配
        /// </summary>
        /// <param name="outline">大纲</param>
        /// <param name="totalWordCount">总字数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>字数分配方案</returns>
        Task<WordAllocationPlan> AllocateWordsIntelligentlyAsync(
            AdaptiveOutline outline, 
            int totalWordCount, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// 生成内容改进建议
        /// </summary>
        /// <param name="content">内容</param>
        /// <param name="qualityMetrics">质量指标</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>改进建议</returns>
        Task<List<ImprovementSuggestion>> GenerateImprovementSuggestionsAsync(
            string content, 
            QualityMetrics qualityMetrics, 
            CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 内容规划请求
    /// </summary>
    public class ContentPlanningRequest
    {
        /// <summary>
        /// 内容类型
        /// </summary>
        public string ContentType { get; set; } = string.Empty;

        /// <summary>
        /// 主题描述
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// 目标受众
        /// </summary>
        public string TargetAudience { get; set; } = string.Empty;

        /// <summary>
        /// 写作风格
        /// </summary>
        public string WritingStyle { get; set; } = string.Empty;

        /// <summary>
        /// 目标字数
        /// </summary>
        public int TargetWordCount { get; set; }

        /// <summary>
        /// 素材文件列表
        /// </summary>
        public List<string> MaterialFiles { get; set; } = new();

        /// <summary>
        /// 用户特殊要求
        /// </summary>
        public string? UserRequirements { get; set; }

        /// <summary>
        /// 质量要求等级 (1-5)
        /// </summary>
        public int QualityLevel { get; set; } = 3;

        /// <summary>
        /// 创新程度要求 (1-5)
        /// </summary>
        public int InnovationLevel { get; set; } = 3;
    }

    /// <summary>
    /// 自适应大纲
    /// </summary>
    public class AdaptiveOutline
    {
        /// <summary>
        /// 大纲ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节列表
        /// </summary>
        public List<AdaptiveSection> Sections { get; set; } = new();

        /// <summary>
        /// 生成策略
        /// </summary>
        public GenerationStrategy Strategy { get; set; } = new();

        /// <summary>
        /// 预估质量分数
        /// </summary>
        public double EstimatedQualityScore { get; set; }

        /// <summary>
        /// 复杂度评估
        /// </summary>
        public ComplexityAssessment Complexity { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 自适应章节
    /// </summary>
    public class AdaptiveSection
    {
        /// <summary>
        /// 章节ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 章节标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 关键要点
        /// </summary>
        public List<string> KeyPoints { get; set; } = new();

        /// <summary>
        /// 分配字数
        /// </summary>
        public int AllocatedWordCount { get; set; }

        /// <summary>
        /// 重要性权重 (0-1)
        /// </summary>
        public double ImportanceWeight { get; set; }

        /// <summary>
        /// 复杂度等级 (1-5)
        /// </summary>
        public int ComplexityLevel { get; set; }

        /// <summary>
        /// 依赖的前置章节
        /// </summary>
        public List<string> Dependencies { get; set; } = new();

        /// <summary>
        /// 生成优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 子章节
        /// </summary>
        public List<AdaptiveSection> SubSections { get; set; } = new();
    }

    /// <summary>
    /// 质量预测结果
    /// </summary>
    public class QualityPrediction
    {
        /// <summary>
        /// 预测的总体质量分数 (0-100)
        /// </summary>
        public double OverallQualityScore { get; set; }

        /// <summary>
        /// 各维度质量分数
        /// </summary>
        public Dictionary<string, double> DimensionScores { get; set; } = new();

        /// <summary>
        /// 潜在问题列表
        /// </summary>
        public List<PotentialIssue> PotentialIssues { get; set; } = new();

        /// <summary>
        /// 改进建议
        /// </summary>
        public List<string> ImprovementSuggestions { get; set; } = new();

        /// <summary>
        /// 预测置信度 (0-1)
        /// </summary>
        public double Confidence { get; set; }

        /// <summary>
        /// 预测时间
        /// </summary>
        public DateTime PredictedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 潜在问题
    /// </summary>
    public class PotentialIssue
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 严重程度 (1-5)
        /// </summary>
        public int Severity { get; set; }

        /// <summary>
        /// 影响的章节
        /// </summary>
        public List<string> AffectedSections { get; set; } = new();

        /// <summary>
        /// 解决建议
        /// </summary>
        public string Solution { get; set; } = string.Empty;
    }

    /// <summary>
    /// 生成策略
    /// </summary>
    public class GenerationStrategy
    {
        /// <summary>
        /// 策略名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 生成模式
        /// </summary>
        public GenerationMode Mode { get; set; }

        /// <summary>
        /// 迭代次数
        /// </summary>
        public int IterationCount { get; set; } = 1;

        /// <summary>
        /// 质量检查点
        /// </summary>
        public List<QualityCheckpoint> QualityCheckpoints { get; set; } = new();

        /// <summary>
        /// 并行处理设置
        /// </summary>
        public ParallelProcessingSettings ParallelSettings { get; set; } = new();
    }

    /// <summary>
    /// 生成模式
    /// </summary>
    public enum GenerationMode
    {
        /// <summary>
        /// 顺序生成
        /// </summary>
        Sequential,

        /// <summary>
        /// 并行生成
        /// </summary>
        Parallel,

        /// <summary>
        /// 混合模式
        /// </summary>
        Hybrid,

        /// <summary>
        /// 自适应模式
        /// </summary>
        Adaptive
    }

    /// <summary>
    /// 复杂度评估
    /// </summary>
    public class ComplexityAssessment
    {
        /// <summary>
        /// 总体复杂度 (1-5)
        /// </summary>
        public int OverallComplexity { get; set; }

        /// <summary>
        /// 内容复杂度
        /// </summary>
        public int ContentComplexity { get; set; }

        /// <summary>
        /// 结构复杂度
        /// </summary>
        public int StructuralComplexity { get; set; }

        /// <summary>
        /// 逻辑复杂度
        /// </summary>
        public int LogicalComplexity { get; set; }

        /// <summary>
        /// 预估生成时间（分钟）
        /// </summary>
        public int EstimatedGenerationTime { get; set; }

        /// <summary>
        /// 建议的生成策略
        /// </summary>
        public string RecommendedStrategy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 优化后的生成计划
    /// </summary>
    public class OptimizedGenerationPlan
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<GenerationStep> Steps { get; set; } = new();

        /// <summary>
        /// 资源分配
        /// </summary>
        public ResourceAllocation ResourceAllocation { get; set; } = new();

        /// <summary>
        /// 预估执行时间
        /// </summary>
        public TimeSpan EstimatedDuration { get; set; }

        /// <summary>
        /// 优化指标
        /// </summary>
        public OptimizationMetrics Metrics { get; set; } = new();
    }

    /// <summary>
    /// 生成步骤
    /// </summary>
    public class GenerationStep
    {
        /// <summary>
        /// 步骤ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 步骤名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 步骤类型
        /// </summary>
        public StepType Type { get; set; }

        /// <summary>
        /// 执行顺序
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// 依赖步骤
        /// </summary>
        public List<string> Dependencies { get; set; } = new();

        /// <summary>
        /// 预估耗时
        /// </summary>
        public TimeSpan EstimatedTime { get; set; }

        /// <summary>
        /// 参数配置
        /// </summary>
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 步骤类型
    /// </summary>
    public enum StepType
    {
        Analysis,
        Planning,
        Generation,
        Review,
        Optimization,
        Formatting
    }

    /// <summary>
    /// 资源分配
    /// </summary>
    public class ResourceAllocation
    {
        /// <summary>
        /// CPU使用率
        /// </summary>
        public double CpuUsage { get; set; }

        /// <summary>
        /// 内存使用量（MB）
        /// </summary>
        public long MemoryUsage { get; set; }

        /// <summary>
        /// 并发任务数
        /// </summary>
        public int ConcurrentTasks { get; set; }

        /// <summary>
        /// AI模型调用次数
        /// </summary>
        public int ModelCalls { get; set; }
    }

    /// <summary>
    /// 优化指标
    /// </summary>
    public class OptimizationMetrics
    {
        /// <summary>
        /// 效率提升百分比
        /// </summary>
        public double EfficiencyImprovement { get; set; }

        /// <summary>
        /// 质量提升百分比
        /// </summary>
        public double QualityImprovement { get; set; }

        /// <summary>
        /// 时间节省百分比
        /// </summary>
        public double TimeSaving { get; set; }

        /// <summary>
        /// 资源利用率
        /// </summary>
        public double ResourceUtilization { get; set; }
    }

    /// <summary>
    /// 一致性报告
    /// </summary>
    public class ConsistencyReport
    {
        /// <summary>
        /// 总体一致性分数 (0-100)
        /// </summary>
        public double OverallConsistencyScore { get; set; }

        /// <summary>
        /// 术语一致性
        /// </summary>
        public ConsistencyCheck TerminologyConsistency { get; set; } = new();

        /// <summary>
        /// 风格一致性
        /// </summary>
        public ConsistencyCheck StyleConsistency { get; set; } = new();

        /// <summary>
        /// 逻辑一致性
        /// </summary>
        public ConsistencyCheck LogicalConsistency { get; set; } = new();

        /// <summary>
        /// 格式一致性
        /// </summary>
        public ConsistencyCheck FormatConsistency { get; set; } = new();

        /// <summary>
        /// 不一致问题列表
        /// </summary>
        public List<InconsistencyIssue> Issues { get; set; } = new();

        /// <summary>
        /// 修复建议
        /// </summary>
        public List<string> FixSuggestions { get; set; } = new();
    }

    /// <summary>
    /// 一致性检查
    /// </summary>
    public class ConsistencyCheck
    {
        /// <summary>
        /// 检查分数 (0-100)
        /// </summary>
        public double Score { get; set; }

        /// <summary>
        /// 检查状态
        /// </summary>
        public CheckStatus Status { get; set; }

        /// <summary>
        /// 详细信息
        /// </summary>
        public string Details { get; set; } = string.Empty;

        /// <summary>
        /// 发现的问题数量
        /// </summary>
        public int IssueCount { get; set; }
    }

    /// <summary>
    /// 检查状态
    /// </summary>
    public enum CheckStatus
    {
        Passed,
        Warning,
        Failed,
        NotChecked
    }

    /// <summary>
    /// 不一致问题
    /// </summary>
    public class InconsistencyIssue
    {
        /// <summary>
        /// 问题类型
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// 问题描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 位置信息
        /// </summary>
        public List<string> Locations { get; set; } = new();

        /// <summary>
        /// 严重程度
        /// </summary>
        public IssueSeverity Severity { get; set; }

        /// <summary>
        /// 修复建议
        /// </summary>
        public string FixSuggestion { get; set; } = string.Empty;
    }

    /// <summary>
    /// 问题严重程度
    /// </summary>
    public enum IssueSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 字数分配计划
    /// </summary>
    public class WordAllocationPlan
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount { get; set; }

        /// <summary>
        /// 各章节字数分配
        /// </summary>
        public List<SectionWordAllocation> Allocations { get; set; } = new();

        /// <summary>
        /// 分配策略
        /// </summary>
        public string AllocationStrategy { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 章节字数分配
    /// </summary>
    public class SectionWordAllocation
    {
        /// <summary>
        /// 章节ID
        /// </summary>
        public string SectionId { get; set; } = string.Empty;

        /// <summary>
        /// 章节标题
        /// </summary>
        public string SectionTitle { get; set; } = string.Empty;

        /// <summary>
        /// 分配字数
        /// </summary>
        public int AllocatedWords { get; set; }

        /// <summary>
        /// 权重
        /// </summary>
        public double Weight { get; set; }

        /// <summary>
        /// 复杂度等级
        /// </summary>
        public int ComplexityLevel { get; set; }

        /// <summary>
        /// 最小字数
        /// </summary>
        public int MinWords { get; set; }

        /// <summary>
        /// 最大字数
        /// </summary>
        public int MaxWords { get; set; }
    }

    /// <summary>
    /// 大纲结构
    /// </summary>
    public class OutlineStructure
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节列表
        /// </summary>
        public List<string> Sections { get; set; } = new();

        /// <summary>
        /// 层级结构
        /// </summary>
        public Dictionary<string, List<string>> Hierarchy { get; set; } = new();

        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount { get; set; }
    }

    /// <summary>
    /// 生成计划
    /// </summary>
    public class GenerationPlan
    {
        /// <summary>
        /// 计划ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 计划名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 执行步骤
        /// </summary>
        public List<GenerationStep> Steps { get; set; } = new();

        /// <summary>
        /// 预估总时间
        /// </summary>
        public TimeSpan EstimatedTotalTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 文档内容
    /// </summary>
    public class DocumentContent
    {
        /// <summary>
        /// 文档ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 章节内容
        /// </summary>
        public List<SectionContent> Sections { get; set; } = new();

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 总字数
        /// </summary>
        public int TotalWordCount => Sections.Sum(s => s.WordCount);
    }

    /// <summary>
    /// 章节内容
    /// </summary>
    public class SectionContent
    {
        /// <summary>
        /// 章节ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 字数
        /// </summary>
        public int WordCount => Content.Split(new char[] { ' ', '\n', '\r', '\t' },
            StringSplitOptions.RemoveEmptyEntries).Length;

        /// <summary>
        /// 层级
        /// </summary>
        public int Level { get; set; }

        /// <summary>
        /// 子章节
        /// </summary>
        public List<SectionContent> SubSections { get; set; } = new();
    }

    /// <summary>
    /// 质量检查点
    /// </summary>
    public class QualityCheckpoint
    {
        /// <summary>
        /// 检查点名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 检查类型
        /// </summary>
        public CheckpointType Type { get; set; }

        /// <summary>
        /// 触发条件
        /// </summary>
        public string TriggerCondition { get; set; } = string.Empty;

        /// <summary>
        /// 检查标准
        /// </summary>
        public List<string> Criteria { get; set; } = new();
    }

    /// <summary>
    /// 检查点类型
    /// </summary>
    public enum CheckpointType
    {
        Structure,
        Content,
        Quality,
        Consistency,
        Completeness
    }

    /// <summary>
    /// 并行处理设置
    /// </summary>
    public class ParallelProcessingSettings
    {
        /// <summary>
        /// 最大并行任务数
        /// </summary>
        public int MaxConcurrentTasks { get; set; } = 3;

        /// <summary>
        /// 是否启用并行处理
        /// </summary>
        public bool EnableParallelProcessing { get; set; } = true;

        /// <summary>
        /// 任务优先级策略
        /// </summary>
        public string PriorityStrategy { get; set; } = "Balanced";

        /// <summary>
        /// 资源限制
        /// </summary>
        public Dictionary<string, object> ResourceLimits { get; set; } = new();
    }
}

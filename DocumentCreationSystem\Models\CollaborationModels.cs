using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    // ==================== 协作相关模型 ====================

    /// <summary>
    /// 协作会话
    /// </summary>
    public class CollaborationSession
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ProjectId { get; set; } = string.Empty;
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? EndedAt { get; set; }
        public CollaborationMode Mode { get; set; }
        public SessionStatus Status { get; set; }
        public List<SessionParticipant> Participants { get; set; } = new();
        public List<CollaborationChange> Changes { get; set; } = new();
        public Dictionary<string, object> Settings { get; set; } = new();
    }

    /// <summary>
    /// 协作模式
    /// </summary>
    public enum CollaborationMode
    {
        RealTime,       // 实时协作
        TurnBased,      // 轮流编辑
        ReviewBased,    // 审查模式
        Asynchronous    // 异步协作
    }

    /// <summary>
    /// 会话状态
    /// </summary>
    public enum SessionStatus
    {
        Active,
        Paused,
        Ended,
        Archived
    }

    /// <summary>
    /// 会话参与者
    /// </summary>
    public class SessionParticipant
    {
        public string UserId { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public DateTime JoinedAt { get; set; }
        public DateTime? LeftAt { get; set; }
        public bool IsActive { get; set; }
        public DateTime LastActiveAt { get; set; }
        public Dictionary<string, object> Preferences { get; set; } = new();
    }

    /// <summary>
    /// 用户角色
    /// </summary>
    public enum UserRole
    {
        Owner,          // 所有者
        Editor,         // 编辑者
        Reviewer,       // 审查者
        Viewer,         // 查看者
        Contributor     // 贡献者
    }

    /// <summary>
    /// 协作变更
    /// </summary>
    public class CollaborationChange
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string SessionId { get; set; } = string.Empty;
        public string ResourceId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public ChangeType Type { get; set; }
        public DateTime Timestamp { get; set; }
        public string Description { get; set; } = string.Empty;
        public ChangePosition? Position { get; set; }
        public string? OldContent { get; set; }
        public string? NewContent { get; set; }
        public ChangeStatus Status { get; set; }
        public DateTime? AppliedAt { get; set; }
        public List<ChangeConflict> Conflicts { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 变更类型
    /// </summary>
    public enum ChangeType
    {
        TextInsert,     // 文本插入
        TextDelete,     // 文本删除
        TextReplace,    // 文本替换
        FormatChange,   // 格式变更
        StructureChange,// 结构变更
        MetadataChange, // 元数据变更
        FileAdd,        // 文件添加
        FileDelete,     // 文件删除
        FileRename      // 文件重命名
    }

    /// <summary>
    /// 变更位置
    /// </summary>
    public class ChangePosition
    {
        public int Start { get; set; }
        public int End { get; set; }
        public int Line { get; set; }
        public int Column { get; set; }
        public string? Section { get; set; }
    }

    /// <summary>
    /// 变更状态
    /// </summary>
    public enum ChangeStatus
    {
        Pending,        // 待处理
        Applied,        // 已应用
        Rejected,       // 已拒绝
        Conflicted,     // 有冲突
        Merged          // 已合并
    }

    /// <summary>
    /// 变更冲突
    /// </summary>
    public class ChangeConflict
    {
        public string ConflictingChangeId { get; set; } = string.Empty;
        public ConflictType ConflictType { get; set; }
        public string Description { get; set; } = string.Empty;
        public ChangePosition? ConflictPosition { get; set; }
        public string? SuggestedResolution { get; set; }
    }

    /// <summary>
    /// 冲突类型
    /// </summary>
    public enum ConflictType
    {
        ConcurrentModification,  // 并发修改
        OverlappingChanges,      // 重叠变更
        DependencyConflict,      // 依赖冲突
        PermissionConflict,      // 权限冲突
        VersionConflict          // 版本冲突
    }

    /// <summary>
    /// 冲突解决请求
    /// </summary>
    public class ConflictResolutionRequest
    {
        public string ChangeId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public ConflictResolutionStrategy ResolutionStrategy { get; set; }
        public CollaborationChange? IncomingChange { get; set; }
        public object? ManualResolution { get; set; }
        public string? Comments { get; set; }
    }

    /// <summary>
    /// 冲突解决策略
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        AcceptCurrent,   // 接受当前变更
        AcceptIncoming,  // 接受传入变更
        Merge,           // 自动合并
        Manual           // 手动解决
    }

    /// <summary>
    /// 冲突解决结果
    /// </summary>
    public class ConflictResolutionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public CollaborationChange? MergedChange { get; set; }
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// 活跃用户
    /// </summary>
    public class ActiveUser
    {
        public string UserId { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public DateTime JoinedAt { get; set; }
        public DateTime LastActiveAt { get; set; }
        public string? CurrentResource { get; set; }
        public UserStatus Status { get; set; }
        public Dictionary<string, object> ActivityData { get; set; } = new();
    }

    /// <summary>
    /// 用户状态
    /// </summary>
    public enum UserStatus
    {
        Online,
        Away,
        Busy,
        Offline
    }

    /// <summary>
    /// 资源锁
    /// </summary>
    public class ResourceLock
    {
        public string SessionId { get; set; } = string.Empty;
        public string ResourceId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public DateTime LockedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public LockType LockType { get; set; }
        public string? Reason { get; set; }
    }

    /// <summary>
    /// 锁类型
    /// </summary>
    public enum LockType
    {
        Exclusive,      // 独占锁
        Shared,         // 共享锁
        ReadOnly        // 只读锁
    }

    /// <summary>
    /// 同步结果
    /// </summary>
    public class SyncResult
    {
        public string SessionId { get; set; } = string.Empty;
        public DateTime SyncedAt { get; set; }
        public int TotalChanges { get; set; }
        public int SuccessfulChanges { get; set; }
        public int FailedChanges { get; set; }
        public List<string> Errors { get; set; } = new();
        public TimeSpan SyncDuration { get; set; }
    }

    /// <summary>
    /// 协作事件
    /// </summary>
    public class CollaborationEvent
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public EventType Type { get; set; }
        public string UserId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public object? Data { get; set; }
        public string? Description { get; set; }
    }

    /// <summary>
    /// 事件类型
    /// </summary>
    public enum EventType
    {
        UserJoined,
        UserLeft,
        ChangeApplied,
        ConflictDetected,
        ConflictResolved,
        ResourceLocked,
        ResourceUnlocked,
        SessionStarted,
        SessionEnded,
        SyncCompleted
    }

    // ==================== 版本控制相关模型 ====================

    /// <summary>
    /// 版本快照
    /// </summary>
    public class VersionSnapshot
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string ResourceId { get; set; } = string.Empty;
        public int VersionNumber { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string Description { get; set; } = string.Empty;
        public string ContentHash { get; set; } = string.Empty;
        public long ContentSize { get; set; }
        public string BranchName { get; set; } = "main";
        public string? ParentVersionId { get; set; }
        public string? ChangesSummary { get; set; }
        public List<string> Tags { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 版本差异
    /// </summary>
    public class VersionDiff
    {
        public string ResourceId { get; set; } = string.Empty;
        public string FromVersionId { get; set; } = string.Empty;
        public string ToVersionId { get; set; } = string.Empty;
        public DateTime ComparedAt { get; set; }
        public List<DiffChange> Changes { get; set; } = new();
        public int AddedLines { get; set; }
        public int DeletedLines { get; set; }
        public int ModifiedLines { get; set; }
        public string Summary { get; set; } = string.Empty;
    }

    /// <summary>
    /// 差异变更
    /// </summary>
    public class DiffChange
    {
        public DiffChangeType Type { get; set; }
        public int LineNumber { get; set; }
        public string? OldContent { get; set; }
        public string Content { get; set; } = string.Empty;
        public string? Context { get; set; }
    }

    /// <summary>
    /// 差异变更类型
    /// </summary>
    public enum DiffChangeType
    {
        Added,
        Deleted,
        Modified,
        Unchanged
    }

    /// <summary>
    /// 版本分支
    /// </summary>
    public class VersionBranch
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public string? BaseVersionId { get; set; }
        public bool IsActive { get; set; }
        public bool IsProtected { get; set; }
        public string? MergedIntoId { get; set; }
        public DateTime? MergedAt { get; set; }
    }

    /// <summary>
    /// 合并结果
    /// </summary>
    public class MergeResult
    {
        public string ResourceId { get; set; } = string.Empty;
        public string SourceBranch { get; set; } = string.Empty;
        public string TargetBranch { get; set; } = string.Empty;
        public string MergedBy { get; set; } = string.Empty;
        public DateTime MergedAt { get; set; }
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? MergeCommitId { get; set; }
        public List<MergeConflict> Conflicts { get; set; } = new();
    }

    /// <summary>
    /// 合并冲突
    /// </summary>
    public class MergeConflict
    {
        public string FilePath { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public string SourceContent { get; set; } = string.Empty;
        public string TargetContent { get; set; } = string.Empty;
        public string? ResolvedContent { get; set; }
        public bool IsResolved { get; set; }
    }

    /// <summary>
    /// 版本统计
    /// </summary>
    public class VersionStatistics
    {
        public string ResourceId { get; set; } = string.Empty;
        public int TotalVersions { get; set; }
        public int TotalBranches { get; set; }
        public DateTime? FirstVersionDate { get; set; }
        public DateTime? LastVersionDate { get; set; }
        public long TotalSize { get; set; }
        public List<string> Contributors { get; set; } = new();
        public Dictionary<string, int> VersionsByMonth { get; set; } = new();
        public double AverageVersionSize { get; set; }
    }

    /// <summary>
    /// 资源版本元数据
    /// </summary>
    public class ResourceVersionMetadata
    {
        public string ResourceId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<VersionSnapshot> Versions { get; set; } = new();
        public List<VersionBranch> Branches { get; set; } = new();
        public Dictionary<string, object> Properties { get; set; } = new();
    }
}

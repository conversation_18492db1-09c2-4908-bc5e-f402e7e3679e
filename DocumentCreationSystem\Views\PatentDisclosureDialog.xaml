<Window x:Class="DocumentCreationSystem.Views.PatentDisclosureDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="一键写专利交底书" Height="750" Width="950"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Margin" Value="0,16,0,8"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
        </Style>
        
        <Style x:Key="ParameterLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,8,0,4"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16,12" CornerRadius="4" Margin="0,0,0,16">
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Kind="LightbulbOn" VerticalAlignment="Center" Margin="0,0,12,0" Width="24" Height="24"/>
                <TextBlock Text="AI一键写专利交底书" VerticalAlignment="Center" FontSize="18" FontWeight="Medium"/>
                <TextBlock Text="基于技术素材自动生成专利交底书，包含数学公式和学术解释" VerticalAlignment="Center" FontSize="12" 
                          Margin="16,0,0,0" Opacity="0.8"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容区域 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="16"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：素材和基本信息 -->
                <StackPanel Grid.Column="0">
                    <!-- 技术素材文件选择 -->
                    <TextBlock Text="📄 技术素材文件" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="选择技术素材文件（支持.txt, .docx, .md格式）" 
                                      Style="{StaticResource ParameterLabelStyle}"/>
                            
                            <Grid Margin="0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox x:Name="MaterialFilesTextBox" Grid.Column="0"
                                        materialDesign:HintAssist.Hint="点击右侧按钮选择技术文档..."
                                        IsReadOnly="True" Height="80" TextWrapping="Wrap"
                                        VerticalScrollBarVisibility="Auto"/>
                                
                                <Button Grid.Column="1" Style="{StaticResource MaterialDesignIconButton}"
                                       Click="SelectMaterialFiles_Click" Margin="8,0,0,0"
                                       ToolTip="选择技术素材文件">
                                    <materialDesign:PackIcon Kind="FolderOpen"/>
                                </Button>
                            </Grid>
                            
                            <TextBlock x:Name="MaterialFilesCountText" Text="已选择 0 个文件" 
                                      FontSize="11" Foreground="Gray" Margin="0,4,0,0"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 专利基本信息 -->
                    <TextBlock Text="📋 专利基本信息" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="发明名称" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="InventionTitleTextBox" 
                                    materialDesign:HintAssist.Hint="请输入发明名称（可留空由AI生成）"
                                    Margin="0,0,0,12"/>
                            
                            <TextBlock Text="专利类型" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="PatentTypeComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="发明专利" IsSelected="True"/>
                                <ComboBoxItem Content="实用新型专利"/>
                                <ComboBoxItem Content="外观设计专利"/>
                                <ComboBoxItem Content="软件著作权"/>
                            </ComboBox>
                            
                            <TextBlock Text="技术领域" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="TechnicalFieldComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="计算机技术" IsSelected="True"/>
                                <ComboBoxItem Content="人工智能"/>
                                <ComboBoxItem Content="机械工程"/>
                                <ComboBoxItem Content="电子技术"/>
                                <ComboBoxItem Content="通信技术"/>
                                <ComboBoxItem Content="生物技术"/>
                                <ComboBoxItem Content="化学工程"/>
                                <ComboBoxItem Content="材料科学"/>
                                <ComboBoxItem Content="其他"/>
                            </ComboBox>
                            
                            <TextBlock Text="申请人" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="ApplicantTextBox" 
                                    materialDesign:HintAssist.Hint="申请人姓名或公司名称"
                                    Margin="0,0,0,12"/>
                            
                            <TextBlock Text="发明人" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="InventorTextBox"
                                    materialDesign:HintAssist.Hint="发明人姓名（多人用分号分隔）"
                                    Margin="0,0,0,12"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 用户要求 -->
                    <TextBlock Text="📝 用户要求" Style="{StaticResource SectionHeaderStyle}"/>

                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="附加创作要求（可选）" Style="{StaticResource ParameterLabelStyle}"/>
                            <TextBox x:Name="UserRequirementsTextBox"
                                    materialDesign:HintAssist.Hint="请输入您的特殊要求，如：重点描述某个技术特征、强调特定优势、包含特定实施例、避免某些表述等..."
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    MinHeight="80"
                                    MaxHeight="120"
                                    VerticalScrollBarVisibility="Auto"/>
                            <TextBlock Text="示例：请重点描述算法的创新性，包含至少3个具体实施例，并强调相比现有技术的显著优势"
                                      FontSize="11" Opacity="0.7" Margin="0,8,0,0" FontStyle="Italic"/>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- 右侧：技术参数和高级设置 -->
                <StackPanel Grid.Column="2">
                    <!-- 技术描述参数 -->
                    <TextBlock Text="⚙️ 技术描述参数" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <TextBlock Text="数学公式复杂度" Style="{StaticResource ParameterLabelStyle}"/>
                            <Slider x:Name="FormulaComplexitySlider" Minimum="1" Maximum="5" Value="3" 
                                   TickFrequency="1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                            <TextBlock x:Name="FormulaComplexityText" Text="中等复杂度" FontSize="11" 
                                      Foreground="Gray" HorizontalAlignment="Center"/>
                            
                            <TextBlock Text="技术深度" Style="{StaticResource ParameterLabelStyle}"/>
                            <Slider x:Name="TechnicalDepthSlider" Minimum="0.1" Maximum="1.0" Value="0.7" 
                                   TickFrequency="0.1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                            <TextBlock x:Name="TechnicalDepthText" Text="0.7" FontSize="11" 
                                      Foreground="Gray" HorizontalAlignment="Center"/>
                            
                            <TextBlock Text="创新性强调" Style="{StaticResource ParameterLabelStyle}"/>
                            <Slider x:Name="InnovationEmphasisSlider" Minimum="0.1" Maximum="1.0" Value="0.8" 
                                   TickFrequency="0.1" IsSnapToTickEnabled="True" Margin="0,0,0,8"/>
                            <TextBlock x:Name="InnovationEmphasisText" Text="0.8" FontSize="11" 
                                      Foreground="Gray" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 专利结构设置 -->
                    <TextBlock Text="📖 专利结构设置" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <CheckBox x:Name="IncludeTechnicalFieldCheckBox" Content="包含技术领域" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeBackgroundCheckBox" Content="包含背景技术" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeSummaryCheckBox" Content="包含发明内容" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeDrawingsCheckBox" Content="包含附图说明" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeDetailedDescriptionCheckBox" Content="包含具体实施方式" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeClaimsCheckBox" Content="包含权利要求书" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeAbstractCheckBox" Content="包含摘要" IsChecked="True" Margin="0,4"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 数学公式设置 -->
                    <TextBlock Text="🔢 数学公式设置" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16" Margin="0,0,0,16">
                        <StackPanel>
                            <CheckBox x:Name="IncludeAlgorithmFormulasCheckBox" Content="包含算法公式" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludePerformanceFormulasCheckBox" Content="包含性能计算公式" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeOptimizationFormulasCheckBox" Content="包含优化公式" IsChecked="True" Margin="0,4"/>
                            <CheckBox x:Name="IncludeStatisticalFormulasCheckBox" Content="包含统计分析公式" IsChecked="False" Margin="0,4"/>
                            <CheckBox x:Name="IncludeModelFormulasCheckBox" Content="包含数学模型公式" IsChecked="True" Margin="0,4"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- 输出设置 -->
                    <TextBlock Text="💾 输出设置" Style="{StaticResource SectionHeaderStyle}"/>
                    
                    <materialDesign:Card Padding="16">
                        <StackPanel>
                            <TextBlock Text="输出格式" Style="{StaticResource ParameterLabelStyle}"/>
                            <ComboBox x:Name="OutputFormatComboBox" Margin="0,0,0,12">
                                <ComboBoxItem Content="Word文档 (.docx)" IsSelected="True"/>
                                <ComboBoxItem Content="文本文件 (.txt)"/>
                                <ComboBoxItem Content="Markdown (.md)"/>
                            </ComboBox>
                            
                            <CheckBox x:Name="SaveToProjectCheckBox" Content="保存到当前项目文件夹" 
                                     IsChecked="True" Margin="0,8"/>
                            
                            <CheckBox x:Name="GenerateDrawingPlaceholdersCheckBox" Content="生成附图占位符" 
                                     IsChecked="True" Margin="0,4"/>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- 底部按钮栏 -->
        <Grid Grid.Row="2" Margin="0,16,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 进度信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                <materialDesign:PackIcon x:Name="StatusIcon" Kind="Information" 
                                        VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock x:Name="StatusText" Text="准备就绪" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- 操作按钮 -->
            <Button Grid.Column="1" x:Name="StartButton" Content="开始生成专利交底书" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="StartGeneration_Click" Margin="0,0,12,0" Padding="24,8"/>
            
            <Button Grid.Column="2" x:Name="CancelButton" Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click" Padding="24,8"/>
        </Grid>
    </Grid>
</Window>

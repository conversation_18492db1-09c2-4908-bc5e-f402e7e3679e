# 文档管理及AI创作系统 - 安装和运行指南

## 系统要求

### 最低要求
- **操作系统**: Windows 10 (1903) 或更高版本 / Linux / macOS
- **内存**: 8GB RAM
- **存储空间**: 2GB 可用空间
- **网络**: 互联网连接（用于AI服务）

### 推荐配置
- **操作系统**: Windows 11 / Ubuntu 20.04+ / macOS 12+
- **内存**: 16GB RAM 或更多
- **存储空间**: 10GB 可用空间（SSD推荐）
- **GPU**: 支持CUDA的NVIDIA显卡（可选，用于AI加速）

## 安装步骤

### 1. 安装.NET 8.0 SDK

#### Windows
1. 访问 [.NET下载页面](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 下载 ".NET 8.0 SDK" (不是Runtime)
3. 运行安装程序并按照提示完成安装
4. 重启命令提示符或PowerShell

#### Linux (Ubuntu/Debian)
```bash
# 添加Microsoft包源
wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
rm packages-microsoft-prod.deb

# 安装.NET SDK
sudo apt-get update
sudo apt-get install -y dotnet-sdk-8.0
```

#### macOS
```bash
# 使用Homebrew安装
brew install --cask dotnet-sdk

# 或者从官网下载安装包
# https://dotnet.microsoft.com/download/dotnet/8.0
```

### 2. 验证安装

打开命令提示符/终端，运行：
```bash
dotnet --version
```

应该显示类似 `8.0.xxx` 的版本号。

### 3. 获取项目代码

如果您还没有项目代码，请确保您有完整的DocumentCreationSystem文件夹。

## 运行程序

### 方法1: 使用自动化脚本（推荐）

#### Windows
```cmd
# 在DocumentCreationSystem目录中运行
.\run_tests.bat
```

#### PowerShell
```powershell
# 在DocumentCreationSystem目录中运行
.\run_tests.ps1
```

### 方法2: 手动运行

1. **打开命令提示符/终端**
2. **导航到项目目录**:
   ```bash
   cd DocumentCreationSystem
   ```

3. **还原NuGet包**:
   ```bash
   dotnet restore
   ```

4. **编译项目**:
   ```bash
   dotnet build
   ```

5. **运行程序**:
   ```bash
   dotnet run
   ```

### 方法3: 使用Visual Studio

1. 安装 [Visual Studio 2022](https://visualstudio.microsoft.com/downloads/) 或 [Visual Studio Code](https://code.visualstudio.com/)
2. 打开 `DocumentCreationSystem.csproj` 文件
3. 按 F5 运行程序

## 配置说明

### appsettings.json
项目根目录下的 `appsettings.json` 文件包含系统配置：

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AIService": {
    "DefaultProvider": "Ollama",
    "Timeout": 300
  },
  "DataStorage": {
    "BasePath": "./Data"
  }
}
```

### AI模型配置
首次运行时，系统会创建默认的AI模型配置。您可以通过GUI界面进行配置：
- Ollama (本地模型)
- OpenAI (需要API密钥)
- 智谱AI (需要API密钥)
- DeepSeek (需要API密钥)

## 常见问题解决

### 1. "dotnet命令未找到"
**原因**: .NET SDK未安装或未添加到PATH
**解决方案**: 
- 重新安装.NET 8.0 SDK
- 重启命令提示符
- 检查环境变量PATH

### 2. "项目编译失败"
**原因**: 缺少依赖包或版本不兼容
**解决方案**:
```bash
# 清理并重新构建
dotnet clean
dotnet restore
dotnet build
```

### 3. "无法连接AI服务"
**原因**: AI服务配置错误或网络问题
**解决方案**:
- 检查网络连接
- 验证API密钥
- 确认服务端点可访问

### 4. "内存不足"
**原因**: 系统内存不够
**解决方案**:
- 关闭其他应用程序
- 增加虚拟内存
- 升级物理内存

### 5. "权限错误"
**原因**: 文件或目录权限不足
**解决方案**:
```bash
# Windows (以管理员身份运行)
# Linux/macOS
sudo chmod -R 755 DocumentCreationSystem
```

## 功能验证

程序启动后，您应该看到：

1. **控制台输出**:
   ```
   === 文档管理及AI创作系统 - 功能测试 ===
   测试开始时间: 2025-07-23 ...
   
   === 测试服务注册 ===
   ✓ IProjectService 注册成功
   ✓ IDocumentService 注册成功
   ...
   ```

2. **GUI界面**（如果启动主程序）:
   - 主窗口正常显示
   - 菜单和工具栏可用
   - 项目导航面板显示

3. **功能测试通过**:
   - 所有服务注册成功
   - Agent功能正常
   - AI服务可用
   - 项目管理功能正常

## 性能优化建议

### 1. 硬件优化
- **SSD存储**: 提高文件读写速度
- **更多内存**: 支持更大的文档和模型
- **GPU加速**: 加速AI推理（如果支持）

### 2. 软件优化
- **关闭不必要的程序**: 释放系统资源
- **定期清理缓存**: 保持系统性能
- **更新驱动程序**: 确保硬件最佳性能

### 3. 网络优化
- **稳定的网络连接**: 确保AI服务访问
- **本地模型**: 使用Ollama等本地模型减少网络依赖
- **代理配置**: 如需要，配置网络代理

## 技术支持

### 日志文件
系统运行时会生成日志文件，位于：
- Windows: `%TEMP%\DocumentCreationSystem\logs\`
- Linux/macOS: `/tmp/DocumentCreationSystem/logs/`

### 诊断信息
运行以下命令获取系统诊断信息：
```bash
dotnet --info
systeminfo  # Windows
uname -a     # Linux/macOS
```

### 联系支持
如果遇到无法解决的问题，请提供：
1. 操作系统版本
2. .NET SDK版本
3. 错误消息和堆栈跟踪
4. 系统日志文件

## 更新和维护

### 检查更新
定期检查项目更新：
```bash
git pull origin main  # 如果使用Git
```

### 备份数据
重要数据备份位置：
- 项目文件: `./Projects/`
- 配置文件: `./Data/`
- 用户设置: `./Settings/`

### 清理缓存
定期清理系统缓存：
```bash
dotnet clean
rm -rf ./bin ./obj  # 清理编译缓存
```

## 开发环境设置

如果您想参与开发或自定义功能：

### 推荐工具
- **Visual Studio 2022** 或 **JetBrains Rider**
- **Git** 版本控制
- **Postman** API测试
- **Docker** 容器化部署

### 开发依赖
```bash
# 安装开发工具
dotnet tool install --global dotnet-ef
dotnet tool install --global dotnet-aspnet-codegenerator
```

### 代码规范
- 遵循C#编码规范
- 使用异步编程模式
- 添加完整的XML文档注释
- 编写单元测试

---

**祝您使用愉快！** 🎉

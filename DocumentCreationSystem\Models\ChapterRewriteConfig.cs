using System;
using System.Collections.Generic;
using System.Text.Json;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 章节重写配置
    /// </summary>
    public class ChapterRewriteConfig
    {
        /// <summary>
        /// 章节号
        /// </summary>
        public int ChapterNumber { get; set; }

        /// <summary>
        /// 章节文件路径
        /// </summary>
        public string ChapterFilePath { get; set; } = "";

        /// <summary>
        /// 项目路径
        /// </summary>
        public string ProjectPath { get; set; } = "";

        /// <summary>
        /// 选择的参考文件路径列表
        /// </summary>
        public List<string> ReferenceFilePaths { get; set; } = new();

        /// <summary>
        /// 是否自动保存
        /// </summary>
        public bool AutoSave { get; set; } = true;

        /// <summary>
        /// 是否备份原始文件
        /// </summary>
        public bool BackupOriginal { get; set; } = true;

        /// <summary>
        /// 是否启用断点续写
        /// </summary>
        public bool EnableResume { get; set; } = true;

        /// <summary>
        /// 最大上下文长度
        /// </summary>
        public int MaxContextLength { get; set; } = 4000;

        /// <summary>
        /// 重写风格
        /// </summary>
        public string RewriteStyle { get; set; } = "保持原风格";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 重写状态
        /// </summary>
        public ChapterRewriteStatus Status { get; set; } = ChapterRewriteStatus.NotStarted;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 重写进度（0-100）
        /// </summary>
        public int Progress { get; set; } = 0;

        /// <summary>
        /// 已生成的内容长度
        /// </summary>
        public int GeneratedContentLength { get; set; } = 0;

        /// <summary>
        /// 目标内容长度
        /// </summary>
        public int TargetContentLength { get; set; } = 6500;

        /// <summary>
        /// 备份文件路径
        /// </summary>
        public string? BackupFilePath { get; set; }

        /// <summary>
        /// 临时保存路径（用于断点续写）
        /// </summary>
        public string? TempSavePath { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 序列化为JSON
        /// </summary>
        public string ToJson()
        {
            return JsonSerializer.Serialize(this, new JsonSerializerOptions 
            { 
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        /// <summary>
        /// 从JSON反序列化
        /// </summary>
        public static ChapterRewriteConfig? FromJson(string json)
        {
            try
            {
                return JsonSerializer.Deserialize<ChapterRewriteConfig>(json);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        public void UpdateStatus(ChapterRewriteStatus status, string? errorMessage = null, int progress = -1)
        {
            Status = status;
            ErrorMessage = errorMessage;
            if (progress >= 0)
                Progress = progress;
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 获取状态描述
        /// </summary>
        public string GetStatusDescription()
        {
            return Status switch
            {
                ChapterRewriteStatus.NotStarted => "未开始",
                ChapterRewriteStatus.InProgress => $"进行中 ({Progress}%)",
                ChapterRewriteStatus.Completed => "已完成",
                ChapterRewriteStatus.Failed => $"失败: {ErrorMessage}",
                ChapterRewriteStatus.Paused => $"已暂停 ({Progress}%)",
                ChapterRewriteStatus.Cancelled => "已取消",
                _ => "未知状态"
            };
        }
    }

    /// <summary>
    /// 章节重写状态枚举
    /// </summary>
    public enum ChapterRewriteStatus
    {
        /// <summary>
        /// 未开始
        /// </summary>
        NotStarted = 0,

        /// <summary>
        /// 进行中
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 失败
        /// </summary>
        Failed = 3,

        /// <summary>
        /// 已暂停
        /// </summary>
        Paused = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled = 5
    }

    /// <summary>
    /// 章节重写事件参数（扩展版）
    /// </summary>
    public class EnhancedChapterRewriteEventArgs : EventArgs
    {
        /// <summary>
        /// 重写配置
        /// </summary>
        public ChapterRewriteConfig Config { get; set; } = new();

        /// <summary>
        /// 是否为续写操作
        /// </summary>
        public bool IsResume { get; set; } = false;

        /// <summary>
        /// 原始事件参数（兼容性）
        /// </summary>
        public string FilePath => Config.ChapterFilePath;
        public string FileName => System.IO.Path.GetFileNameWithoutExtension(Config.ChapterFilePath);
    }
}

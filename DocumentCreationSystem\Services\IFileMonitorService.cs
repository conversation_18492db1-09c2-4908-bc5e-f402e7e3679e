namespace DocumentCreationSystem.Services;

/// <summary>
/// 文件监控服务接口
/// </summary>
public interface IFileMonitorService
{
    /// <summary>
    /// 开始监控项目文件夹
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="projectPath">项目路径</param>
    /// <returns>是否开始监控成功</returns>
    Task<bool> StartMonitoringAsync(int projectId, string projectPath);

    /// <summary>
    /// 停止监控项目文件夹
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>是否停止监控成功</returns>
    Task<bool> StopMonitoringAsync(int projectId);

    /// <summary>
    /// 停止所有监控
    /// </summary>
    Task StopAllMonitoringAsync();

    /// <summary>
    /// 检查项目是否正在监控
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>是否正在监控</returns>
    bool IsMonitoring(int projectId);

    /// <summary>
    /// 获取正在监控的项目列表
    /// </summary>
    /// <returns>项目ID列表</returns>
    List<int> GetMonitoringProjects();

    /// <summary>
    /// 手动触发文件同步
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="filePath">文件路径</param>
    /// <returns>是否同步成功</returns>
    Task<bool> TriggerFileSyncAsync(int projectId, string filePath);

    /// <summary>
    /// 扫描项目文件夹并同步所有文件
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>同步的文件数量</returns>
    Task<int> ScanAndSyncProjectAsync(int projectId);

    /// <summary>
    /// 设置文件过滤器
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="includePatterns">包含模式</param>
    /// <param name="excludePatterns">排除模式</param>
    void SetFileFilter(int projectId, List<string> includePatterns, List<string> excludePatterns);

    /// <summary>
    /// 文件变更事件
    /// </summary>
    event EventHandler<FileChangeEventArgs> FileChanged;

    /// <summary>
    /// 文件创建事件
    /// </summary>
    event EventHandler<FileChangeEventArgs> FileCreated;

    /// <summary>
    /// 文件删除事件
    /// </summary>
    event EventHandler<FileChangeEventArgs> FileDeleted;

    /// <summary>
    /// 文件重命名事件
    /// </summary>
    event EventHandler<FileRenamedEventArgs> FileRenamed;

    /// <summary>
    /// 监控错误事件
    /// </summary>
    event EventHandler<MonitorErrorEventArgs> MonitorError;
}

/// <summary>
/// 文件变更事件参数
/// </summary>
public class FileChangeEventArgs : EventArgs
{
    public int ProjectId { get; set; }
    public string FilePath { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public FileChangeType ChangeType { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public long FileSize { get; set; }
    public string FileExtension { get; set; } = string.Empty;
}

/// <summary>
/// 文件重命名事件参数
/// </summary>
public class FileRenamedEventArgs : EventArgs
{
    public int ProjectId { get; set; }
    public string OldFilePath { get; set; } = string.Empty;
    public string NewFilePath { get; set; } = string.Empty;
    public string OldFileName { get; set; } = string.Empty;
    public string NewFileName { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 监控错误事件参数
/// </summary>
public class MonitorErrorEventArgs : EventArgs
{
    public int ProjectId { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;
    public string FilePath { get; set; } = string.Empty;
}

/// <summary>
/// 文件变更类型
/// </summary>
public enum FileChangeType
{
    Created,
    Modified,
    Deleted,
    Renamed
}

/// <summary>
/// 文件过滤器配置
/// </summary>
public class FileFilterConfig
{
    public List<string> IncludePatterns { get; set; } = new() { "*.docx", "*.md", "*.txt" };
    public List<string> ExcludePatterns { get; set; } = new() { "*.tmp", "*.bak", "~*" };
    public bool IncludeSubdirectories { get; set; } = true;
    public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100MB
    public bool IgnoreHiddenFiles { get; set; } = true;
}

/// <summary>
/// 监控状态信息
/// </summary>
public class MonitorStatus
{
    public int ProjectId { get; set; }
    public string ProjectPath { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public DateTime StartTime { get; set; }
    public int FilesWatched { get; set; }
    public int EventsProcessed { get; set; }
    public DateTime LastEventTime { get; set; }
    public List<string> RecentErrors { get; set; } = new();
    public FileFilterConfig FilterConfig { get; set; } = new();
}

<UserControl x:Class="DocumentCreationSystem.Controls.ThinkingChainViewer"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource MaterialDesignPaper}">
    
    <UserControl.Resources>
        <Style x:Key="ThinkingStepStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="0,4"/>
            <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
        
        <Style x:Key="KeyStepStyle" TargetType="Border" BasedOn="{StaticResource ThinkingStepStyle}">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
            <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="BorderThickness" Value="2"/>
        </Style>

        <Style x:Key="StepNumberStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
        </Style>
        
        <Style x:Key="StepContentStyle" TargetType="TextBlock">
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="20"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>
        
        <Style x:Key="StepTypeStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="10"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="FontStyle" Value="Italic"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
        </Style>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" materialDesign:ElevationAssist.Elevation="Dp1">
            <Grid Margin="16,12">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <Button x:Name="ToggleButton" 
                        Grid.Column="0"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Click="ToggleButton_Click"
                        ToolTip="展开/折叠思维链">
                    <materialDesign:PackIcon x:Name="ToggleIcon" Kind="ChevronDown"/>
                </Button>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="8,0">
                    <materialDesign:PackIcon Kind="Brain" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock x:Name="TitleText" 
                             Text="思维过程" 
                             FontWeight="Medium" 
                             VerticalAlignment="Center"/>
                    <TextBlock x:Name="StepCountText" 
                             Text="(0 步骤)" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             Margin="8,0,0,0"
                             VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button x:Name="SettingsButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Click="SettingsButton_Click"
                            ToolTip="显示设置">
                        <materialDesign:PackIcon Kind="Settings"/>
                    </Button>
                    
                    <Button x:Name="ExportButton"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Click="ExportButton_Click"
                            ToolTip="导出思维链">
                        <materialDesign:PackIcon Kind="Export"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 思维链内容 -->
        <ScrollViewer x:Name="ContentScrollViewer" 
                      Grid.Row="1" 
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Visibility="Collapsed">
            <StackPanel x:Name="ThinkingStepsPanel" Margin="8"/>
        </ScrollViewer>
        
        <!-- 统计信息 -->
        <materialDesign:Card x:Name="StatisticsCard" 
                           Grid.Row="2" 
                           Margin="0,8,0,0" 
                           materialDesign:ElevationAssist.Elevation="Dp1"
                           Visibility="Collapsed">
            <Expander Header="统计信息" IsExpanded="False">
                <Grid Margin="16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="总步骤数" FontWeight="Medium"/>
                        <TextBlock x:Name="TotalStepsText" Text="0" FontSize="18" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1">
                        <TextBlock Text="关键步骤" FontWeight="Medium"/>
                        <TextBlock x:Name="KeyStepsText" Text="0" FontSize="18" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2">
                        <TextBlock Text="处理时间" FontWeight="Medium"/>
                        <TextBlock x:Name="ProcessTimeText" Text="0ms" FontSize="18" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </StackPanel>
                </Grid>
            </Expander>
        </materialDesign:Card>
        
        <!-- 设置面板 -->
        <materialDesign:Card x:Name="SettingsPanel" 
                           Grid.Row="1" 
                           Margin="8"
                           materialDesign:ElevationAssist.Elevation="Dp4"
                           Visibility="Collapsed"
                           Panel.ZIndex="100">
            <StackPanel Margin="16">
                <TextBlock Text="显示设置" FontWeight="Medium" Margin="0,0,0,16"/>
                
                <CheckBox x:Name="ShowStepNumbersCheckBox" 
                        Content="显示步骤编号" 
                        IsChecked="True"
                        Margin="0,0,0,8"/>
                
                <CheckBox x:Name="HighlightKeyStepsCheckBox" 
                        Content="高亮关键步骤" 
                        IsChecked="True"
                        Margin="0,0,0,8"/>
                
                <CheckBox x:Name="ShowStatisticsCheckBox" 
                        Content="显示统计信息" 
                        IsChecked="False"
                        Margin="0,0,0,16"/>
                
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" Text="最大显示步骤数" VerticalAlignment="Center"/>
                    <TextBox Grid.Column="1" 
                           x:Name="MaxStepsTextBox"
                           Text="0" 
                           Width="60"
                           materialDesign:HintAssist.Hint="0=全部"/>
                </Grid>
                
                <ComboBox x:Name="DisplayStyleComboBox"
                        materialDesign:HintAssist.Hint="显示样式"
                        materialDesign:HintAssist.IsFloating="True"
                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                        Margin="0,0,0,16">
                    <ComboBoxItem Content="可展开" Tag="Expandable" IsSelected="True"/>
                    <ComboBoxItem Content="内联显示" Tag="Inline"/>
                    <ComboBoxItem Content="侧边栏" Tag="Sidebar"/>
                </ComboBox>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="重置" 
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ResetSettings_Click"/>
                    <Button Content="应用" 
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Click="ApplySettings_Click"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>

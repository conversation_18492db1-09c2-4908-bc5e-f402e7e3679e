using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 清理重复文件的工具类
    /// </summary>
    public class CleanupDuplicateFiles
    {
        public static void Main(string[] args)
        {
            var workspaceDir = @"d:\AI_project\文档管理及创作系统";
            
            Console.WriteLine("=== 清理重复章节文件 ===");
            Console.WriteLine($"工作目录: {workspaceDir}");
            
            CleanupChapterFiles(workspaceDir);
            
            Console.WriteLine("\n=== 清理完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        static void CleanupChapterFiles(string workspaceDir)
        {
            try
            {
                // 查找所有章节文件
                var chapterFiles = Directory.GetFiles(workspaceDir, "*章*.txt", SearchOption.TopDirectoryOnly)
                    .Where(f => Path.GetFileName(f).Contains("重生之我在乱世靠收尸成圣"))
                    .ToList();
                
                Console.WriteLine($"找到 {chapterFiles.Count} 个章节文件:");
                foreach (var file in chapterFiles)
                {
                    Console.WriteLine($"  - {Path.GetFileName(file)}");
                }
                
                // 按章节号分组
                var chapterGroups = new Dictionary<int, List<string>>();
                
                foreach (var file in chapterFiles)
                {
                    var fileName = Path.GetFileName(file);
                    var chapterNumber = ExtractChapterNumber(fileName);
                    
                    if (chapterNumber > 0)
                    {
                        if (!chapterGroups.ContainsKey(chapterNumber))
                        {
                            chapterGroups[chapterNumber] = new List<string>();
                        }
                        chapterGroups[chapterNumber].Add(file);
                    }
                }
                
                Console.WriteLine($"\n按章节分组后:");
                foreach (var group in chapterGroups.OrderBy(g => g.Key))
                {
                    Console.WriteLine($"第{group.Key}章: {group.Value.Count} 个文件");
                    
                    if (group.Value.Count > 1)
                    {
                        Console.WriteLine("  重复文件:");
                        foreach (var file in group.Value)
                        {
                            Console.WriteLine($"    - {Path.GetFileName(file)}");
                        }
                        
                        // 保留最新的文件（按文件名格式优先级）
                        var fileToKeep = SelectBestFile(group.Value);
                        var filesToDelete = group.Value.Where(f => f != fileToKeep).ToList();
                        
                        Console.WriteLine($"  保留: {Path.GetFileName(fileToKeep)}");
                        
                        foreach (var fileToDelete in filesToDelete)
                        {
                            try
                            {
                                File.Delete(fileToDelete);
                                Console.WriteLine($"  已删除: {Path.GetFileName(fileToDelete)}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"  删除失败: {Path.GetFileName(fileToDelete)} - {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理过程中发生错误: {ex.Message}");
            }
        }
        
        static int ExtractChapterNumber(string fileName)
        {
            try
            {
                // 尝试匹配不同的章节号格式
                var patterns = new[]
                {
                    @"第(\d+)章",
                    @"章(\d+)",
                    @"_(\d+)章_",
                    @"章(\d{3,4})_"
                };
                
                foreach (var pattern in patterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(fileName, pattern);
                    if (match.Success && int.TryParse(match.Groups[1].Value, out int chapterNumber))
                    {
                        return chapterNumber;
                    }
                }
                
                return 0;
            }
            catch
            {
                return 0;
            }
        }
        
        static string SelectBestFile(List<string> files)
        {
            // 优先级：第XX卷_第XXX章 > 卷XX_章XXX > 其他格式
            var priorityScores = files.Select(f => new
            {
                File = f,
                Score = CalculateFileScore(Path.GetFileName(f))
            }).OrderByDescending(x => x.Score).ToList();
            
            return priorityScores.First().File;
        }
        
        static int CalculateFileScore(string fileName)
        {
            int score = 0;
            
            // 标准格式：第XX卷_第XXX章
            if (fileName.Contains("第") && fileName.Contains("卷") && fileName.Contains("章"))
            {
                score += 100;
            }
            
            // 包含卷信息
            if (fileName.Contains("卷"))
            {
                score += 50;
            }
            
            // 包含章节标题
            if (fileName.Count(c => c == '_') >= 3)
            {
                score += 20;
            }
            
            // 文件名长度适中（包含更多信息）
            if (fileName.Length > 30 && fileName.Length < 100)
            {
                score += 10;
            }
            
            return score;
        }
    }
}

<Window x:Class="DocumentCreationSystem.Views.TestCreateProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="测试创建项目对话框" Height="300" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="测试创建项目对话框" 
                   FontSize="18" FontWeight="Medium" 
                   Margin="0,0,0,20"/>

        <!-- 项目名称 -->
        <TextBox Grid.Row="1" x:Name="ProjectNameTextBox"
                 materialDesign:HintAssist.Hint="项目名称"
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 Margin="0,0,0,15"
                 Text="测试项目"/>

        <!-- 项目描述 -->
        <TextBox Grid.Row="2" x:Name="ProjectDescriptionTextBox"
                 materialDesign:HintAssist.Hint="项目描述"
                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                 AcceptsReturn="True"
                 TextWrapping="Wrap"
                 Height="80"
                 VerticalScrollBarVisibility="Auto"
                 Margin="0,0,0,15"
                 Text="这是一个测试项目"/>

        <!-- 空白区域 -->
        <Border Grid.Row="3"/>

        <!-- 按钮区域 -->
        <Border Grid.Row="4" BorderThickness="0,1,0,0" 
                BorderBrush="{DynamicResource MaterialDesignDivider}"
                Margin="0,20,0,0" Padding="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="CancelButton" 
                        Content="取消" 
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Margin="0,0,15,0"
                        MinWidth="90"
                        Height="40"
                        Padding="16,8"
                        Click="CancelButton_Click"/>
                <Button x:Name="CreateButton" 
                        Content="创建项目" 
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        MinWidth="120"
                        Height="40"
                        Padding="16,8"
                        IsDefault="True"
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                        Click="CreateButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

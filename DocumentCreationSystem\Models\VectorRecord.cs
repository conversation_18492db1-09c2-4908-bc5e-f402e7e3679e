

namespace DocumentCreationSystem.Models;

/// <summary>
/// 向量记录实体类
/// </summary>
public class VectorRecord
{
    public int Id { get; set; }

    /// <summary>
    /// 关联的文档ID
    /// </summary>
    public int DocumentId { get; set; }

    /// <summary>
    /// 在向量数据库中的唯一标识
    /// </summary>
    public string VectorId { get; set; } = string.Empty;

    /// <summary>
    /// 文本片段内容
    /// </summary>
    public string TextContent { get; set; } = string.Empty;

    /// <summary>
    /// 文本片段在原文档中的起始位置
    /// </summary>
    public int StartPosition { get; set; }

    /// <summary>
    /// 文本片段在原文档中的结束位置
    /// </summary>
    public int EndPosition { get; set; }

    /// <summary>
    /// 向量维度
    /// </summary>
    public int VectorDimension { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 索引状态：Pending-待处理，Indexed-已索引，Failed-失败
    /// </summary>
    public string IndexStatus { get; set; } = "Pending";

    /// <summary>
    /// 错误信息（如果索引失败）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 文本片段的哈希值（用于检测内容变化）
    /// </summary>
    public string? ContentHash { get; set; }

    /// <summary>
    /// 元数据（JSON格式）
    /// 可以存储章节信息、段落类型等额外信息
    /// </summary>
    public string? Metadata { get; set; }


}

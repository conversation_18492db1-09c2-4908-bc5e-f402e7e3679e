<Application x:Class="DocumentCreationSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design 主题资源 -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局样式 -->
            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="8"/>
                <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
            </Style>

            <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
                <Setter Property="FontWeight" Value="Medium"/>
                <Setter Property="FontSize" Value="16"/>
                <Setter Property="Margin" Value="0,0,0,8"/>
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
            </Style>

            <Style x:Key="ToolButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                <Setter Property="Margin" Value="0,4"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Height" Value="36"/>
            </Style>

            <Style x:Key="OutlineButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                <Setter Property="Margin" Value="0,4"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="Height" Value="36"/>
            </Style>



            <!-- 转换器 -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        </ResourceDictionary>
    </Application.Resources>
</Application>

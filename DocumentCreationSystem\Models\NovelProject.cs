

namespace DocumentCreationSystem.Models;

/// <summary>
/// 小说项目实体类
/// </summary>
public class NovelProject
{
    public int Id { get; set; }

    /// <summary>
    /// 关联的项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 小说标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 创作方向描述
    /// </summary>
    public string? CreativeDirection { get; set; }

    /// <summary>
    /// 世界观设定（JSON格式）
    /// </summary>
    public string? WorldSetting { get; set; }

    /// <summary>
    /// 详细世界设定信息（JSON格式）
    /// </summary>
    public string? DetailedWorldSettings { get; set; }

    /// <summary>
    /// 目标章节数
    /// </summary>
    public int TargetChapterCount { get; set; } = 1000;

    /// <summary>
    /// 当前章节数
    /// </summary>
    public int CurrentChapterCount { get; set; } = 0;

    /// <summary>
    /// 每章目标字数
    /// </summary>
    public int TargetWordsPerChapter { get; set; } = 6500;

    /// <summary>
    /// 总字数统计
    /// </summary>
    public int TotalWordCount { get; set; } = 0;

    /// <summary>
    /// 创作状态：Planning-规划中，Writing-创作中，Paused-暂停，Completed-已完成
    /// </summary>
    public string Status { get; set; } = "Planning";

    /// <summary>
    /// 全书大纲
    /// </summary>
    public string? OverallOutline { get; set; }

    /// <summary>
    /// 卷宗大纲（JSON格式）
    /// </summary>
    public string? VolumeOutlines { get; set; }

    /// <summary>
    /// 角色列表（JSON格式）
    /// </summary>
    public string? Characters { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后创作时间
    /// </summary>
    public DateTime? LastWritingTime { get; set; }


}

# Ollama模型下载功能测试指南

## 测试前准备

### 1. 确保Ollama服务运行
```bash
# 启动Ollama服务
ollama serve

# 或者检查服务状态
curl http://localhost:11434/api/tags
```

### 2. 编译并运行程序
```bash
cd DocumentCreationSystem
dotnet build
dotnet run
```

## 测试步骤

### 测试1：基本功能测试

#### 1.1 打开下载对话框
1. 启动程序
2. 点击菜单栏的"模型配置"
3. 确保选择了"Ollama"平台
4. 点击"下载模型"按钮
5. **预期结果**：打开Ollama模型下载对话框

#### 1.2 测试连接功能
1. 在对话框中确认Ollama地址为 `http://localhost:11434`
2. 点击"测试连接"按钮
3. **预期结果**：显示"连接成功！"状态

#### 1.3 选择预设模型
1. 在"常用模型"下拉框中选择"bge-m3 (向量模型)"
2. **预期结果**：自定义模型输入框被禁用

#### 1.4 测试自定义模型
1. 在"常用模型"下拉框中选择"自定义..."
2. **预期结果**：自定义模型输入框被启用
3. 输入模型名称，如"llama3.2:1b"

### 测试2：下载进度测试

#### 2.1 下载小模型（推荐用于测试）
1. 选择"llama3.2:1b"模型（相对较小）
2. 点击"开始下载"按钮
3. **预期结果**：
   - 按钮文本变为"取消下载"
   - 进度条开始显示
   - 状态文本显示当前操作
   - 日志窗口显示实时信息

#### 2.2 观察进度显示
在下载过程中观察：
- **状态变化**：pulling manifest → downloading → verifying → writing manifest → success
- **进度条**：从0%逐渐增加到100%
- **进度文本**：显示"下载 llama3.2:1b: X.XMB / Y.YMB (Z.Z%)"
- **日志输出**：实时显示下载状态

#### 2.3 测试取消功能
1. 在下载过程中点击"取消下载"按钮
2. **预期结果**：
   - 下载停止
   - 状态显示"下载已取消"
   - 按钮恢复为"开始下载"
   - 界面元素重新启用

### 测试3：错误处理测试

#### 3.1 测试连接错误
1. 修改Ollama地址为无效地址（如：`http://localhost:9999`）
2. 点击"测试连接"
3. **预期结果**：显示连接失败错误信息

#### 3.2 测试无效模型
1. 恢复正确的Ollama地址
2. 选择"自定义..."并输入不存在的模型名（如：`invalid-model-name`）
3. 点击"开始下载"
4. **预期结果**：显示模型不存在的错误信息

#### 3.3 测试重复下载
1. 选择一个已存在的模型（如之前下载的模型）
2. 点击"开始下载"
3. **预期结果**：弹出确认对话框询问是否重新下载

### 测试4：集成测试

#### 4.1 测试模型列表刷新
1. 成功下载一个模型后
2. 点击"关闭"按钮退出下载对话框
3. 在AI模型配置窗口中点击"检测模型"
4. **预期结果**：新下载的模型出现在可用模型列表中

#### 4.2 测试模型使用
1. 在模型列表中选择新下载的模型
2. 点击"测试连接"验证模型可用
3. **预期结果**：模型测试成功

## 常见问题排查

### 问题1：无法连接到Ollama服务
**症状**：测试连接失败
**解决方案**：
1. 确认Ollama服务已启动：`ollama serve`
2. 检查端口是否被占用：`netstat -an | findstr 11434`
3. 尝试手动访问：`curl http://localhost:11434/api/tags`

### 问题2：下载速度很慢
**症状**：下载进度长时间不变
**解决方案**：
1. 检查网络连接
2. 尝试更换模型源
3. 检查磁盘空间是否充足

### 问题3：下载中断
**症状**：下载过程中出现错误
**解决方案**：
1. 检查网络稳定性
2. 重新启动Ollama服务
3. 清理Ollama缓存：`ollama rm <model-name>`

### 问题4：界面无响应
**症状**：点击按钮无反应
**解决方案**：
1. 检查是否有异常日志
2. 重启应用程序
3. 检查.NET运行时环境

## 性能测试

### 测试不同大小的模型
1. **小模型**（<1GB）：llama3.2:1b, phi3:mini
2. **中等模型**（1-4GB）：llama3.2:3b, qwen2.5:3b
3. **大模型**（>4GB）：qwen2.5:7b

### 观察指标
- **下载速度**：MB/s
- **内存使用**：任务管理器中的内存占用
- **CPU使用率**：下载过程中的CPU占用
- **界面响应性**：UI是否流畅

## 自动化测试建议

### 单元测试
```csharp
[Test]
public async Task PullModelAsync_ValidModel_ReturnsTrue()
{
    var service = new OllamaModelPullService(logger);
    var progress = new Progress<OllamaModelPullProgress>();
    
    var result = await service.PullModelAsync(
        "http://localhost:11434", 
        "llama3.2:1b", 
        progress);
    
    Assert.IsTrue(result);
}
```

### 集成测试
```csharp
[Test]
public async Task DownloadDialog_CompleteFlow_Success()
{
    var dialog = new OllamaModelDownloadDialog();
    dialog.SetOllamaUrl("http://localhost:11434");
    
    // 模拟用户操作
    // 验证结果
}
```

## 测试报告模板

### 测试环境
- **操作系统**：Windows 11
- **Ollama版本**：0.x.x
- **.NET版本**：8.0
- **内存**：16GB
- **网络**：100Mbps

### 测试结果
| 测试项目 | 状态 | 备注 |
|---------|------|------|
| 连接测试 | ✅ | 正常 |
| 模型下载 | ✅ | 进度显示正常 |
| 取消功能 | ✅ | 响应及时 |
| 错误处理 | ✅ | 提示清晰 |
| 界面响应 | ✅ | 流畅 |

### 性能数据
- **平均下载速度**：10MB/s
- **内存峰值使用**：150MB
- **CPU平均使用率**：15%

通过这些测试，可以确保Ollama模型下载功能的稳定性和用户体验。

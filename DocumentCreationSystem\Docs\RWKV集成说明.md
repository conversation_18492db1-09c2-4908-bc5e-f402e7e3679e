# RWKV模型集成说明

## 概述

本系统已集成RWKV（Receptance Weighted Key Value）开源大语言模型支持，通过API调用方式使用RWKV模型。

## 功能特性

### 1. API调用支持
- **API模式**：调用RWKV API服务，兼容OpenAI格式
- 支持本地和远程RWKV API服务

### 2. 模型管理
- 支持多种RWKV模型版本（RWKV-4/5/6/7系列）
- 自动检测可用模型
- 动态模型切换

### 3. 智能写作功能
- 文本润色和扩写
- 章节内容生成
- 按要求续写
- 一致性检查

## 配置说明

### 基本配置

1. **API地址**：RWKV API服务地址，默认为 `http://localhost:8000`
2. **API密钥**：API调用时的密钥（可选）

## 支持的模型

### RWKV-7系列（最新）
- **RWKV-v7-2.9B-G1-GGUF**：最新版本，支持GPU推理

### RWKV-6 World系列（推荐）
- **RWKV-6-World-1B6**：轻量级模型，适合资源受限环境
- **RWKV-6-World-3B**：平衡性能，推荐日常使用
- **RWKV-6-World-7B**：高性能模型，适合专业写作
- **RWKV-6-World-14B**：大型模型，最佳质量

### RWKV-5 World系列
- **RWKV-5-World-1B5**：经典轻量级模型
- **RWKV-5-World-3B**：经典中型模型

### RWKV-4 Raven系列
- **RWKV-4-Raven-1B5/3B/7B/14B**：对话优化版本

## 安装和设置

### 1. 准备RWKV API服务

您需要运行一个兼容OpenAI格式的RWKV API服务，可以使用：
- RWKV Runner（推荐）：https://github.com/josStorer/RWKV-Runner
- 其他RWKV API实现

### 2. 配置步骤

1. 打开AI模型配置界面
2. 选择"RWKV"平台
3. 配置API服务地址
4. 设置API密钥（如果需要）
5. 点击"检测可用模型"验证配置
6. 选择要使用的模型
7. 保存配置

## 使用方法

### 文本润色
```csharp
var result = await rwkvService.PolishTextAsync("原始文本", "上下文");
```

### 文本扩写
```csharp
var result = await rwkvService.ExpandTextAsync("原始文本", 1000, "上下文");
```

### 章节生成
```csharp
var result = await rwkvService.GenerateChapterAsync("章节大纲", "上下文", 6500);
```

## API格式

RWKV服务使用兼容OpenAI的API格式：

### 模型列表
```
GET /v1/models
```

### 文本生成
```
POST /v1/completions
Content-Type: application/json

{
  "model": "RWKV-6-World-3B",
  "prompt": "你好",
  "max_tokens": 2000,
  "temperature": 0.7
}
```

## 性能优化建议

### 硬件要求
- **1B-3B模型**：8GB RAM，可选GPU加速
- **7B模型**：16GB RAM，推荐GPU加速
- **14B模型**：32GB RAM，强烈推荐GPU加速

### 配置优化
1. **分段生成**：启用分段生成功能，避免长文本生成失败
2. **分段字数**：根据模型大小调整，建议1000-2000字
3. **温度设置**：创意写作使用0.7-0.9，技术文档使用0.3-0.5

## 故障排除

### 常见问题

1. **连接失败**
   - 检查RWKV Runner是否正常运行
   - 验证服务地址和端口
   - 检查防火墙设置

2. **模型加载失败**
   - 确认模型文件路径正确
   - 检查模型文件完整性
   - 验证系统内存是否足够

3. **生成质量问题**
   - 调整温度参数
   - 优化提示词
   - 尝试不同的模型版本

### 日志查看

系统会记录详细的RWKV服务日志，包括：
- 模型加载状态
- API调用记录
- 错误信息和解决建议

## 最佳实践

1. **模型选择**：根据硬件配置选择合适的模型大小
2. **提示词优化**：使用清晰、具体的提示词
3. **分段处理**：对于长文本，启用分段生成
4. **资源监控**：关注GPU/CPU使用率，避免过载
5. **定期更新**：关注RWKV Runner和模型的更新

## 技术支持

如遇到问题，请：
1. 查看系统日志获取详细错误信息
2. 参考RWKV Runner官方文档
3. 检查模型和软件版本兼容性
4. 联系技术支持团队

## 更新日志

- **v1.0**：初始RWKV集成
  - 支持本地和API两种模式
  - 集成10种预设模型
  - 提供完整的配置界面
  - 支持自动模型检测

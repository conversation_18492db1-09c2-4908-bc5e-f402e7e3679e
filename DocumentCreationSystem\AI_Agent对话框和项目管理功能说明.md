# AI Agent对话框和项目管理功能说明

## 功能概述

本次更新添加了两个重要功能：
1. **AI Agent悬浮对话框** - 双击AI助手标签弹出，支持AI工具调用
2. **项目管理界面** - 预览和管理所有项目ID，支持批量删除

## 1. AI Agent悬浮对话框

### 功能特点

#### 🤖 智能对话
- 悬浮窗口设计，不影响主界面操作
- 支持多轮对话，保持上下文记忆
- 实时显示AI思考和工具执行状态

#### 🛠️ 强大的工具调用系统
AI可以使用以下工具操作项目文件：

**文件操作工具：**
- `read_file(path)` - 读取文件内容
- `write_file(path|content)` - 写入文件内容
- `list_files(directory)` - 列出目录文件
- `delete_file(path)` - 删除文件
- `move_file(sourcePath|destinationPath)` - 移动文件
- `get_file_info(path)` - 获取文件详细信息

**目录操作工具：**
- `create_directory(path)` - 创建目录
- `search_files(keyword)` - 搜索包含关键词的文件

**内容分析工具：**
- `analyze_story(content)` - 分析故事内容
- `generate_content(prompt|type)` - 生成新内容

#### 📝 内容生成类型
支持多种内容类型生成：
- `chapter` - 小说章节（约6500字）
- `character` - 角色设定
- `plot` - 情节大纲
- `dialogue` - 对话内容
- `description` - 场景描写

### 使用方法

#### 启动对话框
1. **双击AI助手标签**（右侧面板顶部的"AI助手"区域）
2. 系统会弹出悬浮的AI Agent对话框
3. 确保已打开项目，AI才能访问项目文件

#### 与AI对话
1. 在底部输入框输入您的需求
2. 支持以下快捷键：
   - `Enter` - 发送消息
   - `Ctrl+Enter` - 发送消息
   - `Shift+Enter` - 换行

#### AI工具调用示例

**查看项目文件：**
```
请帮我查看项目中有哪些文件
```
AI会自动调用 `list_files()` 工具

**分析章节内容：**
```
请分析第一章的内容，给出改进建议
```
AI会调用 `read_file()` 和 `analyze_story()` 工具

**生成新章节：**
```
请根据现有大纲生成第五章的内容
```
AI会调用 `read_file()` 读取大纲，然后用 `generate_content()` 生成章节

**修改文件内容：**
```
请将第三章中的主角名字从"张三"改为"李四"
```
AI会调用 `read_file()` 读取内容，修改后用 `write_file()` 保存

### 界面特性

#### 消息类型
- **用户消息** - 蓝色气泡，右对齐
- **AI消息** - 白色气泡，左对齐，显示"AI Agent"标识
- **工具调用** - 灰色背景，显示工具执行过程和结果

#### 状态指示
- **网络状态** - 顶部右侧显示工具执行状态
- **底部状态栏** - 显示当前操作状态
- **实时反馈** - 工具执行时显示进度

#### 操作按钮
- **附加文件** - 选择文件进行分析
- **清空对话** - 清除所有对话历史
- **设置** - 配置对话参数（待实现）
- **关闭** - 关闭对话框

## 2. 项目管理界面

### 功能特点

#### 📊 项目列表预览
- 显示所有项目的详细信息
- 支持按名称、描述、路径搜索
- 可排序的数据表格
- 实时显示项目统计信息

#### 🗑️ 批量删除功能
- 支持单个项目删除
- 支持批量选择删除
- 全选/取消全选功能
- 安全确认对话框

#### 🔧 项目维护工具
- 清理无效项目（文件夹不存在）
- 导出项目列表为CSV
- 项目信息统计

### 使用方法

#### 打开项目管理
1. 点击菜单栏 **工具 → 项目管理**
2. 系统会显示所有项目的列表

#### 项目操作

**查看项目：**
- 双击项目行可直接打开项目
- 点击操作列的"打开"按钮

**删除项目：**
- 单个删除：点击操作列的"删除"按钮
- 批量删除：
  1. 勾选要删除的项目
  2. 点击顶部"删除选中"按钮
  3. 确认删除操作

**搜索项目：**
- 在搜索框中输入关键词
- 支持搜索项目名称、描述、路径

**维护操作：**
- **刷新列表** - 重新加载项目数据
- **清理无效项目** - 自动检测并删除文件夹不存在的项目
- **导出列表** - 将项目信息导出为CSV文件

### 数据表格列说明

| 列名 | 说明 |
|------|------|
| 选择 | 复选框，用于批量操作 |
| ID | 项目的唯一标识符 |
| 项目名称 | 项目的显示名称 |
| 描述 | 项目的详细描述 |
| 路径 | 项目文件夹的完整路径 |
| 创建时间 | 项目的创建时间 |
| 最后修改 | 项目的最后修改时间 |
| 操作 | 打开和删除按钮 |

### 安全特性

#### 删除保护
- 删除前显示确认对话框
- 只删除数据库记录，不删除实际文件夹
- 支持批量删除的二次确认

#### 数据验证
- 自动检测无效项目
- 提供清理建议
- 防止误操作

## 技术实现

### AI Agent对话框
- **AgentChatDialog.xaml** - 悬浮对话框界面
- **AgentToolService.cs** - AI工具调用服务
- 支持工具调用的智能解析和执行
- 完整的错误处理和状态管理

### 项目管理界面
- **ProjectManagementDialog.xaml** - 项目管理界面
- **ProjectViewModel** - 项目数据绑定模型
- 支持搜索、排序、批量操作
- 集成数据存储服务

### 集成方式
- 双击AI助手标签触发Agent对话框
- 工具菜单集成项目管理功能
- 完整的依赖注入和服务管理

## 使用建议

### AI Agent对话框
1. **明确需求** - 清楚地描述您想要AI做什么
2. **提供上下文** - 告诉AI相关的文件位置和背景信息
3. **分步操作** - 复杂任务可以分解为多个步骤
4. **验证结果** - AI操作后检查文件是否符合预期

### 项目管理
1. **定期清理** - 使用清理功能移除无效项目
2. **备份重要项目** - 删除前确保重要数据已备份
3. **合理命名** - 使用有意义的项目名称和描述
4. **导出记录** - 定期导出项目列表作为备份

## 注意事项

1. **项目依赖** - AI Agent功能需要先打开项目
2. **文件安全** - AI可以修改项目文件，请谨慎使用
3. **网络连接** - AI功能需要网络连接
4. **权限要求** - 确保对项目文件夹有读写权限

这两个功能大大增强了系统的智能化程度和项目管理能力，为用户提供了更便捷的创作和管理体验。



namespace DocumentCreationSystem.Models;

/// <summary>
/// 项目实体类
/// </summary>
public class Project
{
    public int Id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目类型：Normal-普通文档项目，Novel-小说项目
    /// </summary>
    public string Type { get; set; } = "Normal";

    /// <summary>
    /// 项目描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 项目根路径
    /// </summary>
    public string RootPath { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 项目配置（JSON格式）
    /// </summary>
    public string? Configuration { get; set; }

    /// <summary>
    /// 向量索引状态
    /// </summary>
    public bool VectorIndexed { get; set; } = false;

    /// <summary>
    /// 项目状态：Active-活跃，Archived-归档，Deleted-已删除
    /// </summary>
    public string Status { get; set; } = "Active";
}

/// <summary>
/// 项目历史记录
/// </summary>
public class ProjectHistory
{
    public int Id { get; set; }

    /// <summary>
    /// 项目名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 项目根路径
    /// </summary>
    public string RootPath { get; set; } = string.Empty;

    /// <summary>
    /// 最后访问时间
    /// </summary>
    public DateTime LastAccessTime { get; set; } = DateTime.Now;

    /// <summary>
    /// 访问次数
    /// </summary>
    public int AccessCount { get; set; } = 1;

    /// <summary>
    /// 是否收藏
    /// </summary>
    public bool IsFavorite { get; set; } = false;

    /// <summary>
    /// 项目类型
    /// </summary>
    public string Type { get; set; } = "Normal";
}

/// <summary>
/// 项目状态枚举
/// </summary>
public enum ProjectStatus
{
    Active,
    Archived,
    Deleted
}

/// <summary>
/// 项目类型常量
/// </summary>
public static class ProjectTypes
{
    public const string Normal = "Normal";
    public const string Novel = "Novel";
    public const string Research = "Research";
    public const string Documentation = "Documentation";
}

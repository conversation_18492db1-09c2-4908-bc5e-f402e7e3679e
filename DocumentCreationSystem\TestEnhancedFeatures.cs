using System;
using DocumentCreationSystem.Services;
using Microsoft.Extensions.Logging;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试增强功能的简单程序
    /// </summary>
    public class TestEnhancedFeatures
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== 增强写书功能测试 ===");
            Console.WriteLine();

            try
            {
                // 测试ContentQualityService的output标签清理功能
                TestOutputTagCleaning();
                
                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
                Console.WriteLine("所有基础功能测试通过！");
                Console.WriteLine();
                Console.WriteLine("注意：完整的增强功能需要在主程序中通过依赖注入使用。");
                Console.WriteLine("新增的服务包括：");
                Console.WriteLine("- EnhancedChapterContentService: 增强章节内容生成");
                Console.WriteLine("- CharacterUpdateService: 角色信息更新");
                Console.WriteLine("- ChapterOutlineUpdateService: 章节细纲更新");
                Console.WriteLine("- TimelineService增强: 时间线管理");
                Console.WriteLine("- ContentQualityService增强: 内容清理和质量评估");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试output标签清理功能
        /// </summary>
        private static void TestOutputTagCleaning()
        {
            Console.WriteLine("=== 测试output标签清理功能 ===");

            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<ContentQualityService>();
            var contentQualityService = new ContentQualityService(logger);

            // 测试用例1：标准output标签
            var testContent1 = "这是正文内容。<output>这是需要删除的内容</output>继续正文。";
            var cleaned1 = contentQualityService.CleanOutputTags(testContent1);
            Console.WriteLine($"测试1 - 标准output标签:");
            Console.WriteLine($"原文: {testContent1}");
            Console.WriteLine($"清理后: {cleaned1}");
            Console.WriteLine($"结果: {(cleaned1.Contains("<output>") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例2：大小写变体
            var testContent2 = "正文<OUTPUT>删除内容</OUTPUT>继续<Output>删除</Output>结束";
            var cleaned2 = contentQualityService.CleanOutputTags(testContent2);
            Console.WriteLine($"测试2 - 大小写变体:");
            Console.WriteLine($"原文: {testContent2}");
            Console.WriteLine($"清理后: {cleaned2}");
            Console.WriteLine($"结果: {(cleaned2.Contains("OUTPUT") || cleaned2.Contains("Output") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例3：拼写错误
            var testContent3 = "正文<ouput>删除内容</ouput>继续";
            var cleaned3 = contentQualityService.CleanOutputTags(testContent3);
            Console.WriteLine($"测试3 - 拼写错误:");
            Console.WriteLine($"原文: {testContent3}");
            Console.WriteLine($"清理后: {cleaned3}");
            Console.WriteLine($"结果: {(cleaned3.Contains("ouput") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例4：多个标签
            var testContent4 = "开始<output>删除1</output>中间<response>删除2</response>结束<o>删除3</o>完成";
            var cleaned4 = contentQualityService.CleanOutputTags(testContent4);
            Console.WriteLine($"测试4 - 多个标签:");
            Console.WriteLine($"原文: {testContent4}");
            Console.WriteLine($"清理后: {cleaned4}");
            Console.WriteLine($"结果: {(cleaned4.Contains("<") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例5：嵌套和复杂情况
            var testContent5 = @"章节开始
<output>
这是一个多行的输出内容
包含换行符
</output>
正文继续
<RESPONSE>大写标签</RESPONSE>
结束";
            var cleaned5 = contentQualityService.CleanOutputTags(testContent5);
            Console.WriteLine($"测试5 - 复杂情况:");
            Console.WriteLine($"原文: {testContent5}");
            Console.WriteLine($"清理后: {cleaned5}");
            Console.WriteLine($"结果: {(cleaned5.Contains("<") ? "失败" : "成功")}");
            Console.WriteLine();

            Console.WriteLine("output标签清理功能测试完成！");
        }
    }
}

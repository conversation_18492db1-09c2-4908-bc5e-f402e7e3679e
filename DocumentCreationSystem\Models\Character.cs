

namespace DocumentCreationSystem.Models;

/// <summary>
/// 角色实体类
/// </summary>
public class Character
{
    public int Id { get; set; }

    /// <summary>
    /// 所属小说项目ID
    /// </summary>
    public int NovelProjectId { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 角色描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 角色属性（JSON格式）
    /// 例如：{"境界": "筑基期", "修为": "筑基三层", "体质": "先天灵体"}
    /// </summary>
    public string? Attributes { get; set; }

    /// <summary>
    /// 技能列表（JSON数组格式）
    /// 例如：["御剑术", "五行遁法", "神识探查"]
    /// </summary>
    public string? Skills { get; set; }

    /// <summary>
    /// 装备列表（JSON数组格式）
    /// 例如：["青锋剑", "护体法衣", "储物戒"]
    /// </summary>
    public string? Equipment { get; set; }

    /// <summary>
    /// 角色类型：Protagonist-主角，Supporting-配角，Antagonist-反派，Minor-次要角色
    /// </summary>
    public string Type { get; set; } = "Minor";

    /// <summary>
    /// 重要程度（1-10，10最重要）
    /// </summary>
    public int Importance { get; set; } = 1;

    /// <summary>
    /// 首次出现章节
    /// </summary>
    public int? FirstAppearanceChapter { get; set; }

    /// <summary>
    /// 最后出现章节
    /// </summary>
    public int? LastAppearanceChapter { get; set; }

    /// <summary>
    /// 最后更新的章节号
    /// </summary>
    public int? LastUpdatedChapter { get; set; }

    /// <summary>
    /// 角色状态：Active-活跃，Inactive-不活跃，Dead-已死亡
    /// </summary>
    public string Status { get; set; } = "Active";

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新历史（JSON格式）
    /// 记录角色属性、技能、装备的变化历史
    /// </summary>
    public string? UpdateHistory { get; set; }


}

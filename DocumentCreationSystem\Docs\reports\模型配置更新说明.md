# AI模型配置更新说明

## 更新概述

根据最新的API限制和要求，已更新智谱AI和DeepSeek的模型配置，确保系统使用正确的模型名称和API调用格式。

## 智谱AI模型更新

### 新的可用模型列表
根据智谱AI的最新限制，现在仅支持以下模型：

1. **GLM-4-Flash-250414** (默认)
   - 类型：对话模型
   - 用途：主要用于文本创作
   - Token限制：8192

2. **GLM-4.1V-Thinking-Flash**
   - 类型：视觉推理模型
   - 用途：支持图像理解和推理
   - Token限制：8192

3. **GLM-4V-Flash**
   - 类型：图像理解模型
   - 用途：专门用于图像分析
   - Token限制：8192

4. **GLM-Z1-Flash**
   - 类型：推理模型
   - 用途：适合复杂逻辑推理
   - Token限制：8192

5. **Cogview-3-Flash**
   - 类型：图像生成模型
   - 用途：用于生成图像
   - Token限制：4096

6. **CogVideoX-Flash**
   - 类型：视频生成模型
   - 用途：用于生成视频
   - Token限制：4096

### API配置
- **Base URL**: `https://open.bigmodel.cn/api/paas/v4`
- **默认模型**: `GLM-4-Flash-250414`

## DeepSeek模型配置

### API调用格式
根据提供的API示例，DeepSeek使用标准的OpenAI兼容格式：

```bash
curl https://api.deepseek.com/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <DeepSeek API Key>" \
  -d '{
        "model": "deepseek-chat",
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "Hello!"}
        ],
        "stream": false
      }'
```

### 可用模型
1. **deepseek-chat** (默认)
   - 类型：主要对话模型
   - 用途：适合文本创作和对话
   - Token限制：32768

2. **deepseek-coder**
   - 类型：代码生成模型
   - 用途：专门用于编程
   - Token限制：16384

3. **deepseek-reasoner**
   - 类型：推理模型
   - 用途：适合复杂逻辑推理
   - Token限制：32768

### API配置
- **Base URL**: `https://api.deepseek.com`
- **默认模型**: `deepseek-chat`

## 代码更新内容

### 1. 模型配置文件更新
- `AIModelConfig.cs`: 更新了智谱AI和DeepSeek的预设模型列表
- 智谱AI默认模型改为 `GLM-4-Flash-250414`
- DeepSeek保持 `deepseek-chat` 作为默认模型

### 2. UI界面更新
- `AIModelConfigWindow.xaml`: 更新了模型选择下拉框
- 为每个模型添加了工具提示，说明模型类型和用途
- 智谱AI模型选项完全替换为新的Flash系列模型

### 3. 服务层更新
- `ZhipuAIService.cs`: 更新了模型初始化列表
- `DeepSeekService.cs`: 新创建的DeepSeek服务实现
- `AIServiceManager.cs`: 添加了DeepSeek服务的初始化

### 4. 默认配置更新
- `AIModelConfigService.cs`: 更新了默认配置中的模型名称

## 使用建议

### 文本创作推荐
- **智谱AI**: 使用 `GLM-4-Flash-250414` 进行一般文本创作
- **DeepSeek**: 使用 `deepseek-chat` 进行对话和创作

### 特殊用途
- **复杂推理**: 使用 `GLM-Z1-Flash` 或 `deepseek-reasoner`
- **图像相关**: 使用智谱AI的视觉模型系列
- **代码生成**: 使用 `deepseek-coder`

## 兼容性说明

- 旧的智谱AI模型名称（如 `glm-4`, `glm-4-plus` 等）已被移除
- 系统会自动使用新的默认模型
- 现有配置会在下次启动时自动更新到新的模型名称

## 测试验证

更新后的系统已通过以下测试：
- ✅ 模型配置界面显示正确的模型选项
- ✅ AI服务能够正确初始化各平台服务
- ✅ 模型切换功能正常工作
- ✅ API调用格式符合各平台要求

## 注意事项

1. **API Key配置**: 确保在使用前正确配置各平台的API Key
2. **网络连接**: 智谱AI和DeepSeek都需要网络连接
3. **Token限制**: 注意各模型的Token限制，避免超出限制
4. **模型选择**: 根据具体用途选择合适的模型以获得最佳效果

using System;
using System.Windows;
using System.Windows.Threading;

namespace DocumentCreationSystem
{
    /// <summary>
    /// SimpleMainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class SimpleMainWindow : Window
    {
        private DispatcherTimer? _timer;

        public SimpleMainWindow()
        {
            try
            {
                Console.WriteLine("正在初始化SimpleMainWindow...");
                InitializeComponent();
                
                // 初始化定时器
                _timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(1)
                };
                _timer.Tick += Timer_Tick;
                _timer.Start();

                // 设置系统信息
                UpdateSystemInfo();
                
                Console.WriteLine("SimpleMainWindow初始化完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SimpleMainWindow初始化失败: {ex.Message}");
                MessageBox.Show($"窗口初始化失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private void UpdateSystemInfo()
        {
            try
            {
                var info = $"操作系统: {Environment.OSVersion}\n";
                info += $".NET版本: {Environment.Version}\n";
                info += $"工作目录: {Environment.CurrentDirectory}\n";
                info += $"用户名: {Environment.UserName}";
                
                SystemInfoText.Text = info;
            }
            catch (Exception ex)
            {
                SystemInfoText.Text = $"获取系统信息失败: {ex.Message}";
            }
        }

        private void NewProject_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("新建项目功能正在开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void OpenProject_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("打开项目功能正在开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AIConfig_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("AI模型配置功能正在开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ProjectTools_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("项目工具功能正在开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutText = "文档管理及AI创作系统\n\n";
            aboutText += "版本: 1.0.0 (测试版)\n";
            aboutText += "开发框架: .NET 8.0 + WPF\n";
            aboutText += "功能: 文档管理、AI辅助创作、项目管理\n\n";
            aboutText += "这是一个简化的测试版本，用于验证GUI基本功能。";
            
            MessageBox.Show(aboutText, "关于系统", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}

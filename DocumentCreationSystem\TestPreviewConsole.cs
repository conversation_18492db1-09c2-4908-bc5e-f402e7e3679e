using System;
using DocumentCreationSystem.Tests;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 预览功能控制台测试程序
    /// </summary>
    public class TestPreviewConsole
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== AI输出预览功能测试程序 ===");
            Console.WriteLine("这是一个独立的测试程序，用于验证AI输出预览功能的核心逻辑");
            Console.WriteLine();
            
            // 运行预览功能测试
            PreviewFunctionalityTest.RunAllTests();
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

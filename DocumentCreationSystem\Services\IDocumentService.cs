using DocumentCreationSystem.Models;
using DocModel = DocumentCreationSystem.Models.Document;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 文档服务接口
/// </summary>
public interface IDocumentService
{
    /// <summary>
    /// 创建新文档
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="fileName">文件名</param>
    /// <param name="content">初始内容</param>
    /// <returns>创建的文档</returns>
    Task<DocModel> CreateDocumentAsync(int projectId, string fileName, string? content = null);

    /// <summary>
    /// 保存文档内容
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="content">文档内容</param>
    /// <returns>是否保存成功</returns>
    Task<bool> SaveDocumentAsync(int documentId, string content);

    /// <summary>
    /// 读取文档内容
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>文档内容</returns>
    Task<string> ReadDocumentAsync(int documentId);

    /// <summary>
    /// 获取文档信息
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>文档信息</returns>
    Task<DocModel?> GetDocumentAsync(int documentId);

    /// <summary>
    /// 获取项目下的所有文档
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <returns>文档列表</returns>
    Task<List<DocModel>> GetProjectDocumentsAsync(int projectId);

    /// <summary>
    /// 删除文档
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteDocumentAsync(int documentId);

    /// <summary>
    /// 更新文档元数据
    /// </summary>
    /// <param name="document">文档对象</param>
    /// <returns>是否更新成功</returns>
    Task<bool> UpdateDocumentAsync(DocModel document);

    /// <summary>
    /// 统计文档字数
    /// </summary>
    /// <param name="content">文档内容</param>
    /// <returns>字数</returns>
    int CountWords(string content);

    /// <summary>
    /// 检查文档是否已修改
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <returns>是否已修改</returns>
    Task<bool> IsDocumentModifiedAsync(int documentId);

    /// <summary>
    /// 获取文档的完整路径
    /// </summary>
    /// <param name="document">文档对象</param>
    /// <returns>完整路径</returns>
    string GetDocumentFullPath(DocModel document);

    /// <summary>
    /// 导入外部文档
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="filePath">外部文件路径</param>
    /// <returns>导入的文档</returns>
    Task<DocModel?> ImportDocumentAsync(int projectId, string filePath);

    /// <summary>
    /// 导出文档
    /// </summary>
    /// <param name="documentId">文档ID</param>
    /// <param name="exportPath">导出路径</param>
    /// <param name="format">导出格式</param>
    /// <returns>是否导出成功</returns>
    Task<bool> ExportDocumentAsync(int documentId, string exportPath, string format = "docx");
}

/// <summary>
/// 文档格式枚举
/// </summary>
public enum DocumentFormat
{
    Docx,
    Markdown,
    Text,
    Html
}

/// <summary>
/// 文档统计信息
/// </summary>
public class DocumentStatistics
{
    public int WordCount { get; set; }
    public int CharacterCount { get; set; }
    public int ParagraphCount { get; set; }
    public int PageCount { get; set; }
    public TimeSpan ReadingTime { get; set; }
}

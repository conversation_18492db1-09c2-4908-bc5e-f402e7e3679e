

namespace DocumentCreationSystem.Models;

/// <summary>
/// 文档实体类
/// </summary>
public class Document
{
    public int Id { get; set; }

    /// <summary>
    /// 所属项目ID
    /// </summary>
    public int ProjectId { get; set; }

    /// <summary>
    /// 文件名
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件相对路径（相对于项目根目录）
    /// </summary>
    public string RelativePath { get; set; } = string.Empty;

    /// <summary>
    /// 文件格式：docx, md, txt等
    /// </summary>
    public string Format { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 最后修改时间
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 文件最后修改时间（用于检测文件变更）
    /// </summary>
    public DateTime FileModifiedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 内容摘要
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 向量ID（在向量数据库中的标识）
    /// </summary>
    public string? VectorId { get; set; }

    /// <summary>
    /// 向量化状态
    /// </summary>
    public bool IsVectorized { get; set; } = false;

    /// <summary>
    /// 文档状态：Active-活跃，Deleted-已删除
    /// </summary>
    public string Status { get; set; } = "Active";

    /// <summary>
    /// 字数统计
    /// </summary>
    public int WordCount { get; set; } = 0;

    /// <summary>
    /// 文档标签（JSON数组格式）
    /// </summary>
    public string? Tags { get; set; }


}

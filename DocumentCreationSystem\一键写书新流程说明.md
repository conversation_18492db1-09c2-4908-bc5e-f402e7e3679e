# 一键写书新流程说明

## 概述

根据用户需求，一键写书功能已经重新设计，采用更加有序和连贯的创作流程。新流程确保每一步都基于前一步的结果，特别是时间线的连贯性和情节的逻辑发展。

## 新流程步骤

### 第一步：创建小说项目 (2%)
- 在数据库中创建小说项目记录
- 设置基本信息：标题、创作方向、章节数、每章字数等

### 第二步：生成世界设定 (5%)
- 基于创作方向生成详细的世界设定
- 包含17个方面的设定：世界观、修炼体系、政治体系等
- 保存到项目的Settings文件夹和数据库

### 第三步：根据世界设定生成全书大纲 (8%)
- **新增功能**：使用世界设定作为参考生成全书大纲
- 确保大纲与世界设定的一致性
- 按照指定的分卷数量进行规划

### 第四步：分卷创作流程 (10%-95%)
按卷顺序执行，每卷包含以下子步骤：

#### 4.1 生成分卷大纲
- **新增功能**：参考前一卷的时间线内容
- 确保各卷之间的情节连贯性
- 为当前卷设定明确的章节范围

#### 4.2 逐章创作
对于当前卷的每一章：

1. **生成章节细纲**
   - **新增功能**：参考前一章的时间线内容
   - 确保章节间的连贯性
   - 按照"场景→人物→主线/爽点/冲突点/悬念→结果"的结构

2. **生成章节正文**
   - 基于章节细纲创作
   - 参考前几章内容保持连贯性
   - 严格按照目标字数要求

3. **更新分卷时间线**
   - **新增功能**：实时更新当前卷的时间线
   - 记录章节的主要事件和角色变化
   - 为下一章的创作提供参考

#### 4.3 卷间衔接
- 完成当前卷的所有章节后，保存完整的分卷时间线
- 下一卷的分卷大纲生成时会参考这个时间线

## 关键改进

### 1. 时间线驱动的创作
- 每章创作完成后立即更新时间线
- 后续章节的细纲生成会参考前一章的时间线
- 分卷大纲生成会参考前一卷的完整时间线

### 2. 世界设定的深度整合
- 全书大纲基于世界设定生成，确保一致性
- 所有后续创作都遵循既定的世界设定

### 3. 分卷顺序执行
- 严格按卷顺序执行，确保逻辑连贯
- 每卷必须完成所有章节后才开始下一卷
- 避免了并行创作可能导致的不一致问题

### 4. 章节间的紧密衔接
- 每章细纲都参考前一章的时间线内容
- 确保情节发展的自然流畅
- 避免重复或矛盾的情节

## 文件保存结构

```
项目文件夹/
├── 设定/
│   ├── 世界设定.json              # 详细世界设定
│   ├── 时间线管理.md              # 总时间线文件
│   ├── 第1卷时间线.md             # 分卷时间线
│   ├── 第2卷时间线.md
│   └── ...
├── 大纲/
│   ├── 全书大纲.txt               # 基于世界设定的全书大纲
│   ├── 第1卷大纲.txt              # 分卷大纲
│   ├── 第2卷大纲.txt
│   ├── 分卷/                      # 分卷大纲子文件夹
│   │   ├── volume_01_outline.txt
│   │   └── volume_02_outline.txt
│   ├── 章节/                      # 章节细纲子文件夹
│   │   ├── 第1章细纲.txt
│   │   ├── 第2章细纲.txt
│   │   └── ...
│   └── ...
└── 章节/
    ├── 第1章正文.txt              # 章节正文
    ├── 第2章正文.txt
    └── ...
```

## 技术实现

### 新增方法

1. **GenerateOverallOutlineWithWorldSettingAsync**
   - 基于世界设定生成全书大纲
   - 确保大纲与世界设定的一致性

2. **GenerateVolumeOutlineWithTimelineAsync**
   - 基于前一卷时间线生成分卷大纲
   - 确保卷间情节的连贯性

3. **GenerateChapterOutlineWithTimelineAsync**
   - 基于前一章时间线生成章节细纲
   - 确保章节间的紧密衔接

4. **UpdateVolumeTimelineAsync**
   - 实时更新分卷时间线
   - 记录章节的关键信息

5. **SaveVolumeTimelineAsync**
   - 保存分卷时间线到文件
   - 同时更新总时间线文件

### 流程控制

- 使用严格的顺序执行，避免并发问题
- 每个步骤都有明确的依赖关系
- 支持中断和恢复功能

## 用户体验改进

1. **进度显示更精确**：按卷和章节显示详细进度
2. **实时预览**：可以看到正在处理的文件和内容
3. **错误恢复**：支持从中断点继续执行
4. **文件自动保存**：每个步骤完成后立即保存

## 注意事项

1. **执行顺序不可变更**：必须严格按照设计的顺序执行
2. **时间线的重要性**：时间线是连贯性的关键，不可忽略
3. **世界设定的一致性**：所有创作都必须遵循世界设定
4. **文件保存的及时性**：每步完成后立即保存，避免数据丢失

这个新流程确保了小说创作的逻辑连贯性和情节一致性，为用户提供了更高质量的自动化写作体验。

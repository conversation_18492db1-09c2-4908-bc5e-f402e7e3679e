using DocumentCreationSystem.Models;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Services.MCP;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace DocumentCreationSystem.Views
{
    /// <summary>
    /// Agent配置窗口
    /// </summary>
    public partial class AgentConfigWindow : Window, INotifyPropertyChanged
    {
        private readonly ILogger<AgentConfigWindow> _logger;
        private readonly IProjectToolsService _toolsService;
        private readonly IToolExecutionMonitor _toolExecutionMonitor;
        private readonly IAIService _aiService;
        private readonly ObservableCollection<ToolConfigViewModel> _tools = new();
        private AgentConfiguration _currentConfig = new();

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ToolConfigViewModel> Tools => _tools;

        public AgentConfigWindow(
            ILogger<AgentConfigWindow> logger,
            IProjectToolsService toolsService,
            IToolExecutionMonitor toolExecutionMonitor,
            IAIService aiService)
        {
            InitializeComponent();
            _logger = logger;
            _toolsService = toolsService;
            _toolExecutionMonitor = toolExecutionMonitor;
            _aiService = aiService;

            DataContext = this;
            ToolsDataGrid.ItemsSource = _tools;

            Loaded += OnLoaded;
            Closed += OnClosed;

            // 订阅配置更新事件
            ConfigurationNotificationService.AIModelConfigurationUpdated += OnAIModelConfigurationUpdated;
        }

        private async void OnLoaded(object sender, RoutedEventArgs e)
        {
            await LoadConfigurationAsync();
            await LoadAvailableModelsAsync();
            await LoadToolsAsync();
            await LoadStatisticsAsync();
        }

        private async Task LoadConfigurationAsync()
        {
            try
            {
                StatusTextBlock.Text = "正在加载配置...";

                // 加载默认配置
                _currentConfig = new AgentConfiguration
                {
                    Name = "DocumentCreationAgent",
                    Description = "文档创建专用AI助手",
                    Type = "文档创建专家",
                    ConfidenceThreshold = 0.7,
                    ModelProvider = "OpenAI",
                    ModelName = "gpt-4",
                    Temperature = 0.7,
                    EnableMemory = true,
                    MemoryRetentionDays = 30,
                    MaxMemoryItems = 10000,
                    AutoConsolidate = true,
                    EnableLearning = true,
                    LearningRate = 0.1,
                    LearnFromFailures = true,
                    MaxConcurrentTools = 3,
                    ToolTimeoutSeconds = 30
                };

                ApplyConfigurationToUI();
                StatusTextBlock.Text = "配置加载完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置失败");
                StatusTextBlock.Text = "加载配置失败";
                MessageBox.Show($"加载配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyConfigurationToUI()
        {
            AgentNameTextBox.Text = _currentConfig.Name;
            AgentDescriptionTextBox.Text = _currentConfig.Description;
            AgentTypeComboBox.Text = _currentConfig.Type;
            ConfidenceThresholdSlider.Value = _currentConfig.ConfidenceThreshold;

            // 设置模型提供商选择
            if (!string.IsNullOrEmpty(_currentConfig.ModelProvider))
            {
                var providerItem = ModelProviderComboBox.Items.Cast<ComboBoxItem>()
                    .FirstOrDefault(i => i.Content.ToString() == _currentConfig.ModelProvider);
                if (providerItem != null)
                {
                    ModelProviderComboBox.SelectedItem = providerItem;
                }
                else
                {
                    ModelProviderComboBox.Text = _currentConfig.ModelProvider;
                }
            }

            // 设置模型名称选择
            if (!string.IsNullOrEmpty(_currentConfig.ModelName))
            {
                var modelItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                    .FirstOrDefault(i => i.Tag?.ToString() == _currentConfig.ModelName || i.Content.ToString() == _currentConfig.ModelName);
                if (modelItem != null)
                {
                    ModelNameComboBox.SelectedItem = modelItem;
                }
                else
                {
                    ModelNameComboBox.Text = _currentConfig.ModelName;
                }
            }

            TemperatureSlider.Value = _currentConfig.Temperature;
            // 这些控件在XAML中未定义，暂时注释掉
            // EnableMemoryCheckBox.IsChecked = _currentConfig.EnableMemory;
            // MemoryRetentionSlider.Value = _currentConfig.MemoryRetentionDays;
            // MaxMemoryItemsTextBox.Text = _currentConfig.MaxMemoryItems.ToString();
            // AutoConsolidateCheckBox.IsChecked = _currentConfig.AutoConsolidate;
            // EnableLearningCheckBox.IsChecked = _currentConfig.EnableLearning;
            // LearningRateSlider.Value = _currentConfig.LearningRate;
            // LearnFromFailuresCheckBox.IsChecked = _currentConfig.LearnFromFailures;
            MaxConcurrentToolsTextBox.Text = _currentConfig.MaxConcurrentTools.ToString();
            ToolTimeoutTextBox.Text = _currentConfig.ToolTimeoutSeconds.ToString();
        }

        /// <summary>
        /// 加载可用的AI模型
        /// </summary>
        private async Task LoadAvailableModelsAsync()
        {
            try
            {
                StatusTextBlock.Text = "正在加载AI模型...";

                // 清空现有选项
                ModelProviderComboBox.Items.Clear();
                ModelNameComboBox.Items.Clear();

                // 加载AI模型配置
                var configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DocumentCreationSystem", "ai-model-config.json");

                if (File.Exists(configPath))
                {
                    var json = await File.ReadAllTextAsync(configPath);
                    var config = System.Text.Json.JsonSerializer.Deserialize<AIModelConfig>(json);

                    if (config != null)
                    {
                        // 添加可用的模型提供商
                        var providers = new List<string>();

                        if (!string.IsNullOrEmpty(config.OllamaConfig?.SelectedModel))
                            providers.Add("Ollama");
                        if (!string.IsNullOrEmpty(config.LMStudioConfig?.SelectedModel))
                            providers.Add("LMStudio");
                        if (!string.IsNullOrEmpty(config.ZhipuAIConfig?.ApiKey))
                            providers.Add("ZhipuAI");
                        if (!string.IsNullOrEmpty(config.DeepSeekConfig?.ApiKey))
                            providers.Add("DeepSeek");
                        if (!string.IsNullOrEmpty(config.OpenAIConfig?.ApiKey))
                            providers.Add("OpenAI");
                        if (!string.IsNullOrEmpty(config.AlibabaConfig?.ApiKey))
                            providers.Add("Alibaba");
                        if (!string.IsNullOrEmpty(config.RWKVConfig?.SelectedModel))
                            providers.Add("RWKV");

                        foreach (var provider in providers)
                        {
                            ModelProviderComboBox.Items.Add(new ComboBoxItem { Content = provider });
                        }

                        // 设置当前平台为默认选择
                        if (!string.IsNullOrEmpty(config.Platform) && providers.Contains(config.Platform))
                        {
                            var item = ModelProviderComboBox.Items.Cast<ComboBoxItem>()
                                .FirstOrDefault(i => i.Content.ToString() == config.Platform);
                            if (item != null)
                            {
                                ModelProviderComboBox.SelectedItem = item;
                                await LoadModelsForProvider(config.Platform, config);
                            }
                        }
                    }
                }

                // 如果没有配置文件或配置为空，添加默认选项
                if (ModelProviderComboBox.Items.Count == 0)
                {
                    var defaultProviders = new[] { "OpenAI", "ZhipuAI", "DeepSeek", "Ollama", "LMStudio", "Alibaba", "RWKV" };
                    foreach (var provider in defaultProviders)
                    {
                        ModelProviderComboBox.Items.Add(new ComboBoxItem { Content = provider });
                    }
                }

                // 添加提供商选择变化事件
                ModelProviderComboBox.SelectionChanged += async (s, e) =>
                {
                    if (ModelProviderComboBox.SelectedItem is ComboBoxItem selectedItem)
                    {
                        var provider = selectedItem.Content.ToString();
                        await LoadModelsForProviderFromConfig(provider);
                    }
                };

                StatusTextBlock.Text = "AI模型加载完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载AI模型失败");
                StatusTextBlock.Text = "加载AI模型失败";

                // 添加默认选项作为备用
                if (ModelProviderComboBox.Items.Count == 0)
                {
                    var defaultProviders = new[] { "OpenAI", "ZhipuAI", "DeepSeek", "Ollama", "LMStudio", "Alibaba", "RWKV" };
                    foreach (var provider in defaultProviders)
                    {
                        ModelProviderComboBox.Items.Add(new ComboBoxItem { Content = provider });
                    }
                }
            }
        }

        /// <summary>
        /// 为指定提供商加载模型列表
        /// </summary>
        private async Task LoadModelsForProvider(string provider, AIModelConfig config)
        {
            try
            {
                ModelNameComboBox.Items.Clear();

                switch (provider)
                {
                    case "Ollama":
                        if (config.OllamaConfig?.AvailableModels != null)
                        {
                            foreach (var model in config.OllamaConfig.AvailableModels)
                            {
                                ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model.Name, Tag = model.Id });
                            }
                            if (!string.IsNullOrEmpty(config.OllamaConfig.SelectedModel))
                            {
                                var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                    .FirstOrDefault(i => i.Tag?.ToString() == config.OllamaConfig.SelectedModel);
                                if (selectedItem != null)
                                    ModelNameComboBox.SelectedItem = selectedItem;
                            }
                        }
                        break;

                    case "LMStudio":
                        if (config.LMStudioConfig?.AvailableModels != null)
                        {
                            foreach (var model in config.LMStudioConfig.AvailableModels)
                            {
                                ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model.Name, Tag = model.Id });
                            }
                            if (!string.IsNullOrEmpty(config.LMStudioConfig.SelectedModel))
                            {
                                var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                    .FirstOrDefault(i => i.Tag?.ToString() == config.LMStudioConfig.SelectedModel);
                                if (selectedItem != null)
                                    ModelNameComboBox.SelectedItem = selectedItem;
                            }
                        }
                        break;

                    case "ZhipuAI":
                        foreach (var model in ZhipuAIConfig.PresetModels)
                        {
                            ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model, Tag = model });
                        }
                        if (!string.IsNullOrEmpty(config.ZhipuAIConfig?.Model))
                        {
                            var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                .FirstOrDefault(i => i.Tag?.ToString() == config.ZhipuAIConfig.Model);
                            if (selectedItem != null)
                                ModelNameComboBox.SelectedItem = selectedItem;
                        }
                        break;

                    case "DeepSeek":
                        var deepSeekModels = new[] { "deepseek-chat", "deepseek-coder", "deepseek-reasoner" };
                        foreach (var model in deepSeekModels)
                        {
                            ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model, Tag = model });
                        }
                        if (!string.IsNullOrEmpty(config.DeepSeekConfig?.Model))
                        {
                            var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                .FirstOrDefault(i => i.Tag?.ToString() == config.DeepSeekConfig.Model);
                            if (selectedItem != null)
                                ModelNameComboBox.SelectedItem = selectedItem;
                        }
                        break;

                    case "OpenAI":
                        var openAIModels = new[] { "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o", "gpt-4o-mini" };
                        foreach (var model in openAIModels)
                        {
                            ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model, Tag = model });
                        }
                        if (!string.IsNullOrEmpty(config.OpenAIConfig?.Model))
                        {
                            var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                .FirstOrDefault(i => i.Tag?.ToString() == config.OpenAIConfig.Model);
                            if (selectedItem != null)
                                ModelNameComboBox.SelectedItem = selectedItem;
                        }
                        break;

                    case "Alibaba":
                        var alibabaModels = new[] { "qwen-turbo", "qwen-plus", "qwen-max", "qwen-max-longcontext", "qwen-vl-plus", "qwen-vl-max" };
                        foreach (var model in alibabaModels)
                        {
                            ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model, Tag = model });
                        }
                        if (!string.IsNullOrEmpty(config.AlibabaConfig?.Model))
                        {
                            var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                .FirstOrDefault(i => i.Tag?.ToString() == config.AlibabaConfig.Model);
                            if (selectedItem != null)
                                ModelNameComboBox.SelectedItem = selectedItem;
                        }
                        break;

                    case "RWKV":
                        if (config.RWKVConfig?.AvailableModels != null)
                        {
                            foreach (var model in config.RWKVConfig.AvailableModels)
                            {
                                ModelNameComboBox.Items.Add(new ComboBoxItem { Content = model, Tag = model });
                            }
                            if (!string.IsNullOrEmpty(config.RWKVConfig.SelectedModel))
                            {
                                var selectedItem = ModelNameComboBox.Items.Cast<ComboBoxItem>()
                                    .FirstOrDefault(i => i.Tag?.ToString() == config.RWKVConfig.SelectedModel);
                                if (selectedItem != null)
                                    ModelNameComboBox.SelectedItem = selectedItem;
                            }
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载 {provider} 模型失败");
            }
        }

        /// <summary>
        /// 从配置文件加载指定提供商的模型
        /// </summary>
        private async Task LoadModelsForProviderFromConfig(string provider)
        {
            try
            {
                var configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "DocumentCreationSystem", "ai-model-config.json");

                if (File.Exists(configPath))
                {
                    var json = await File.ReadAllTextAsync(configPath);
                    var config = System.Text.Json.JsonSerializer.Deserialize<AIModelConfig>(json);

                    if (config != null)
                    {
                        await LoadModelsForProvider(provider, config);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"从配置文件加载 {provider} 模型失败");
            }
        }

        private async Task LoadToolsAsync()
        {
            try
            {
                StatusTextBlock.Text = "正在加载工具列表...";
                _tools.Clear();

                // 加载内置工具
                var builtInTools = await _toolsService.GetAvailableToolsAsync("All");
                foreach (var tool in builtInTools)
                {
                    _tools.Add(new ToolConfigViewModel
                    {
                        Name = tool.Name,
                        Category = tool.Category,
                        Description = tool.Description,
                        Version = "1.0.0",
                        IsEnabled = tool.IsEnabled,
                        IsBuiltIn = tool.IsBuiltIn
                    });
                }

                // MCP工具功能正在开发中

                StatusTextBlock.Text = $"已加载 {_tools.Count} 个工具";
                _logger.LogInformation($"已加载 {_tools.Count} 个工具");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载工具列表失败");
                StatusTextBlock.Text = "加载工具列表失败";
                MessageBox.Show($"加载工具列表失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                // 加载工具执行统计
                var allStats = _toolExecutionMonitor.GetAllStatistics();
                var totalCalls = allStats.Values.Sum(s => s.TotalExecutions);
                var successfulCalls = allStats.Values.Sum(s => s.SuccessfulExecutions);
                var avgResponseTime = allStats.Values.Any() 
                    ? allStats.Values.Average(s => s.AverageExecutionTime) 
                    : 0;

                // 这些控件在XAML中未定义，暂时注释掉
                // TotalCallsTextBlock.Text = totalCalls.ToString();
                // SuccessRateTextBlock.Text = totalCalls > 0 ? $"{(double)successfulCalls / totalCalls:P0}" : "0%";
                // AvgResponseTimeTextBlock.Text = $"{avgResponseTime:F0}ms";

                // MCP工具统计功能正在开发中
                
                // 模拟记忆统计（实际应该从记忆服务获取）
                // TotalMemoriesTextBlock.Text = "1,234";
                // AvgMemoryStrengthTextBlock.Text = "75%";
                // TotalAssociationsTextBlock.Text = "456";

                _logger.LogInformation("统计信息加载完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载统计信息失败");
            }
        }

        private async void SaveConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                StatusTextBlock.Text = "正在保存配置...";

                // 从UI收集配置
                _currentConfig.Name = AgentNameTextBox.Text;
                _currentConfig.Description = AgentDescriptionTextBox.Text;
                _currentConfig.Type = AgentTypeComboBox.Text;
                _currentConfig.ConfidenceThreshold = ConfidenceThresholdSlider.Value;

                // 获取选中的模型提供商
                if (ModelProviderComboBox.SelectedItem is ComboBoxItem providerItem)
                {
                    _currentConfig.ModelProvider = providerItem.Content.ToString();
                }
                else
                {
                    _currentConfig.ModelProvider = ModelProviderComboBox.Text;
                }

                // 获取选中的模型名称
                if (ModelNameComboBox.SelectedItem is ComboBoxItem modelItem)
                {
                    _currentConfig.ModelName = modelItem.Tag?.ToString() ?? modelItem.Content.ToString();
                }
                else
                {
                    _currentConfig.ModelName = ModelNameComboBox.Text;
                }

                _currentConfig.Temperature = TemperatureSlider.Value;
                // 这些控件在XAML中未定义，暂时注释掉
                // _currentConfig.EnableMemory = EnableMemoryCheckBox.IsChecked == true;
                // _currentConfig.MemoryRetentionDays = (int)MemoryRetentionSlider.Value;

                // if (int.TryParse(MaxMemoryItemsTextBox.Text, out var maxMemoryItems))
                //     _currentConfig.MaxMemoryItems = maxMemoryItems;

                // _currentConfig.AutoConsolidate = AutoConsolidateCheckBox.IsChecked == true;
                // _currentConfig.EnableLearning = EnableLearningCheckBox.IsChecked == true;
                // _currentConfig.LearningRate = LearningRateSlider.Value;
                // _currentConfig.LearnFromFailures = LearnFromFailuresCheckBox.IsChecked == true;
                
                if (int.TryParse(MaxConcurrentToolsTextBox.Text, out var maxConcurrentTools))
                    _currentConfig.MaxConcurrentTools = maxConcurrentTools;
                
                if (int.TryParse(ToolTimeoutTextBox.Text, out var toolTimeout))
                    _currentConfig.ToolTimeoutSeconds = toolTimeout;

                // 保存工具配置
                foreach (var tool in _tools)
                {
                    // 这里应该保存工具的启用状态
                    _logger.LogDebug($"工具 {tool.Name} 启用状态: {tool.IsEnabled}");
                }

                // 实际保存配置到文件或数据库
                await SaveConfigurationToFileAsync(_currentConfig);

                StatusTextBlock.Text = "配置保存成功";
                MessageBox.Show("配置保存成功", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
                _logger.LogInformation("Agent配置保存成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置失败");
                StatusTextBlock.Text = "保存配置失败";
                MessageBox.Show($"保存配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SaveConfigurationToFileAsync(AgentConfiguration config)
        {
            var configPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                "DocumentCreationSystem", "agent-config.json");
            
            var directory = Path.GetDirectoryName(configPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            var json = System.Text.Json.JsonSerializer.Serialize(config, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(configPath, json);
        }

        private async void MCPConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("MCP配置功能正在开发中...", "信息", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开MCP配置窗口失败");
                MessageBox.Show($"打开MCP配置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void RefreshTools_Click(object sender, RoutedEventArgs e)
        {
            await LoadToolsAsync();
        }

        private async void RefreshStats_Click(object sender, RoutedEventArgs e)
        {
            await LoadStatisticsAsync();
        }

        private void ConfigureTool_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ToolConfigViewModel tool)
            {
                MessageBox.Show($"配置工具: {tool.Name}\n\n此功能正在开发中...", "工具配置", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void ResetConfig_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要重置所有配置到默认值吗？", "确认重置", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                await LoadConfigurationAsync();
                StatusTextBlock.Text = "配置已重置";
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 处理AI模型配置更新事件
        /// </summary>
        private async void OnAIModelConfigurationUpdated(object? sender, EventArgs e)
        {
            try
            {
                // 在UI线程中刷新模型列表
                await Dispatcher.InvokeAsync(async () =>
                {
                    await LoadAvailableModelsAsync();
                    _logger.LogInformation("Agent配置窗口已响应AI模型配置更新");
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "响应AI模型配置更新时发生错误");
            }
        }

        /// <summary>
        /// 窗口关闭时的清理
        /// </summary>
        private void OnClosed(object? sender, EventArgs e)
        {
            try
            {
                // 取消订阅事件
                ConfigurationNotificationService.AIModelConfigurationUpdated -= OnAIModelConfigurationUpdated;
                _logger.LogInformation("Agent配置窗口已清理事件订阅");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理Agent配置窗口事件订阅时发生错误");
            }
        }
    }

    /// <summary>
    /// 工具配置视图模型
    /// </summary>
    public class ToolConfigViewModel : INotifyPropertyChanged
    {
        private bool _isEnabled;

        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public bool IsBuiltIn { get; set; }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                _isEnabled = value;
                OnPropertyChanged(nameof(IsEnabled));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// Agent配置
    /// </summary>
    public class AgentConfiguration
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public double ConfidenceThreshold { get; set; } = 0.7;
        public string ModelProvider { get; set; } = string.Empty;
        public string ModelName { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public double Temperature { get; set; } = 0.7;
        public bool EnableMemory { get; set; } = true;
        public int MemoryRetentionDays { get; set; } = 30;
        public int MaxMemoryItems { get; set; } = 10000;
        public bool AutoConsolidate { get; set; } = true;
        public bool EnableLearning { get; set; } = true;
        public double LearningRate { get; set; } = 0.1;
        public bool LearnFromFailures { get; set; } = true;
        public int MaxConcurrentTools { get; set; } = 3;
        public int ToolTimeoutSeconds { get; set; } = 30;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}

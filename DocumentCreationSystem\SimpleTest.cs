using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 简单测试AI助手高级检索工具功能
    /// </summary>
    public class SimpleTest
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("🚀 开始测试AI助手高级检索工具功能...\n");

            try
            {
                // 创建日志记录器
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Warning));
                var agentLogger = loggerFactory.CreateLogger<AgentToolService>();
                
                // 创建AgentToolService实例
                var agentToolService = new AgentToolService(agentLogger);

                // 设置测试项目
                var testProject = new Project
                {
                    Id = 999,
                    Name = "测试项目",
                    RootPath = Path.Combine(Directory.GetCurrentDirectory(), "Projects"),
                    Type = "Novel"
                };
                agentToolService.SetCurrentProject(testProject);

                Console.WriteLine("📁 测试基础文件操作...");
                var result1 = await agentToolService.ExecuteToolAsync("list_files", "");
                Console.WriteLine(result1);
                Console.WriteLine(new string('-', 50));

                Console.WriteLine("📁 测试项目结构扫描...");
                var result2 = await agentToolService.ExecuteToolAsync("scan_project_structure", "");
                Console.WriteLine(result2);
                Console.WriteLine(new string('-', 50));

                Console.WriteLine("🔗 测试文件关系分析...");
                var result3 = await agentToolService.ExecuteToolAsync("analyze_file_relationships", "");
                Console.WriteLine(result3);
                Console.WriteLine(new string('-', 50));

                Console.WriteLine("🔍 测试深度内容搜索...");
                var result4 = await agentToolService.ExecuteToolAsync("deep_content_search", "时间");
                Console.WriteLine(result4);
                Console.WriteLine(new string('-', 50));

                Console.WriteLine("📊 测试项目概览...");
                var result5 = await agentToolService.ExecuteToolAsync("get_project_overview", "");
                Console.WriteLine(result5);
                Console.WriteLine(new string('-', 50));

                Console.WriteLine("✅ 所有测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}

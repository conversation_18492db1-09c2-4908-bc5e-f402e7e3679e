<Window x:Class="DocumentCreationSystem.Views.WritingWorkflowConfigDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="写书流程设定" Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" Padding="16" materialDesign:ElevationAssist.Elevation="Dp4"
                                  Mode="PrimaryMid">
            <DockPanel>
                <materialDesign:PackIcon Kind="WorkflowOutline" Height="24" Width="24"
                                       VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBlock Text="写书流程设定管理" VerticalAlignment="Center"
                         FontSize="18" FontWeight="Medium"/>
                
                <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="NewConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Plus" Margin="0,0,4,0"/>
                                <TextBlock Text="新建配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ApplyConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Check" Margin="0,0,4,0"/>
                                <TextBlock Text="应用配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Margin="0,0,8,0"
                          Click="ImportConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Import" Margin="0,0,4,0"/>
                                <TextBlock Text="导入配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                          Click="ExportConfig_Click">
                        <Button.Content>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Export" Margin="0,0,4,0"/>
                                <TextBlock Text="导出配置"/>
                            </StackPanel>
                        </Button.Content>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧配置列表 -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="配置列表" FontWeight="Medium" 
                             Margin="16,16,16,8" FontSize="14"/>

                    <ListBox x:Name="ConfigListBox" Grid.Row="1" 
                           SelectionChanged="ConfigListBox_SelectionChanged"
                           Margin="8">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <Grid Margin="8">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <StackPanel Grid.Row="0" Orientation="Horizontal">
                                        <TextBlock Text="{Binding Name}" FontWeight="Medium"/>
                                        <materialDesign:PackIcon Kind="Star" Width="16" Height="16" 
                                                               Margin="4,0,0,0" Foreground="Gold"
                                                               Visibility="{Binding IsDefault, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                    </StackPanel>
                                    <TextBlock Grid.Row="1" Text="{Binding Description}" 
                                             FontSize="12" Opacity="0.7" TextWrapping="Wrap"/>
                                </Grid>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" 
                              HorizontalAlignment="Center" Margin="8">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="设为默认" Click="SetDefault_Click">
                            <materialDesign:PackIcon Kind="Star"/>
                        </Button>
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="删除配置" Click="DeleteConfig_Click">
                            <materialDesign:PackIcon Kind="Delete"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- 右侧配置编辑区域 -->
            <materialDesign:Card Grid.Column="1" Margin="8,0,0,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="16">
                        <!-- 基本信息 -->
                        <TextBlock Text="基本信息" FontWeight="Medium" FontSize="16" Margin="0,0,0,16"/>
                        
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBox x:Name="ConfigNameTextBox" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="配置名称"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,8,0"/>
                            
                            <CheckBox x:Name="IsDefaultCheckBox" Grid.Column="1"
                                    Content="设为默认配置" Margin="8,0,0,0"
                                    VerticalAlignment="Center"/>
                        </Grid>

                        <TextBox x:Name="ConfigDescriptionTextBox"
                               materialDesign:HintAssist.Hint="配置描述"
                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,0,24"/>

                        <!-- 流程配置选项卡 -->
                        <TabControl x:Name="WorkflowTabControl" Height="400">
                            <!-- 基础流程设定 -->
                            <TabItem Header="基础流程">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <StackPanel Margin="16">
                                        <TextBlock Text="启用的生成步骤" FontWeight="Medium" Margin="0,0,0,16"/>
                                        
                                        <CheckBox x:Name="EnableOverallOutlineCheckBox" Content="全书大纲生成" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableWorldSettingCheckBox" Content="世界设定生成" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableVolumeOutlineCheckBox" Content="卷宗大纲生成" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableChapterOutlineCheckBox" Content="章节细纲生成" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableChapterContentCheckBox" Content="章节正文生成" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableTimelineUpdateCheckBox" Content="时间线更新" Margin="0,0,0,16"/>
                                        
                                        <TextBlock Text="生成模式" FontWeight="Medium" Margin="0,0,0,16"/>
                                        <RadioButton x:Name="SequentialModeRadio" Content="顺序生成模式（推荐）" 
                                                   GroupName="GenerationMode" IsChecked="True" Margin="0,0,0,8"/>
                                        <RadioButton x:Name="BatchModeRadio" Content="批量生成模式" 
                                                   GroupName="GenerationMode" Margin="0,0,0,8"/>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                            
                            <!-- 生成参数 -->
                            <TabItem Header="生成参数">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <StackPanel Margin="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBlock Text="Token数设定" FontWeight="Medium" Margin="0,0,0,8"/>
                                                <TextBox x:Name="OverallOutlineMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="全书大纲最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="WorldSettingMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="世界设定最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="VolumeOutlineMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="卷宗大纲最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="ChapterOutlineMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="章节细纲最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="ChapterContentMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="章节正文最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="TimelineUpdateMaxTokensTextBox" 
                                                       materialDesign:HintAssist.Hint="时间线更新最大Token数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBlock Text="Temperature设定" FontWeight="Medium" Margin="0,0,0,8"/>
                                                <TextBox x:Name="OverallOutlineTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="全书大纲Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="WorldSettingTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="世界设定Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="VolumeOutlineTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="卷宗大纲Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="ChapterOutlineTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="章节细纲Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="ChapterContentTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="章节正文Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="TimelineUpdateTemperatureTextBox" 
                                                       materialDesign:HintAssist.Hint="时间线更新Temperature"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                            
                            <!-- 流程控制 -->
                            <TabItem Header="流程控制">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <StackPanel Margin="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <TextBox x:Name="RetryCountTextBox" 
                                                       materialDesign:HintAssist.Hint="失败重试次数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="GenerationIntervalTextBox" 
                                                       materialDesign:HintAssist.Hint="生成间隔时间(毫秒)"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <CheckBox x:Name="EnableAutoSaveCheckBox" Content="启用自动保存" Margin="0,0,0,8"/>
                                                <TextBox x:Name="AutoSaveIntervalTextBox" 
                                                       materialDesign:HintAssist.Hint="自动保存间隔(分钟)"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <TextBox x:Name="PreviousChaptersForOutlineTextBox" 
                                                       materialDesign:HintAssist.Hint="细纲参考前文章节数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="PreviousChaptersForContentTextBox" 
                                                       materialDesign:HintAssist.Hint="正文参考前文章节数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="MaxWorldSettingFilesTextBox" 
                                                       materialDesign:HintAssist.Hint="最大世界设定文件数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                                <TextBox x:Name="MaxWorldSettingFileLengthTextBox" 
                                                       materialDesign:HintAssist.Hint="单个设定文件最大字符数"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                            
                            <!-- 文件输出 -->
                            <TabItem Header="文件输出">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <StackPanel Margin="16">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0" Margin="0,0,8,0">
                                                <ComboBox x:Name="OutputFileFormatComboBox" 
                                                        materialDesign:HintAssist.Hint="输出文件格式"
                                                        Margin="0,0,0,8">
                                                    <ComboBoxItem Content="txt"/>
                                                    <ComboBoxItem Content="docx"/>
                                                </ComboBox>
                                                <CheckBox x:Name="EnableAutoFileNameCheckBox" Content="启用文件名自动生成" Margin="0,0,0,8"/>
                                                <TextBox x:Name="FileNameTemplateTextBox" 
                                                       materialDesign:HintAssist.Hint="文件名模板"
                                                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                                       Margin="0,0,0,8"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1" Margin="8,0,0,0">
                                                <CheckBox x:Name="EnableAutoChapterTitleCheckBox" Content="启用章节标题自动生成" Margin="0,0,0,8"/>
                                                <CheckBox x:Name="IncludeChapterNumberInFileNameCheckBox" Content="文件名包含章节号" Margin="0,0,0,8"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                            
                            <!-- 质量控制 -->
                            <TabItem Header="质量控制">
                                <ScrollViewer VerticalScrollBarVisibility="Auto">
                                    <StackPanel Margin="16">
                                        <CheckBox x:Name="EnableConsistencyCheckCheckBox" Content="启用内容一致性检查" Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableDuplicationCheckCheckBox" Content="启用重复内容检测" Margin="0,0,0,8"/>
                                        <TextBox x:Name="DuplicationThresholdTextBox" 
                                               materialDesign:HintAssist.Hint="重复内容检测阈值(0-1)"
                                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                               Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableWordCountControlCheckBox" Content="启用字数控制" Margin="0,0,0,8"/>
                                        <TextBox x:Name="WordCountToleranceTextBox" 
                                               materialDesign:HintAssist.Hint="字数控制容差范围(百分比)"
                                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                               Margin="0,0,0,8"/>
                                        <CheckBox x:Name="EnableCrossVolumeTimelineReferenceCheckBox" Content="启用跨卷时间线参考" Margin="0,0,0,8"/>
                                        <TextBox x:Name="MaxCrossVolumeTimelineLengthTextBox" 
                                               materialDesign:HintAssist.Hint="跨卷时间线最大字符数"
                                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                               Margin="0,0,0,8"/>
                                    </StackPanel>
                                </ScrollViewer>
                            </TabItem>
                        </TabControl>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>
        </Grid>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                  HorizontalAlignment="Right" Margin="16">
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                  Margin="0,0,8,0"
                  Click="Save_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ContentSave" Margin="0,0,4,0"/>
                        <TextBlock Text="保存"/>
                    </StackPanel>
                </Button.Content>
            </Button>
            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                  Click="Cancel_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cancel" Margin="0,0,4,0"/>
                        <TextBlock Text="取消"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>
    </Grid>
</Window>

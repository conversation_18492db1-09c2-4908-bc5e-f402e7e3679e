MIT License

Copyright (c) 2025 文档管理及AI创作系统

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## 第三方组件许可证声明

本项目使用了以下开源组件，各组件遵循其相应的开源许可证：

### .NET 相关
- Microsoft.Extensions.Hosting (MIT License)
- Microsoft.Extensions.DependencyInjection (MIT License)
- Microsoft.Extensions.Logging (MIT License)
- Microsoft.Extensions.Configuration (MIT License)

### UI 框架
- MaterialDesignThemes (MIT License)
- MaterialDesignColors (MIT License)
- AvalonEdit (MIT License)

### 文档处理
- DocumentFormat.OpenXml (MIT License)

### JSON 处理
- Newtonsoft.Json (MIT License)

### HTTP 客户端
- System.Net.Http (MIT License)

### 数据库
- Microsoft.EntityFrameworkCore (MIT License)
- Microsoft.EntityFrameworkCore.Sqlite (MIT License)

### 测试框架
- Microsoft.NET.Test.Sdk (MIT License)
- xunit (Apache License 2.0)
- xunit.runner.visualstudio (MIT License)

## 免责声明

1. **AI 服务集成**：本软件集成了多种AI服务（智谱AI、DeepSeek、LM Studio、Ollama等），
   用户在使用这些服务时需要遵守相应服务提供商的使用条款和隐私政策。

2. **内容生成**：本软件使用AI技术生成内容，生成的内容可能存在不准确、不完整或不适当的情况，
   用户应当对生成的内容进行审查和验证。

3. **数据安全**：用户应当妥善保管自己的API密钥和敏感数据，开发者不对因用户不当使用
   导致的数据泄露或损失承担责任。

4. **法律合规**：用户在使用本软件时应当遵守当地法律法规，不得用于非法用途。

## 贡献指南

欢迎社区贡献代码和建议！在贡献之前，请：

1. 阅读项目的代码规范和贡献指南
2. 确保您的贡献不侵犯第三方知识产权
3. 遵循项目的开源许可证要求
4. 提交Pull Request前请进行充分测试

## 联系方式

如有任何问题或建议，请通过以下方式联系：

- 项目主页：https://github.com/your-username/document-creation-system
- 问题反馈：https://github.com/your-username/document-creation-system/issues
- 邮箱：<EMAIL>

感谢您使用文档管理及AI创作系统！

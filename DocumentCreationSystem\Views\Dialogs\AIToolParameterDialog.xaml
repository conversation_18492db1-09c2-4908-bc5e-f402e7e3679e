<Window x:Class="DocumentCreationSystem.Views.Dialogs.AIToolParameterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="工具参数设置" 
        Height="500" 
        Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <Style x:Key="ParameterLabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="ParameterInputStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Border Grid.Row="0" Background="#2196F3" Padding="15,10">
            <StackPanel>
                <TextBlock x:Name="ToolNameText" 
                           Text="工具参数设置" 
                           FontSize="16" 
                           FontWeight="Bold" 
                           Foreground="White"/>
                <TextBlock x:Name="ToolDescriptionText" 
                           Text="请设置工具执行所需的参数" 
                           FontSize="12" 
                           Foreground="#E3F2FD" 
                           Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel x:Name="ParametersContainer">
            </StackPanel>
        </ScrollViewer>

        <Border Grid.Row="2" Background="#F8F9FA" Padding="20,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                <Button x:Name="ValidateButton" 
                        Content="验证参数" 
                        Padding="15,8" 
                        Margin="0,0,10,0"
                        Background="#FFC107"
                        Foreground="White"
                        BorderThickness="0"
                        Click="ValidateButton_Click"/>
                
                <Button x:Name="ExecuteButton" 
                        Content="执行工具" 
                        Padding="15,8" 
                        Margin="0,0,10,0"
                        Background="#4CAF50"
                        Foreground="White"
                        BorderThickness="0"
                        Click="ExecuteButton_Click"/>
                
                <Button x:Name="CancelButton" 
                        Content="取消" 
                        Padding="15,8"
                        Background="#9E9E9E"
                        Foreground="White"
                        BorderThickness="0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>

using System;
using System.Collections.Generic;
using System.IO;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 项目信息模型
    /// </summary>
    public class ProjectInfo
    {
        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 项目路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 项目类型
        /// </summary>
        public ProjectType Type { get; set; }

        /// <summary>
        /// 项目描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; }

        /// <summary>
        /// 文件数量
        /// </summary>
        public int FileCount { get; set; }

        /// <summary>
        /// MD文件数量
        /// </summary>
        public int MarkdownFileCount { get; set; }

        /// <summary>
        /// 项目大小（字节）
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 是否为活跃项目
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 项目标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 主要文件列表
        /// </summary>
        public List<string> MainFiles { get; set; } = new();

        /// <summary>
        /// 获取项目类型显示名称
        /// </summary>
        public string TypeDisplayName => Type switch
        {
            ProjectType.MarkdownProject => "Markdown项目",
            ProjectType.NovelProject => "小说项目",
            ProjectType.DocumentProject => "文档项目",
            ProjectType.CodeProject => "代码项目",
            ProjectType.MixedProject => "混合项目",
            ProjectType.Unknown => "未知类型",
            _ => "其他"
        };

        /// <summary>
        /// 获取格式化的大小字符串
        /// </summary>
        public string FormattedSize
        {
            get
            {
                if (Size < 1024) return $"{Size} B";
                if (Size < 1024 * 1024) return $"{Size / 1024.0:F1} KB";
                if (Size < 1024 * 1024 * 1024) return $"{Size / (1024.0 * 1024):F1} MB";
                return $"{Size / (1024.0 * 1024 * 1024):F1} GB";
            }
        }

        /// <summary>
        /// 获取相对路径显示
        /// </summary>
        public string RelativePath
        {
            get
            {
                try
                {
                    var currentDir = Directory.GetCurrentDirectory();
                    var uri1 = new Uri(currentDir + System.IO.Path.DirectorySeparatorChar);
                    var uri2 = new Uri(Path);
                    return uri1.MakeRelativeUri(uri2).ToString().Replace('/', System.IO.Path.DirectorySeparatorChar);
                }
                catch
                {
                    return Path;
                }
            }
        }
    }

    /// <summary>
    /// 项目类型枚举
    /// </summary>
    public enum ProjectType
    {
        /// <summary>
        /// 未知类型
        /// </summary>
        Unknown,

        /// <summary>
        /// Markdown项目（主要包含.md文件）
        /// </summary>
        MarkdownProject,

        /// <summary>
        /// 小说项目（包含章节文件）
        /// </summary>
        NovelProject,

        /// <summary>
        /// 文档项目（包含各种文档文件）
        /// </summary>
        DocumentProject,

        /// <summary>
        /// 代码项目（包含源代码文件）
        /// </summary>
        CodeProject,

        /// <summary>
        /// 混合项目（包含多种类型文件）
        /// </summary>
        MixedProject
    }

    /// <summary>
    /// 项目扫描结果
    /// </summary>
    public class ProjectScanResult
    {
        /// <summary>
        /// 扫描到的项目列表
        /// </summary>
        public List<ProjectInfo> Projects { get; set; } = new();

        /// <summary>
        /// 扫描的目录数量
        /// </summary>
        public int ScannedDirectories { get; set; }

        /// <summary>
        /// 扫描耗时
        /// </summary>
        public TimeSpan ScanDuration { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess => Errors.Count == 0;
    }

    /// <summary>
    /// 项目文件信息
    /// </summary>
    public class ProjectFileInfo
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 文件路径
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Extension { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; }

        /// <summary>
        /// 修改时间
        /// </summary>
        public DateTime ModifiedTime { get; set; }

        /// <summary>
        /// 是否为主要文件
        /// </summary>
        public bool IsMainFile { get; set; }

        /// <summary>
        /// 文件类型
        /// </summary>
        public FileType Type { get; set; }
    }

    /// <summary>
    /// 文件类型枚举
    /// </summary>
    public enum FileType
    {
        Markdown,
        Text,
        Word,
        Code,
        Image,
        Other
    }
}

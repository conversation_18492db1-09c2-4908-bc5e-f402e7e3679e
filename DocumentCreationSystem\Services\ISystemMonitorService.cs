using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 系统监控服务接口
/// </summary>
public interface ISystemMonitorService
{
    /// <summary>
    /// 获取当前AI模型信息
    /// </summary>
    /// <returns>当前AI模型信息</returns>
    Task<CurrentModelInfo> GetCurrentModelInfoAsync();

    /// <summary>
    /// 获取系统资源使用情况
    /// </summary>
    /// <returns>系统资源信息</returns>
    Task<SystemResourceInfo> GetSystemResourceInfoAsync();

    /// <summary>
    /// 获取GPU使用情况
    /// </summary>
    /// <returns>GPU使用信息</returns>
    Task<GpuInfo> GetGpuInfoAsync();

    /// <summary>
    /// 获取CPU使用情况
    /// </summary>
    /// <returns>CPU使用信息</returns>
    Task<CpuInfo> GetCpuInfoAsync();

    /// <summary>
    /// 获取内存使用情况
    /// </summary>
    /// <returns>内存使用信息</returns>
    Task<MemoryInfo> GetMemoryInfoAsync();

    /// <summary>
    /// 开始监控
    /// </summary>
    void StartMonitoring();

    /// <summary>
    /// 停止监控
    /// </summary>
    void StopMonitoring();

    /// <summary>
    /// 监控数据更新事件
    /// </summary>
    event EventHandler<SystemMonitorEventArgs>? MonitorDataUpdated;

    /// <summary>
    /// 获取系统指标
    /// </summary>
    /// <returns>系统指标信息</returns>
    Task<SystemMetrics> GetSystemMetricsAsync();

    /// <summary>
    /// 获取CPU使用率
    /// </summary>
    /// <returns>CPU使用率</returns>
    Task<float> GetCpuUsageAsync();

    /// <summary>
    /// 获取内存使用率
    /// </summary>
    /// <returns>内存使用率</returns>
    Task<float> GetMemoryUsageAsync();

    /// <summary>
    /// 获取GPU使用率
    /// </summary>
    /// <returns>GPU使用率</returns>
    Task<float> GetGpuUsageAsync();

    /// <summary>
    /// 获取磁盘使用率
    /// </summary>
    /// <returns>磁盘使用率</returns>
    Task<float> GetDiskUsageAsync();

    /// <summary>
    /// 获取网络使用率
    /// </summary>
    /// <returns>网络使用率</returns>
    Task<float> GetNetworkUsageAsync();

    /// <summary>
    /// 获取详细系统信息
    /// </summary>
    /// <returns>详细系统信息</returns>
    Task<Dictionary<string, object>> GetDetailedSystemInfoAsync();
}

/// <summary>
/// 系统监控事件参数
/// </summary>
public class SystemMonitorEventArgs : EventArgs
{
    public CurrentModelInfo ModelInfo { get; set; } = new();
    public SystemResourceInfo ResourceInfo { get; set; } = new();
    public DateTime UpdateTime { get; set; } = DateTime.Now;
}

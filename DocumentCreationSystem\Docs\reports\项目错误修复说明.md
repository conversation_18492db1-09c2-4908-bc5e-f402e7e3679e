# 项目错误修复说明

## 问题描述

当您在使用"一键写书"功能时，可能会遇到以下错误：
```
写书过程中发生错误:小说项目不存在:6
```

这个错误通常发生在以下情况：
1. 通过"打开项目文件夹"方式进入项目，而不是通过"新建项目"创建的正式项目
2. 项目数据存储文件丢失或损坏
3. 项目ID和小说项目ID之间的映射关系有问题

## 自动修复功能

系统现在提供了自动修复功能：

### 1. 错误检测
当系统检测到"小说项目不存在"错误时，会自动弹出询问对话框：
```
检测到小说项目配置问题。是否打开修复工具来解决此问题？
```

### 2. 修复选项
点击"是"后，会打开项目错误修复对话框，提供三种解决方案：

#### 选项1：为当前项目创建小说项目记录（推荐）
- 适用于：已有项目文件夹，只是缺少小说项目记录
- 操作：填写小说标题和创作方向，系统会为当前项目创建对应的小说项目记录
- 优点：保持现有项目结构不变

#### 选项2：创建新的正式项目
- 适用于：希望将临时项目转换为正式项目
- 操作：填写项目名称，系统会创建新的正式项目记录
- 优点：获得完整的项目管理功能

#### 选项3：取消操作，稍后手动处理
- 适用于：暂时不想修复，稍后手动处理
- 操作：取消当前操作，返回主界面

## 手动修复方法

如果自动修复失败，您可以尝试以下手动方法：

### 方法1：重新创建项目
1. 在主界面点击"新建项目"
2. 选择项目类型为"小说项目"
3. 设置项目路径为您现有的文件夹
4. 填写项目信息并创建

### 方法2：检查数据存储
1. 检查程序目录下的`Data`文件夹是否存在
2. 如果不存在，程序会自动创建
3. 重启程序让数据存储重新初始化

### 方法3：清理并重新开始
1. 关闭程序
2. 删除程序目录下的`Data`文件夹（注意：这会清除所有项目数据）
3. 重启程序
4. 重新创建项目

## 预防措施

为了避免此类问题，建议：

1. **使用正式项目创建流程**
   - 通过"新建项目"创建项目，而不是直接打开文件夹
   - 选择合适的项目类型（小说项目）

2. **定期备份项目数据**
   - 备份项目文件夹
   - 备份程序的`Data`文件夹

3. **检查项目状态**
   - 在使用写书功能前，确认项目类型正确
   - 查看项目导航中是否显示正确的项目信息

## 调试信息

如果问题持续存在，您可以：

1. **查看日志文件**
   - 程序会在日志中记录详细的错误信息
   - 查看项目状态调试信息

2. **联系技术支持**
   - 提供错误信息和日志文件
   - 描述操作步骤和项目配置

## 常见问题

**Q: 修复后数据会丢失吗？**
A: 不会。修复过程只是创建缺失的项目记录，不会影响现有文件。

**Q: 可以同时有多个项目吗？**
A: 可以。系统支持管理多个项目，每个项目都有独立的ID。

**Q: 修复后需要重新配置吗？**
A: 通常不需要。修复后项目会保持原有的文件结构和配置。

**Q: 如何避免再次出现此问题？**
A: 建议使用"新建项目"功能创建正式项目，而不是直接打开文件夹。

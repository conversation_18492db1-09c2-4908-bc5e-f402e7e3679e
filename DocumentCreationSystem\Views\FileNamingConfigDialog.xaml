<Window x:Class="DocumentCreationSystem.Views.FileNamingConfigDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="文件命名规则配置" 
        Height="600" 
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16" CornerRadius="4" Margin="0,0,0,16">
            <StackPanel>
                <TextBlock Text="文件命名规则配置" 
                          Style="{StaticResource MaterialDesignHeadline5TextBlock}" 
                          Foreground="White" 
                          HorizontalAlignment="Center"/>
                <TextBlock Text="自定义各种文件的命名格式和规则" 
                          Style="{StaticResource MaterialDesignBody2TextBlock}" 
                          Foreground="White" 
                          HorizontalAlignment="Center" 
                          Opacity="0.8"/>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 章节文件命名 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="章节文件命名" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="命名模板：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox x:Name="ChapterNamingTemplateTextBox" 
                                    Grid.Row="0" Grid.Column="1" 
                                    materialDesign:HintAssist.Hint="例如：{BookTitle}_第{VolumeNumber:D2}卷_{VolumeName}_第{ChapterNumber:D3}章_{ChapterTitle}"
                                    Margin="0,0,8,0"/>
                            <Button x:Name="ChapterTemplateHelpButton" 
                                   Grid.Row="0" Grid.Column="2"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   ToolTip="查看可用变量"
                                   Click="ChapterTemplateHelp_Click">
                                <materialDesign:PackIcon Kind="Help"/>
                            </Button>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="预览示例：" VerticalAlignment="Center" Margin="0,8,8,0"/>
                            <TextBlock x:Name="ChapterPreviewText" 
                                      Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                                      Text="示例：我的小说_第01卷_修炼之路_第001章_初入修仙界.txt"
                                      Style="{StaticResource MaterialDesignBody2TextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      Margin="0,8,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 大纲文件命名 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="大纲文件命名" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="命名模板：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox x:Name="OutlineNamingTemplateTextBox" 
                                    Grid.Row="0" Grid.Column="1" 
                                    materialDesign:HintAssist.Hint="例如：{BookTitle}_{OutlineType}_大纲"
                                    Margin="0,0,8,0"/>
                            <Button x:Name="OutlineTemplateHelpButton" 
                                   Grid.Row="0" Grid.Column="2"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   ToolTip="查看可用变量"
                                   Click="OutlineTemplateHelp_Click">
                                <materialDesign:PackIcon Kind="Help"/>
                            </Button>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="预览示例：" VerticalAlignment="Center" Margin="0,8,8,0"/>
                            <TextBlock x:Name="OutlinePreviewText" 
                                      Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                                      Text="示例：我的小说_全书_大纲.txt"
                                      Style="{StaticResource MaterialDesignBody2TextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      Margin="0,8,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 角色设定文件命名 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="角色设定文件命名" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="命名模板：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox x:Name="CharacterNamingTemplateTextBox" 
                                    Grid.Row="0" Grid.Column="1" 
                                    materialDesign:HintAssist.Hint="例如：{BookTitle}_角色_{CharacterName}"
                                    Margin="0,0,8,0"/>
                            <Button x:Name="CharacterTemplateHelpButton" 
                                   Grid.Row="0" Grid.Column="2"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   ToolTip="查看可用变量"
                                   Click="CharacterTemplateHelp_Click">
                                <materialDesign:PackIcon Kind="Help"/>
                            </Button>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="预览示例：" VerticalAlignment="Center" Margin="0,8,8,0"/>
                            <TextBlock x:Name="CharacterPreviewText" 
                                      Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                                      Text="示例：我的小说_角色_李逍遥.txt"
                                      Style="{StaticResource MaterialDesignBody2TextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      Margin="0,8,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 时间线文件命名 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="时间线文件命名" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,12"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="命名模板：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <TextBox x:Name="TimelineNamingTemplateTextBox" 
                                    Grid.Row="0" Grid.Column="1" 
                                    materialDesign:HintAssist.Hint="例如：{BookTitle}_时间线_{VolumeNumber:D2}卷"
                                    Margin="0,0,8,0"/>
                            <Button x:Name="TimelineTemplateHelpButton" 
                                   Grid.Row="0" Grid.Column="2"
                                   Style="{StaticResource MaterialDesignIconButton}"
                                   ToolTip="查看可用变量"
                                   Click="TimelineTemplateHelp_Click">
                                <materialDesign:PackIcon Kind="Help"/>
                            </Button>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="预览示例：" VerticalAlignment="Center" Margin="0,8,8,0"/>
                            <TextBlock x:Name="TimelinePreviewText" 
                                      Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2"
                                      Text="示例：我的小说_时间线_第01卷.txt"
                                      Style="{StaticResource MaterialDesignBody2TextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      Margin="0,8,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 通用设置 -->
                <materialDesign:Card Padding="16" Margin="0,0,0,8">
                    <StackPanel>
                        <TextBlock Text="通用设置" Style="{StaticResource MaterialDesignHeadline6TextBlock}" Margin="0,0,0,12"/>
                        
                        <CheckBox x:Name="AutoSanitizeFileNamesCheckBox" 
                                 Content="自动清理文件名中的非法字符" 
                                 IsChecked="True"
                                 Margin="0,0,0,8"/>
                        
                        <CheckBox x:Name="UseChineseNumbersCheckBox" 
                                 Content="使用中文数字格式（一、二、三）" 
                                 IsChecked="False"
                                 Margin="0,0,0,8"/>
                        
                        <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                            <TextBlock Text="默认文件扩展名：" VerticalAlignment="Center" Margin="0,0,8,0"/>
                            <ComboBox x:Name="DefaultFileExtensionComboBox" Width="120">
                                <ComboBoxItem Content=".txt" IsSelected="True"/>
                                <ComboBoxItem Content=".docx"/>
                                <ComboBoxItem Content=".md"/>
                            </ComboBox>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,16,0,0">
            <Button x:Name="ResetToDefaultButton" 
                   Content="恢复默认" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="ResetToDefault_Click"/>
            <Button x:Name="PreviewButton" 
                   Content="预览效果" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="Preview_Click"/>
            <Button x:Name="CancelButton" 
                   Content="取消" 
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Margin="0,0,8,0"
                   Click="Cancel_Click"/>
            <Button x:Name="SaveButton" 
                   Content="保存" 
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"/>
        </StackPanel>
    </Grid>
</Window>

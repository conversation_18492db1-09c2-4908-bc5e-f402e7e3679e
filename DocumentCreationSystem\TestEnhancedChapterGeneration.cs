using Microsoft.Extensions.Logging;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem
{
    /// <summary>
    /// 测试增强章节生成功能
    /// </summary>
    public class TestEnhancedChapterGeneration
    {
        private readonly ILogger<TestEnhancedChapterGeneration> _logger;

        public TestEnhancedChapterGeneration(ILogger<TestEnhancedChapterGeneration> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 测试output标签清理功能
        /// </summary>
        public void TestOutputTagCleaning()
        {
            Console.WriteLine("=== 测试output标签清理功能 ===");

            var contentQualityService = new ContentQualityService(
                Microsoft.Extensions.Logging.LoggerFactory.Create(builder => builder.AddConsole())
                    .CreateLogger<ContentQualityService>());

            // 测试用例1：标准output标签
            var testContent1 = @"这是正文内容。<output>这是需要删除的内容</output>继续正文。";
            var cleaned1 = contentQualityService.CleanOutputTags(testContent1);
            Console.WriteLine($"原文: {testContent1}");
            Console.WriteLine($"清理后: {cleaned1}");
            Console.WriteLine($"测试1结果: {(cleaned1.Contains("<output>") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例2：大小写变体
            var testContent2 = @"正文<OUTPUT>删除内容</OUTPUT>继续<Output>删除</Output>结束";
            var cleaned2 = contentQualityService.CleanOutputTags(testContent2);
            Console.WriteLine($"原文: {testContent2}");
            Console.WriteLine($"清理后: {cleaned2}");
            Console.WriteLine($"测试2结果: {(cleaned2.Contains("OUTPUT") || cleaned2.Contains("Output") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例3：拼写错误
            var testContent3 = @"正文<ouput>删除内容</ouput>继续";
            var cleaned3 = contentQualityService.CleanOutputTags(testContent3);
            Console.WriteLine($"原文: {testContent3}");
            Console.WriteLine($"清理后: {cleaned3}");
            Console.WriteLine($"测试3结果: {(cleaned3.Contains("ouput") ? "失败" : "成功")}");
            Console.WriteLine();

            // 测试用例4：多个标签
            var testContent4 = @"开始<output>删除1</output>中间<response>删除2</response>结束<o>删除3</o>完成";
            var cleaned4 = contentQualityService.CleanOutputTags(testContent4);
            Console.WriteLine($"原文: {testContent4}");
            Console.WriteLine($"清理后: {cleaned4}");
            Console.WriteLine($"测试4结果: {(cleaned4.Contains("<") ? "失败" : "成功")}");
            Console.WriteLine();
        }

        /// <summary>
        /// 测试增强章节内容服务的基本功能
        /// </summary>
        public void TestEnhancedChapterContentService()
        {
            Console.WriteLine("=== 测试增强章节内容服务 ===");

            try
            {
                // 创建模拟的服务依赖
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                
                // 注意：这里需要实际的服务实例，在真实环境中需要通过依赖注入获取
                Console.WriteLine("增强章节内容服务需要完整的依赖注入环境才能测试");
                Console.WriteLine("建议在实际项目运行时进行集成测试");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试角色名称提取功能
        /// </summary>
        public void TestCharacterNameExtraction()
        {
            Console.WriteLine("=== 测试角色名称提取功能 ===");

            var testOutline = @"
## 第1章：初遇

### 场景设定
- 时间：清晨
- 地点：练武场
- 环境：晨雾缭绕

### 人物动态
- 主角：李明正在练剑
- 重要角色：张师傅观察指导，王小二在一旁学习
- 配角：其他弟子围观

### 情节发展
李明说道：""师傅，这招剑法我还是不太明白。""
张师傅想了想，决定亲自示范。
王小二看到后很兴奋。
";

            // 这里应该调用EnhancedChapterContentService的角色名称提取方法
            // 由于需要完整的服务实例，这里只是演示测试思路
            Console.WriteLine("测试细纲:");
            Console.WriteLine(testOutline);
            Console.WriteLine();
            Console.WriteLine("预期提取的角色名称: 李明, 张师傅, 王小二");
            Console.WriteLine("实际测试需要在完整环境中运行");
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public void RunAllTests()
        {
            Console.WriteLine("开始运行增强章节生成功能测试...");
            Console.WriteLine();

            try
            {
                TestOutputTagCleaning();
                TestEnhancedChapterContentService();
                TestCharacterNameExtraction();

                Console.WriteLine("=== 测试总结 ===");
                Console.WriteLine("1. output标签清理功能 - 可以独立测试");
                Console.WriteLine("2. 增强章节内容服务 - 需要完整环境");
                Console.WriteLine("3. 角色名称提取功能 - 需要完整环境");
                Console.WriteLine();
                Console.WriteLine("建议在实际项目中进行完整的集成测试");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试过程中发生错误");
                Console.WriteLine($"测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 主测试入口
        /// </summary>
        public static void Main(string[] args)
        {
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<TestEnhancedChapterGeneration>();
            
            var tester = new TestEnhancedChapterGeneration(logger);
            tester.RunAllTests();

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}

namespace DocumentCreationSystem.Models;

/// <summary>
/// 向量搜索结果
/// </summary>
public class VectorSearchResult
{
    public string Id { get; set; } = "";
    public float Score { get; set; }
    public string Content { get; set; } = "";
    public int DocumentId { get; set; }
    public int ChunkIndex { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// 卷宗大纲
/// </summary>
public class VolumeOutline
{
    public int VolumeNumber { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int StartChapter { get; set; }
    public int EndChapter { get; set; }
    public string MainPlot { get; set; } = string.Empty;
    public List<string> KeyEvents { get; set; } = new();
    public List<string> CharacterDevelopments { get; set; } = new();

    // 保持向后兼容
    public string Outline
    {
        get => Description;
        set => Description = value;
    }
    public int ChapterCount => EndChapter - StartChapter + 1;
}

/// <summary>
/// 章节大纲
/// </summary>
public class ChapterOutline
{
    public int ChapterNumber { get; set; }
    public string Title { get; set; } = "";
    public string Outline { get; set; } = "";
    public int TargetWordCount { get; set; }
    public DateTime CreatedAt { get; set; }
}

/// <summary>
/// 角色更新信息
/// </summary>
public class CharacterUpdate
{
    public string CharacterName { get; set; } = "";
    public List<AttributeChange> Changes { get; set; } = new();
}

/// <summary>
/// 属性变更信息
/// </summary>
public class AttributeChange
{
    public string Attribute { get; set; } = "";
    public string OldValue { get; set; } = "";
    public string NewValue { get; set; } = "";
    public string Description { get; set; } = "";
}

/// <summary>
/// 章节创作进度
/// </summary>
public class ChapterCreationProgress
{
    public int ChapterId { get; set; }
    public int ChapterNumber { get; set; }
    public string CurrentPhase { get; set; } = "";
    public int CurrentWordCount { get; set; }
    public int WordCount { get; set; }
    public int TargetWordCount { get; set; }
    public float ProgressPercentage { get; set; }
    public string Status { get; set; } = "";
    public DateTime StartTime { get; set; }
    public DateTime? EstimatedCompletion { get; set; }
}

/// <summary>
/// 上下文类型
/// </summary>
public enum ContextType
{
    Character,
    Plot,
    Setting,
    Previous,
    All
}



/// <summary>
/// AI模型信息
/// </summary>
public class AIModel
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty; // ZhipuAI, Ollama, LMStudio
    public string Description { get; set; } = string.Empty;
    public bool IsAvailable { get; set; }
    public int MaxTokens { get; set; }
    public Dictionary<string, object> Parameters { get; set; } = new();
    public bool SupportsFunctionCalling { get; set; } = false;

    // 新增属性用于AI模型配置
    public long Size { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime ModifiedAt { get; set; }
}

/// <summary>
/// 一致性检查结果
/// </summary>
public class ConsistencyCheckResult
{
    public bool IsConsistent { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
    public float ConfidenceScore { get; set; }
}

/// <summary>
/// 集合信息
/// </summary>
public class CollectionInfo
{
    public string Name { get; set; } = "";
    public int VectorCount { get; set; }
    public int Dimension { get; set; }
    public string Status { get; set; } = "";
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 角色信息
/// </summary>
public class CharacterInfo
{
    public string Name { get; set; } = string.Empty;
    public Dictionary<string, string> Attributes { get; set; } = new();
    public List<string> Skills { get; set; } = new();
    public List<string> Equipment { get; set; } = new();
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// 向量集合信息
/// </summary>
public class VectorCollectionInfo
{
    public string Name { get; set; } = string.Empty;
    public int VectorSize { get; set; }
    public long PointsCount { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

/// <summary>
/// 文本分割配置
/// </summary>
public class TextSplitConfig
{
    public int ChunkSize { get; set; } = 500;
    public int Overlap { get; set; } = 50;
    public string Separator { get; set; } = "\n\n";
    public bool PreserveStructure { get; set; } = true;
}

/// <summary>
/// 章节创作结果
/// </summary>
public class ChapterCreationResult
{
    public bool IsSuccess { get; set; }
    public string Content { get; set; } = string.Empty;
    public int WordCount { get; set; }
    public List<string> Warnings { get; set; } = new();
    public List<string> Suggestions { get; set; } = new();
    public TimeSpan CreationTime { get; set; }
    public bool IsCompleted { get; set; }
    public string? ErrorMessage { get; set; }
}

/// <summary>
/// 章节进度信息
/// </summary>
public class ChapterProgress
{
    public int ChapterId { get; set; }
    public int WordCount { get; set; }
    public int TargetWordCount { get; set; }
    public float CompletionPercentage { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime LastModified { get; set; }
    public List<ConsistencyCheckResult> ConsistencyChecks { get; set; } = new();
    public bool IsAutoGenerating { get; set; }
}

/// <summary>
/// 小说创作统计
/// </summary>
public class NovelCreationStatistics
{
    public int TotalChapters { get; set; }
    public int CompletedChapters { get; set; }
    public int InProgressChapters { get; set; }
    public int PlannedChapters { get; set; }
    public int TotalWordCount { get; set; }
    public int AverageWordsPerChapter { get; set; }
    public float CompletionPercentage { get; set; }
    public TimeSpan TotalCreationTime { get; set; }
    public DateTime LastCreationDate { get; set; }
    public int CharacterCount { get; set; }
    public Dictionary<string, int> CharacterAppearances { get; set; } = new();
}

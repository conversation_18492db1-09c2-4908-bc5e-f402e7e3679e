<UserControl x:Class="DocumentCreationSystem.Controls.DocumentEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
             xmlns:local="clr-namespace:DocumentCreationSystem.Controls">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 编辑器工具栏 -->
        <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryLight" Padding="8">
            <StackPanel Orientation="Horizontal">
                <!-- 格式化按钮 -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="粗体" Margin="2,0"
                      Click="Bold_Click">
                    <TextBlock Text="B" FontWeight="Bold"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="斜体" Margin="2,0"
                      Click="Italic_Click">
                    <TextBlock Text="I" FontStyle="Italic"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="下划线" Margin="2,0"
                      Click="Underline_Click">
                    <TextBlock Text="U" TextDecorations="Underline"/>
                </Button>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- 段落格式 -->
                <ComboBox x:Name="FontSizeComboBox" 
                        Width="80" Margin="4,0"
                        materialDesign:HintAssist.Hint="字号"
                        SelectedValue="14"
                        SelectionChanged="FontSize_Changed">
                    <ComboBoxItem Content="12"/>
                    <ComboBoxItem Content="14" IsSelected="True"/>
                    <ComboBoxItem Content="16"/>
                    <ComboBoxItem Content="18"/>
                    <ComboBoxItem Content="20"/>
                    <ComboBoxItem Content="24"/>
                </ComboBox>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- 文件操作 -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="保存文档 (Ctrl+S)" Margin="2,0"
                      Click="Save_Click">
                    <TextBlock Text="保存"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="另存为" Margin="2,0"
                      Click="SaveAs_Click">
                    <TextBlock Text="另存为"/>
                </Button>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- 编辑操作 -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="撤销" Margin="2,0"
                      Click="Undo_Click">
                    <TextBlock Text="撤销"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="重做" Margin="2,0"
                      Click="Redo_Click">
                    <TextBlock Text="重做"/>
                </Button>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- AI助手功能 -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="章节重写" Margin="2,0"
                      Click="RewriteChapter_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- 查找替换 -->
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="查找" Margin="2,0"
                      Click="Find_Click">
                    <TextBlock Text="查找"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="替换" Margin="2,0"
                      Click="Replace_Click">
                    <TextBlock Text="替换"/>
                </Button>

                <Separator Margin="8,0" Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}"/>

                <!-- 字数统计 -->
                <StackPanel Orientation="Horizontal" Margin="8,0">
                    <TextBlock Text="📊" VerticalAlignment="Center" Margin="0,0,4,0"/>
                    <TextBlock x:Name="WordCountDisplay" Text="字数: 0" VerticalAlignment="Center" Margin="0,0,8,0"/>

                    <!-- 详细统计按钮 -->
                    <Button x:Name="DetailedStatsButton"
                            Style="{StaticResource MaterialDesignToolButton}"
                            ToolTip="查看详细统计信息"
                            Click="DetailedStats_Click"
                            Margin="4,0">
                        <TextBlock Text="📈"/>
                    </Button>
                </StackPanel>
            </StackPanel>
        </materialDesign:ColorZone>

        <!-- 主编辑区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 文本编辑器 -->
            <Border Grid.Column="0"
                    Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="1">
                <avalonEdit:TextEditor x:Name="TextEditor"
                                     FontFamily="Microsoft YaHei, Consolas"
                                     FontSize="14"
                                     ShowLineNumbers="True"
                                     WordWrap="True"
                                     HorizontalScrollBarVisibility="Auto"
                                     VerticalScrollBarVisibility="Auto"
                                     Padding="16"
                                     Background="{DynamicResource MaterialDesignPaper}"
                                     Foreground="{DynamicResource MaterialDesignBody}"
                                     TextChanged="TextEditor_TextChanged">
                    <avalonEdit:TextEditor.Options>
                        <avalonEdit:TextEditorOptions ShowSpaces="False"
                                                     ShowTabs="False"
                                                     ShowEndOfLine="False"
                                                     ConvertTabsToSpaces="True"
                                                     IndentationSize="4"/>
                    </avalonEdit:TextEditor.Options>
                </avalonEdit:TextEditor>
            </Border>

            <!-- 润色预览面板 -->
            <Border Grid.Column="1" x:Name="PolishPreviewPanel"
                    Width="350" Background="#FFF8F8F8"
                    BorderBrush="LightGray" BorderThickness="1,0,0,0"
                    Visibility="Collapsed">
                <local:PolishPreviewControl x:Name="PolishPreview"
                                          PolishAccepted="PolishPreview_PolishAccepted"
                                          PolishRejected="PolishPreview_PolishRejected"
                                          PreviewClosed="PolishPreview_PreviewClosed"/>
            </Border>

            <!-- 翻译扩写预览面板 -->
            <Border Grid.Column="1" x:Name="TranslateExpandPreviewPanel"
                    Width="350" Background="#FFF8F8F8"
                    BorderBrush="LightGray" BorderThickness="1,0,0,0"
                    Visibility="Collapsed">
                <local:TranslateExpandPreviewControl x:Name="TranslateExpandPreview"
                                                   ProcessAccepted="TranslateExpandPreview_ProcessAccepted"
                                                   ProcessRejected="TranslateExpandPreview_ProcessRejected"
                                                   PreviewClosed="TranslateExpandPreview_PreviewClosed"/>
            </Border>
        </Grid>

        <!-- 底部状态栏和功能区域 -->
        <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryDark" Padding="12,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：文档状态信息 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <materialDesign:PackIcon x:Name="DocumentStatusIcon" Kind="FileDocument"
                                           Foreground="LightGreen" VerticalAlignment="Center" Margin="0,0,8,0"/>
                    <TextBlock x:Name="DocumentStatusText" Text="文档就绪" VerticalAlignment="Center" Margin="0,0,16,0"/>
                    <TextBlock x:Name="CurrentFileText" Text="未打开文件" VerticalAlignment="Center" Margin="0,0,16,0"/>

                    <!-- AI处理状态 -->
                    <Border x:Name="AIProcessingIndicator" Background="#FF4CAF50" CornerRadius="4" Padding="8,4"
                            Margin="0,0,8,0" Visibility="Collapsed"
                            ToolTip="AI正在处理中">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Robot" Foreground="White"
                                                   VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock x:Name="AIProcessingText" Text="AI处理中..."
                                     Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 右侧：系统监控信息 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">

                    <!-- CPU使用率 -->
                    <Border Background="#2E2E2E" CornerRadius="4" Padding="8,4" Margin="0,0,8,0"
                            ToolTip="CPU使用率">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Cpu64Bit" Foreground="#FF9800"
                                                   VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock x:Name="CpuUsageText" Text="0%"
                                     Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- GPU使用率 -->
                    <Border Background="#2E2E2E" CornerRadius="4" Padding="8,4" Margin="0,0,8,0"
                            ToolTip="GPU使用率和显存使用">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Video" Foreground="#E91E63"
                                                   VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock x:Name="GpuUsageText" Text="0%"
                                     Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                            <TextBlock Text=" | " Foreground="Gray" FontSize="11" VerticalAlignment="Center"/>
                            <TextBlock x:Name="GpuMemoryText" Text="0%"
                                     Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- 内存使用率 -->
                    <Border Background="#2E2E2E" CornerRadius="4" Padding="8,4"
                            ToolTip="内存使用率">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Memory" Foreground="#9C27B0"
                                                   VerticalAlignment="Center" Margin="0,0,4,0"/>
                            <TextBlock x:Name="MemoryUsageText" Text="0%"
                                     Foreground="White" FontSize="11" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- 查找面板 -->
        <materialDesign:Card x:Name="FindPanel" 
                           Grid.Row="1" 
                           VerticalAlignment="Top" 
                           HorizontalAlignment="Right"
                           Margin="16"
                           Visibility="Collapsed"
                           materialDesign:ElevationAssist.Elevation="Dp4">
            <StackPanel Orientation="Horizontal" Margin="16,8">
                <TextBox x:Name="FindTextBox" 
                       Width="200"
                       materialDesign:HintAssist.Hint="查找内容"
                       KeyDown="FindTextBox_KeyDown"/>
                
                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="查找下一个" Margin="4,0"
                      Click="FindNext_Click">
                    <TextBlock Text="↓"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="查找上一个" Margin="4,0"
                      Click="FindPrevious_Click">
                    <TextBlock Text="↑"/>
                </Button>

                <Button Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="关闭" Margin="4,0"
                      Click="CloseFindPanel_Click">
                    <TextBlock Text="✕"/>
                </Button>
            </StackPanel>
        </materialDesign:Card>

        <!-- 详细统计信息弹出窗口 -->
        <Popup x:Name="StatsPopup"
               PlacementTarget="{Binding ElementName=DetailedStatsButton}"
               Placement="Bottom"
               StaysOpen="False"
               AllowsTransparency="True">
            <materialDesign:Card Margin="8" Padding="16" MaxWidth="400">
                <StackPanel>
                    <TextBlock Text="详细统计信息"
                               Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                               Margin="0,0,0,12"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="总字数：" Margin="0,2"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="TotalWordsText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="中文字符：" Margin="0,2"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="ChineseCharsText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="英文单词：" Margin="0,2"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="EnglishWordsText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="总字符数：" Margin="0,2"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" x:Name="TotalCharsText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="不含空格：" Margin="0,2"/>
                        <TextBlock Grid.Row="4" Grid.Column="1" x:Name="CharsNoSpaceText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="段落数：" Margin="0,2"/>
                        <TextBlock Grid.Row="5" Grid.Column="1" x:Name="ParagraphsText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="6" Grid.Column="0" Text="行数：" Margin="0,2"/>
                        <TextBlock Grid.Row="6" Grid.Column="1" x:Name="LinesText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="7" Grid.Column="0" Text="句子数：" Margin="0,2"/>
                        <TextBlock Grid.Row="7" Grid.Column="1" x:Name="SentencesText" Text="0" Margin="8,2"/>

                        <TextBlock Grid.Row="8" Grid.Column="0" Text="预计阅读时间：" Margin="0,2"/>
                        <TextBlock Grid.Row="8" Grid.Column="1" x:Name="ReadingTimeText" Text="0分钟" Margin="8,2"/>
                    </Grid>

                    <Button Content="关闭"
                            Style="{StaticResource MaterialDesignFlatButton}"
                            HorizontalAlignment="Right"
                            Margin="0,12,0,0"
                            Click="CloseStatsPopup_Click"/>
                </StackPanel>
            </materialDesign:Card>
        </Popup>
    </Grid>
</UserControl>

using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Services;

/// <summary>
/// 小说创作服务接口
/// </summary>
public interface INovelCreationService
{
    /// <summary>
    /// 创建小说项目
    /// </summary>
    /// <param name="projectId">项目ID</param>
    /// <param name="title">小说标题</param>
    /// <param name="description">创作方向描述</param>
    /// <param name="targetChapters">目标章节数</param>
    /// <param name="targetWordsPerChapter">每章目标字数</param>
    /// <returns>创建的小说项目</returns>
    Task<NovelProject> CreateNovelProjectAsync(int projectId, string title, string description, int targetChapters, int targetWordsPerChapter);

    /// <summary>
    /// 生成全书大纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="creativeDirection">创作方向</param>
    /// <param name="volumeCount">分卷数量</param>
    /// <returns>生成的全书大纲</returns>
    Task<string> GenerateOverallOutlineAsync(int novelProjectId, string creativeDirection, int volumeCount = 10);

    /// <summary>
    /// 生成卷宗大纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="overallOutline">全书大纲</param>
    /// <param name="volumeCount">卷数</param>
    /// <returns>生成的卷宗大纲</returns>
    Task<List<VolumeOutline>> GenerateVolumeOutlinesAsync(int novelProjectId, string overallOutline, int volumeCount = 10);

    /// <summary>
    /// 生成章节细纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="volumeOutline">卷宗大纲</param>
    /// <param name="chapterNumber">章节号</param>
    /// <returns>生成的章节细纲</returns>
    Task<string> GenerateChapterOutlineAsync(int novelProjectId, VolumeOutline volumeOutline, int chapterNumber);

    /// <summary>
    /// 创建新章节
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="chapterNumber">章节号</param>
    /// <param name="title">章节标题</param>
    /// <param name="outline">章节大纲</param>
    /// <returns>创建的章节</returns>
    Task<Chapter> CreateChapterAsync(int novelProjectId, int chapterNumber, string title, string? outline = null);

    /// <summary>
    /// 自动创作章节内容
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <param name="progressCallback">进度回调</param>
    /// <returns>创作结果</returns>
    Task<ChapterCreationResult> AutoGenerateChapterAsync(int chapterId, IProgress<ChapterCreationProgress>? progressCallback = null);

    /// <summary>
    /// 继续创作章节（从中断点继续）
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <param name="progressCallback">进度回调</param>
    /// <returns>创作结果</returns>
    Task<ChapterCreationResult> ContinueChapterCreationAsync(int chapterId, IProgress<ChapterCreationProgress>? progressCallback = null);

    /// <summary>
    /// 检查上下文一致性
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <param name="currentContent">当前内容</param>
    /// <returns>一致性检查结果</returns>
    Task<ConsistencyCheckResult> CheckChapterConsistencyAsync(int chapterId, string currentContent);

    /// <summary>
    /// 智能章节收尾
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <param name="currentContent">当前内容</param>
    /// <returns>收尾内容</returns>
    Task<string> GenerateChapterEndingAsync(int chapterId, string currentContent);

    /// <summary>
    /// 更新角色属性
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="chapterContent">章节内容</param>
    /// <param name="chapterNumber">章节号</param>
    /// <returns>更新的角色列表</returns>
    Task<List<Character>> UpdateCharacterAttributesAsync(int novelProjectId, string chapterContent, int chapterNumber);

    /// <summary>
    /// 获取相关历史上下文
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="currentChapter">当前章节号</param>
    /// <param name="contextType">上下文类型</param>
    /// <returns>相关上下文</returns>
    Task<string> GetRelevantHistoryContextAsync(int novelProjectId, int currentChapter, ContextType contextType);

    /// <summary>
    /// 获取章节创作进度
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <returns>创作进度</returns>
    Task<ChapterProgress> GetChapterProgressAsync(int chapterId);

    /// <summary>
    /// 暂停章节创作
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <returns>是否暂停成功</returns>
    Task<bool> PauseChapterCreationAsync(int chapterId);

    /// <summary>
    /// 完成章节创作
    /// </summary>
    /// <param name="chapterId">章节ID</param>
    /// <returns>是否完成成功</returns>
    Task<bool> CompleteChapterAsync(int chapterId);

    /// <summary>
    /// 获取小说创作统计
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <returns>创作统计</returns>
    Task<NovelCreationStatistics> GetCreationStatisticsAsync(int novelProjectId);

    /// <summary>
    /// 根据世界设定生成全书大纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="creativeDirection">创作方向</param>
    /// <param name="worldSetting">世界设定</param>
    /// <param name="volumeCount">分卷数量</param>
    /// <returns>生成的全书大纲</returns>
    Task<string> GenerateOverallOutlineWithWorldSettingAsync(int novelProjectId, string creativeDirection, WorldSetting worldSetting, int volumeCount = 10);

    /// <summary>
    /// 根据时间线生成分卷大纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="overallOutline">全书大纲</param>
    /// <param name="volumeNumber">卷号</param>
    /// <param name="totalVolumes">总卷数</param>
    /// <param name="startChapter">起始章节</param>
    /// <param name="endChapter">结束章节</param>
    /// <param name="previousVolumeTimeline">前一卷时间线</param>
    /// <returns>生成的分卷大纲</returns>
    Task<VolumeOutline> GenerateVolumeOutlineWithTimelineAsync(int novelProjectId, string overallOutline, int volumeNumber, int totalVolumes, int startChapter, int endChapter, string previousVolumeTimeline);

    /// <summary>
    /// 根据时间线生成章节细纲
    /// </summary>
    /// <param name="novelProjectId">小说项目ID</param>
    /// <param name="volumeOutline">分卷大纲</param>
    /// <param name="chapterNumber">章节号</param>
    /// <param name="previousChapterTimeline">前一章时间线</param>
    /// <returns>生成的章节细纲</returns>
    Task<string> GenerateChapterOutlineWithTimelineAsync(int novelProjectId, VolumeOutline volumeOutline, int chapterNumber, string previousChapterTimeline);
}



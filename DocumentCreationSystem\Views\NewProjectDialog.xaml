<Window x:Class="DocumentCreationSystem.Views.NewProjectDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="新建项目" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,24">
            <materialDesign:PackIcon Kind="FolderPlus" Width="32" Height="32"
                                   VerticalAlignment="Center" Margin="0,0,16,0"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock Text="创建新项目" FontSize="24" FontWeight="Medium" VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 表单内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 项目名称 -->
                <TextBox x:Name="ProjectNameTextBox"
                       materialDesign:HintAssist.Hint="项目名称"
                       materialDesign:HintAssist.IsFloating="True"
                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                       Margin="0,0,0,16"/>

                <!-- 项目类型 -->
                <ComboBox x:Name="ProjectTypeComboBox"
                        materialDesign:HintAssist.Hint="项目类型"
                        materialDesign:HintAssist.IsFloating="True"
                        Style="{StaticResource MaterialDesignOutlinedComboBox}"
                        Margin="0,0,0,16">
                    <ComboBoxItem Content="普通文档项目" Tag="Normal"/>
                    <ComboBoxItem Content="小说创作项目" Tag="Novel" IsSelected="True"/>
                </ComboBox>

                <!-- 项目路径 -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="ProjectPathTextBox"
                           Grid.Column="0"
                           materialDesign:HintAssist.Hint="项目路径"
                           materialDesign:HintAssist.IsFloating="True"
                           Style="{StaticResource MaterialDesignOutlinedTextBox}"
                           IsReadOnly="True"/>
                    
                    <Button Grid.Column="1" 
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="选择文件夹"
                          Margin="8,0,0,0"
                          Click="BrowseFolder_Click">
                        <materialDesign:PackIcon Kind="FolderOpen"/>
                    </Button>
                </Grid>

                <!-- 项目描述 -->
                <TextBox x:Name="ProjectDescriptionTextBox"
                       materialDesign:HintAssist.Hint="项目描述（可选）"
                       materialDesign:HintAssist.IsFloating="True"
                       Style="{StaticResource MaterialDesignOutlinedTextBox}"
                       AcceptsReturn="True"
                       TextWrapping="Wrap"
                       MinLines="3"
                       MaxLines="5"
                       Margin="0,0,0,16"/>

                <!-- 小说项目特殊设置 -->
                <Expander x:Name="NovelSettingsExpander" 
                        Header="小说创作设置" 
                        IsExpanded="True"
                        Margin="0,0,0,16">
                    <StackPanel Margin="16,8,0,0">
                        <!-- 目标章节数 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="目标章节数" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" 
                                   x:Name="TargetChapterCountTextBox"
                                   Text="1000" 
                                   Width="100"
                                   materialDesign:HintAssist.Hint="章节数"/>
                        </Grid>

                        <!-- 每章目标字数 -->
                        <Grid Margin="0,0,0,16">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="每章目标字数" VerticalAlignment="Center"/>
                            <TextBox Grid.Column="1" 
                                   x:Name="TargetWordsPerChapterTextBox"
                                   Text="6500" 
                                   Width="100"
                                   materialDesign:HintAssist.Hint="字数"/>
                        </Grid>

                        <!-- 创作方向 -->
                        <TextBox x:Name="CreativeDirectionTextBox"
                               materialDesign:HintAssist.Hint="创作方向（例如：现代都市修仙小说）"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedTextBox}"
                               AcceptsReturn="True"
                               TextWrapping="Wrap"
                               MinLines="2"
                               MaxLines="4"/>
                    </StackPanel>
                </Expander>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,24,0,0">
            <Button Content="取消" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Margin="0,0,16,0"
                  Padding="24,8"
                  Click="Cancel_Click"/>
            <Button Style="{StaticResource MaterialDesignRaisedButton}"
                  Padding="24,8"
                  Click="CreateProject_Click">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Check" Margin="0,0,8,0"/>
                        <TextBlock Text="创建项目"/>
                    </StackPanel>
                </Button.Content>
            </Button>
        </StackPanel>
    </Grid>
</Window>

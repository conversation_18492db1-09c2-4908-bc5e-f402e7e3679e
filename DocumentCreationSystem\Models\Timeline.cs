using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    /// <summary>
    /// 时间线事件
    /// </summary>
    public class TimelineEvent
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 所属项目ID
        /// </summary>
        public int ProjectId { get; set; }

        /// <summary>
        /// 章节编号
        /// </summary>
        public int ChapterNumber { get; set; }

        /// <summary>
        /// 事件类型（人物、势力、世界）
        /// </summary>
        public string EventType { get; set; } = string.Empty;

        /// <summary>
        /// 关联对象ID（角色ID、势力ID等）
        /// </summary>
        public string? RelatedObjectId { get; set; }

        /// <summary>
        /// 关联对象名称
        /// </summary>
        public string RelatedObjectName { get; set; } = string.Empty;

        /// <summary>
        /// 事件标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 事件描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 故事内时间
        /// </summary>
        public string? StoryTime { get; set; }

        /// <summary>
        /// 地点
        /// </summary>
        public string? Location { get; set; }

        /// <summary>
        /// 重要程度（1-5）
        /// </summary>
        public int Importance { get; set; } = 3;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 角色时间线
    /// </summary>
    public class CharacterTimeline
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 所属项目ID
        /// </summary>
        public int ProjectId { get; set; }

        /// <summary>
        /// 角色ID
        /// </summary>
        public string CharacterId { get; set; } = string.Empty;

        /// <summary>
        /// 角色名称
        /// </summary>
        public string CharacterName { get; set; } = string.Empty;

        /// <summary>
        /// 当前状态
        /// </summary>
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// 当前位置
        /// </summary>
        public string? CurrentLocation { get; set; }

        /// <summary>
        /// 当前实力等级
        /// </summary>
        public string? PowerLevel { get; set; }

        /// <summary>
        /// 重要关系
        /// </summary>
        public string? ImportantRelationships { get; set; }

        /// <summary>
        /// 最后出现章节
        /// </summary>
        public int LastAppearanceChapter { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 势力时间线
    /// </summary>
    public class FactionTimeline
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 所属项目ID
        /// </summary>
        public int ProjectId { get; set; }

        /// <summary>
        /// 势力ID
        /// </summary>
        public string FactionId { get; set; } = string.Empty;

        /// <summary>
        /// 势力名称
        /// </summary>
        public string FactionName { get; set; } = string.Empty;

        /// <summary>
        /// 当前状态
        /// </summary>
        public string CurrentStatus { get; set; } = string.Empty;

        /// <summary>
        /// 控制区域
        /// </summary>
        public string? ControlledTerritory { get; set; }

        /// <summary>
        /// 实力评估
        /// </summary>
        public string? PowerAssessment { get; set; }

        /// <summary>
        /// 重要成员
        /// </summary>
        public string? ImportantMembers { get; set; }

        /// <summary>
        /// 盟友关系
        /// </summary>
        public string? Allies { get; set; }

        /// <summary>
        /// 敌对关系
        /// </summary>
        public string? Enemies { get; set; }

        /// <summary>
        /// 最后提及章节
        /// </summary>
        public int LastMentionChapter { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 章节总结
    /// </summary>
    public class ChapterSummaryTimeline
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 所属项目ID
        /// </summary>
        public int ProjectId { get; set; }

        /// <summary>
        /// 章节编号
        /// </summary>
        public int ChapterNumber { get; set; }

        /// <summary>
        /// 章节标题
        /// </summary>
        public string ChapterTitle { get; set; } = string.Empty;

        /// <summary>
        /// 主要事件总结
        /// </summary>
        public string MainEvents { get; set; } = string.Empty;

        /// <summary>
        /// 角色变化
        /// </summary>
        public string CharacterChanges { get; set; } = string.Empty;

        /// <summary>
        /// 势力变化
        /// </summary>
        public string FactionChanges { get; set; } = string.Empty;

        /// <summary>
        /// 世界状态变化
        /// </summary>
        public string WorldChanges { get; set; } = string.Empty;

        /// <summary>
        /// 重要伏笔
        /// </summary>
        public string? ImportantForeshadowing { get; set; }

        /// <summary>
        /// 故事时间跨度
        /// </summary>
        public string? TimeSpan { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 时间线事件类型枚举
    /// </summary>
    public enum TimelineEventType
    {
        Character,  // 角色事件
        Faction,    // 势力事件
        World,      // 世界事件
        Plot        // 情节事件
    }
}

using DocumentCreationSystem.Views.Dialogs;
using System.Windows;

namespace DocumentCreationSystem.Helpers
{
    /// <summary>
    /// 消息对话框帮助类，提供统一的消息显示接口
    /// </summary>
    public static class MessageHelper
    {
        /// <summary>
        /// 显示信息消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="details">详细信息</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowInformation(string message, string title = "信息", string? details = null, Window? owner = null)
        {
            return EnhancedMessageDialog.ShowInformation(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="details">详细信息</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowWarning(string message, string title = "警告", string? details = null, Window? owner = null)
        {
            return EnhancedMessageDialog.ShowWarning(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="details">详细信息</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowError(string message, string title = "错误", string? details = null, Window? owner = null)
        {
            return EnhancedMessageDialog.ShowError(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示确认消息
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="details">详细信息</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowConfirmation(string message, string title = "确认", string? details = null, Window? owner = null)
        {
            return EnhancedMessageDialog.ShowConfirmation(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示自定义消息对话框
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        /// <param name="buttons">按钮类型</param>
        /// <param name="icon">图标类型</param>
        /// <param name="details">详细信息</param>
        /// <param name="owner">父窗口</param>
        /// <param name="customContent">自定义内容</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowCustom(
            string message, 
            string title, 
            MessageBoxButton buttons, 
            MessageBoxImage icon, 
            string? details = null, 
            Window? owner = null,
            FrameworkElement? customContent = null)
        {
            return EnhancedMessageDialog.ShowDialog(message, title, buttons, icon, details, owner ?? GetActiveWindow(), customContent);
        }

        /// <summary>
        /// 显示带有异常详细信息的错误消息
        /// </summary>
        /// <param name="message">主要错误消息</param>
        /// <param name="exception">异常对象</param>
        /// <param name="title">标题</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowError(string message, Exception exception, string title = "错误", Window? owner = null)
        {
            var details = $"异常类型: {exception.GetType().Name}\n" +
                         $"异常消息: {exception.Message}\n" +
                         $"堆栈跟踪:\n{exception.StackTrace}";

            return EnhancedMessageDialog.ShowError(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示操作确认对话框
        /// </summary>
        /// <param name="operation">操作描述</param>
        /// <param name="consequences">操作后果描述</param>
        /// <param name="title">标题</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowOperationConfirmation(string operation, string consequences, string title = "确认操作", Window? owner = null)
        {
            var message = $"您即将执行以下操作：\n\n{operation}";
            var details = $"操作后果：\n{consequences}\n\n此操作可能无法撤销，请确认是否继续。";

            return EnhancedMessageDialog.ShowConfirmation(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示数据丢失警告确认对话框
        /// </summary>
        /// <param name="dataDescription">数据描述</param>
        /// <param name="title">标题</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowDataLossWarning(string dataDescription, string title = "数据丢失警告", Window? owner = null)
        {
            var message = $"此操作将导致数据丢失：\n\n{dataDescription}";
            var details = "丢失的数据无法恢复。建议在继续之前备份重要数据。\n\n确定要继续吗？";

            return EnhancedMessageDialog.ShowDialog(message, title, MessageBoxButton.YesNo, MessageBoxImage.Warning, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 显示进度完成通知
        /// </summary>
        /// <param name="taskDescription">任务描述</param>
        /// <param name="results">结果描述</param>
        /// <param name="title">标题</param>
        /// <param name="owner">父窗口</param>
        /// <returns>对话框结果</returns>
        public static MessageBoxResult ShowTaskCompleted(string taskDescription, string results, string title = "任务完成", Window? owner = null)
        {
            var message = $"任务已完成：{taskDescription}";
            var details = $"执行结果：\n{results}";

            return EnhancedMessageDialog.ShowInformation(message, title, details, owner ?? GetActiveWindow());
        }

        /// <summary>
        /// 获取当前活动窗口
        /// </summary>
        /// <returns>活动窗口或主窗口</returns>
        private static Window? GetActiveWindow()
        {
            try
            {
                // 尝试获取当前活动窗口
                var activeWindow = Application.Current.Windows.OfType<Window>().FirstOrDefault(w => w.IsActive);
                if (activeWindow != null)
                    return activeWindow;

                // 如果没有活动窗口，返回主窗口
                return Application.Current.MainWindow;
            }
            catch
            {
                return null;
            }
        }
    }
}

<Window x:Class="DocumentCreationSystem.Views.ToolParameterDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="工具参数设置" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <materialDesign:Card Grid.Row="0" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,0,0,16">
            <Grid Margin="24,16">
                <StackPanel Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="Settings" 
                                           VerticalAlignment="Center" 
                                           Width="24" Height="24"
                                           Margin="0,0,12,0"/>
                    <TextBlock x:Name="TitleText" 
                             Text="工具参数设置" 
                             FontSize="18" 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
        
        <!-- 参数列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel x:Name="ParametersPanel" Margin="24,0"/>
        </ScrollViewer>
        
        <!-- 按钮 -->
        <materialDesign:Card Grid.Row="2" materialDesign:ElevationAssist.Elevation="Dp1" Margin="0,16,0,0">
            <StackPanel Orientation="Horizontal" 
                      HorizontalAlignment="Right" 
                      Margin="24,16">
                <Button Content="取消" 
                      Style="{StaticResource MaterialDesignFlatButton}"
                      Margin="0,0,8,0"
                      Click="Cancel_Click"/>
                <Button Content="确定" 
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Click="OK_Click"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using DocumentCreationSystem.Services;
using DocumentCreationSystem.Models;

namespace DocumentCreationSystem.Views
{
    /// <summary>
    /// 一键写专利交底书对话框
    /// </summary>
    public partial class PatentDisclosureDialog : Window
    {
        private readonly ILogger<PatentDisclosureDialog> _logger;
        private readonly IPatentDisclosureService _patentDisclosureService;
        private readonly IProjectService _projectService;
        private readonly IFileFormatService _fileFormatService;
        private readonly List<string> _selectedMaterialFiles = new();
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isGenerating = false;

        public Project? CurrentProject { get; set; }

        public PatentDisclosureDialog(IServiceProvider serviceProvider)
        {
            InitializeComponent();
            
            _logger = serviceProvider.GetRequiredService<ILogger<PatentDisclosureDialog>>();
            _patentDisclosureService = serviceProvider.GetRequiredService<IPatentDisclosureService>();
            _projectService = serviceProvider.GetRequiredService<IProjectService>();
            _fileFormatService = serviceProvider.GetRequiredService<IFileFormatService>();

            InitializeUI();
        }

        private void InitializeUI()
        {
            // 绑定滑块值变化事件
            FormulaComplexitySlider.ValueChanged += (s, e) =>
            {
                var complexity = (int)e.NewValue;
                FormulaComplexityText.Text = complexity switch
                {
                    1 => "简单公式",
                    2 => "基础公式",
                    3 => "中等复杂度",
                    4 => "复杂公式",
                    5 => "高度复杂",
                    _ => "中等复杂度"
                };
            };

            TechnicalDepthSlider.ValueChanged += (s, e) =>
            {
                TechnicalDepthText.Text = e.NewValue.ToString("F1");
            };

            InnovationEmphasisSlider.ValueChanged += (s, e) =>
            {
                InnovationEmphasisText.Text = e.NewValue.ToString("F1");
            };

            UpdateStatus("准备就绪", "Information", Brushes.Green);
        }

        /// <summary>
        /// 选择技术素材文件
        /// </summary>
        private void SelectMaterialFiles_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择技术素材文件",
                    Filter = "支持的文档|*.txt;*.docx;*.md|文本文件|*.txt|Word文档|*.docx|Markdown文件|*.md|所有文件|*.*",
                    Multiselect = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    _selectedMaterialFiles.Clear();
                    _selectedMaterialFiles.AddRange(openFileDialog.FileNames);

                    // 更新界面显示
                    var fileNames = _selectedMaterialFiles.Select(Path.GetFileName);
                    MaterialFilesTextBox.Text = string.Join("\n", fileNames);
                    MaterialFilesCountText.Text = $"已选择 {_selectedMaterialFiles.Count} 个文件";

                    _logger.LogInformation($"用户选择了 {_selectedMaterialFiles.Count} 个技术素材文件");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择技术素材文件时发生错误");
                MessageBox.Show($"选择文件时发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 开始生成专利交底书
        /// </summary>
        private async void StartGeneration_Click(object sender, RoutedEventArgs e)
        {
            if (_isGenerating)
            {
                // 如果正在生成，则停止
                _cancellationTokenSource?.Cancel();
                return;
            }

            try
            {
                // 验证输入
                if (!ValidateInputs())
                {
                    return;
                }

                _isGenerating = true;
                _cancellationTokenSource = new CancellationTokenSource();
                
                // 更新UI状态
                StartButton.Content = "停止生成";
                StartButton.Background = Brushes.OrangeRed;
                UpdateStatus("正在生成专利交底书...", "Loading", Brushes.Orange);

                // 构建专利生成请求
                var request = BuildPatentGenerationRequest();

                // 开始生成专利交底书
                await GeneratePatentDisclosureAsync(request, _cancellationTokenSource.Token);

                if (!_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    UpdateStatus("专利交底书生成完成！", "CheckCircle", Brushes.Green);
                    MessageBox.Show("专利交底书生成完成！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (OperationCanceledException)
            {
                UpdateStatus("生成已取消", "Cancel", Brushes.Gray);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成专利交底书时发生错误");
                UpdateStatus("生成失败", "Error", Brushes.Red);
                MessageBox.Show($"生成专利交底书时发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isGenerating = false;
                StartButton.Content = "开始生成专利交底书";
                StartButton.Background = SystemColors.ControlBrush;
            }
        }

        /// <summary>
        /// 验证输入参数
        /// </summary>
        private bool ValidateInputs()
        {
            if (_selectedMaterialFiles.Count == 0)
            {
                MessageBox.Show("请至少选择一个技术素材文件", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(ApplicantTextBox.Text))
            {
                MessageBox.Show("请输入申请人信息", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(InventorTextBox.Text))
            {
                MessageBox.Show("请输入发明人信息", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 构建专利生成请求
        /// </summary>
        private PatentDisclosureRequest BuildPatentGenerationRequest()
        {
            return new PatentDisclosureRequest
            {
                InventionTitle = string.IsNullOrWhiteSpace(InventionTitleTextBox.Text) ? null : InventionTitleTextBox.Text,
                PatentType = ((ComboBoxItem)PatentTypeComboBox.SelectedItem).Content.ToString()!,
                TechnicalField = ((ComboBoxItem)TechnicalFieldComboBox.SelectedItem).Content.ToString()!,
                Applicant = ApplicantTextBox.Text,
                Inventor = InventorTextBox.Text,
                FormulaComplexity = (int)FormulaComplexitySlider.Value,
                TechnicalDepth = (float)TechnicalDepthSlider.Value,
                InnovationEmphasis = (float)InnovationEmphasisSlider.Value,
                MaterialFiles = _selectedMaterialFiles.ToList(),
                Structure = new PatentStructure
                {
                    IncludeTechnicalField = IncludeTechnicalFieldCheckBox.IsChecked == true,
                    IncludeBackground = IncludeBackgroundCheckBox.IsChecked == true,
                    IncludeSummary = IncludeSummaryCheckBox.IsChecked == true,
                    IncludeDrawings = IncludeDrawingsCheckBox.IsChecked == true,
                    IncludeDetailedDescription = IncludeDetailedDescriptionCheckBox.IsChecked == true,
                    IncludeClaims = IncludeClaimsCheckBox.IsChecked == true,
                    IncludeAbstract = IncludeAbstractCheckBox.IsChecked == true
                },
                FormulaSettings = new FormulaSettings
                {
                    IncludeAlgorithmFormulas = IncludeAlgorithmFormulasCheckBox.IsChecked == true,
                    IncludePerformanceFormulas = IncludePerformanceFormulasCheckBox.IsChecked == true,
                    IncludeOptimizationFormulas = IncludeOptimizationFormulasCheckBox.IsChecked == true,
                    IncludeStatisticalFormulas = IncludeStatisticalFormulasCheckBox.IsChecked == true,
                    IncludeModelFormulas = IncludeModelFormulasCheckBox.IsChecked == true
                },
                OutputFormat = GetSelectedOutputFormat(),
                SaveToProject = SaveToProjectCheckBox.IsChecked == true,
                GenerateDrawingPlaceholders = GenerateDrawingPlaceholdersCheckBox.IsChecked == true,
                UserRequirements = string.IsNullOrWhiteSpace(UserRequirementsTextBox.Text) ? null : UserRequirementsTextBox.Text
            };
        }

        /// <summary>
        /// 获取选择的输出格式
        /// </summary>
        private string GetSelectedOutputFormat()
        {
            var selectedItem = (ComboBoxItem)OutputFormatComboBox.SelectedItem;
            var content = selectedItem.Content.ToString()!;
            
            if (content.Contains(".docx")) return "docx";
            if (content.Contains(".txt")) return "txt";
            if (content.Contains(".md")) return "md";
            
            return "docx"; // 默认格式
        }

        /// <summary>
        /// 生成专利交底书
        /// </summary>
        private async Task GeneratePatentDisclosureAsync(PatentDisclosureRequest request, CancellationToken cancellationToken)
        {
            try
            {
                // 设置进度回调
                var progress = new Progress<string>(message =>
                {
                    Dispatcher.Invoke(() => UpdateStatus(message, "Loading", Brushes.Orange));
                });

                // 调用专利写作服务
                var result = await _patentDisclosureService.GeneratePatentDisclosureAsync(request, progress, cancellationToken);

                if (result.IsSuccess && !string.IsNullOrEmpty(result.Content))
                {
                    // 保存专利交底书
                    await SavePatentDisclosureAsync(result, request);
                }
                else
                {
                    throw new Exception(result.ErrorMessage ?? "生成专利交底书失败");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成专利交底书过程中发生错误");
                throw;
            }
        }

        /// <summary>
        /// 保存专利交底书
        /// </summary>
        private async Task SavePatentDisclosureAsync(PatentDisclosureResult result, PatentDisclosureRequest request)
        {
            try
            {
                string fileName = $"{result.InventionTitle ?? "专利交底书"}_{DateTime.Now:yyyyMMdd_HHmmss}.{request.OutputFormat}";
                string filePath;

                if (request.SaveToProject && CurrentProject != null)
                {
                    // 保存到项目文件夹
                    var patentFolder = Path.Combine(CurrentProject.RootPath, "专利文档");
                    Directory.CreateDirectory(patentFolder);
                    filePath = Path.Combine(patentFolder, fileName);
                }
                else
                {
                    // 让用户选择保存位置
                    var saveFileDialog = new SaveFileDialog
                    {
                        FileName = fileName,
                        Filter = GetSaveFileFilter(request.OutputFormat),
                        DefaultExt = request.OutputFormat
                    };

                    if (saveFileDialog.ShowDialog() != true)
                    {
                        return;
                    }

                    filePath = saveFileDialog.FileName;
                }

                // 根据格式保存文件
                var fileFormat = GetFileFormat(request.OutputFormat);
                await _fileFormatService.SaveContentAsync(filePath, result.Content, fileFormat);

                _logger.LogInformation($"专利交底书已保存到: {filePath}");
                UpdateStatus($"专利交底书已保存: {Path.GetFileName(filePath)}", "CheckCircle", Brushes.Green);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存专利交底书时发生错误");
                throw new Exception($"保存专利交底书失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取保存文件对话框的过滤器
        /// </summary>
        private string GetSaveFileFilter(string format)
        {
            return format.ToLower() switch
            {
                "docx" => "Word文档|*.docx",
                "txt" => "文本文件|*.txt",
                "md" => "Markdown文件|*.md",
                _ => "所有文件|*.*"
            };
        }

        /// <summary>
        /// 获取文件格式枚举
        /// </summary>
        private Services.FileFormat GetFileFormat(string format)
        {
            return format.ToLower() switch
            {
                "docx" => Services.FileFormat.Docx,
                "txt" => Services.FileFormat.Text,
                "md" => Services.FileFormat.Markdown,
                _ => Services.FileFormat.Text
            };
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string message, string iconKind, Brush color)
        {
            StatusText.Text = message;
            StatusText.Foreground = color;
            // 这里可以根据iconKind设置不同的图标
        }

        /// <summary>
        /// 取消按钮
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            if (_isGenerating)
            {
                _cancellationTokenSource?.Cancel();
            }
            
            Close();
        }
    }
}

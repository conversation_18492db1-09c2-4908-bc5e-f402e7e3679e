using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models.Reasoning
{
    /// <summary>
    /// 推理事实 - 表示知识库中的一个事实
    /// </summary>
    public class ReasoningFact
    {
        /// <summary>
        /// 事实唯一标识
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 事实主体（主语）
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// 事实谓词（谓语）
        /// </summary>
        public string Predicate { get; set; } = string.Empty;

        /// <summary>
        /// 事实客体（宾语）
        /// </summary>
        public string Object { get; set; } = string.Empty;

        /// <summary>
        /// 事实的置信度 (0.0 - 1.0)
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 事实的时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;

        /// <summary>
        /// 事实来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 事实类型
        /// </summary>
        public FactType Type { get; set; } = FactType.Assertion;

        /// <summary>
        /// 事实的上下文信息
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// 事实的标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 事实是否为临时事实
        /// </summary>
        public bool IsTemporary { get; set; } = false;

        /// <summary>
        /// 事实的过期时间（如果是临时事实）
        /// </summary>
        public DateTime? ExpiryTime { get; set; }

        /// <summary>
        /// 事实的权重（用于推理优先级）
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        public override string ToString()
        {
            return $"{Subject} {Predicate} {Object}";
        }

        /// <summary>
        /// 检查事实是否已过期
        /// </summary>
        public bool IsExpired => IsTemporary && ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;
    }

    /// <summary>
    /// 事实类型枚举
    /// </summary>
    public enum FactType
    {
        /// <summary>
        /// 断言事实
        /// </summary>
        Assertion,

        /// <summary>
        /// 假设事实
        /// </summary>
        Hypothesis,

        /// <summary>
        /// 推导事实
        /// </summary>
        Derived,

        /// <summary>
        /// 观察事实
        /// </summary>
        Observation,

        /// <summary>
        /// 规则事实
        /// </summary>
        Rule,

        /// <summary>
        /// 约束事实
        /// </summary>
        Constraint
    }

    /// <summary>
    /// 推理规则 - 表示if-then形式的推理规则
    /// </summary>
    public class ReasoningRule
    {
        /// <summary>
        /// 规则唯一标识
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 规则条件（前件）
        /// </summary>
        public List<RuleCondition> Conditions { get; set; } = new();

        /// <summary>
        /// 规则结论（后件）
        /// </summary>
        public List<RuleConclusion> Conclusions { get; set; } = new();

        /// <summary>
        /// 规则优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 规则置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;

        /// <summary>
        /// 规则是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则类型
        /// </summary>
        public RuleType Type { get; set; } = RuleType.Deductive;

        /// <summary>
        /// 规则的使用次数
        /// </summary>
        public int UsageCount { get; set; } = 0;

        /// <summary>
        /// 规则的成功率
        /// </summary>
        public double SuccessRate { get; set; } = 1.0;

        /// <summary>
        /// 规则创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 规则最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// 规则标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 规则的上下文约束
        /// </summary>
        public Dictionary<string, object> ContextConstraints { get; set; } = new();
    }

    /// <summary>
    /// 规则类型枚举
    /// </summary>
    public enum RuleType
    {
        /// <summary>
        /// 演绎推理规则
        /// </summary>
        Deductive,

        /// <summary>
        /// 归纳推理规则
        /// </summary>
        Inductive,

        /// <summary>
        /// 溯因推理规则
        /// </summary>
        Abductive,

        /// <summary>
        /// 默认推理规则
        /// </summary>
        Default,

        /// <summary>
        /// 概率推理规则
        /// </summary>
        Probabilistic,

        /// <summary>
        /// 模糊推理规则
        /// </summary>
        Fuzzy
    }

    /// <summary>
    /// 规则条件
    /// </summary>
    public class RuleCondition
    {
        /// <summary>
        /// 条件ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 条件类型
        /// </summary>
        public ConditionType Type { get; set; } = ConditionType.FactMatch;

        /// <summary>
        /// 条件模式（用于匹配事实）
        /// </summary>
        public FactPattern Pattern { get; set; } = new();

        /// <summary>
        /// 条件操作符
        /// </summary>
        public ConditionOperator Operator { get; set; } = ConditionOperator.Equals;

        /// <summary>
        /// 条件值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 条件是否为否定
        /// </summary>
        public bool IsNegated { get; set; } = false;

        /// <summary>
        /// 条件权重
        /// </summary>
        public double Weight { get; set; } = 1.0;
    }

    /// <summary>
    /// 条件类型枚举
    /// </summary>
    public enum ConditionType
    {
        /// <summary>
        /// 事实匹配
        /// </summary>
        FactMatch,

        /// <summary>
        /// 数值比较
        /// </summary>
        NumericComparison,

        /// <summary>
        /// 字符串匹配
        /// </summary>
        StringMatch,

        /// <summary>
        /// 正则表达式匹配
        /// </summary>
        RegexMatch,

        /// <summary>
        /// 函数调用
        /// </summary>
        FunctionCall,

        /// <summary>
        /// 上下文检查
        /// </summary>
        ContextCheck
    }

    /// <summary>
    /// 条件操作符枚举
    /// </summary>
    public enum ConditionOperator
    {
        Equals,
        NotEquals,
        GreaterThan,
        LessThan,
        GreaterThanOrEqual,
        LessThanOrEqual,
        Contains,
        StartsWith,
        EndsWith,
        Matches,
        In,
        NotIn
    }

    /// <summary>
    /// 事实模式 - 用于匹配事实的模式
    /// </summary>
    public class FactPattern
    {
        /// <summary>
        /// 主体模式
        /// </summary>
        public string? SubjectPattern { get; set; }

        /// <summary>
        /// 谓词模式
        /// </summary>
        public string? PredicatePattern { get; set; }

        /// <summary>
        /// 客体模式
        /// </summary>
        public string? ObjectPattern { get; set; }

        /// <summary>
        /// 是否使用正则表达式
        /// </summary>
        public bool UseRegex { get; set; } = false;

        /// <summary>
        /// 是否忽略大小写
        /// </summary>
        public bool IgnoreCase { get; set; } = true;
    }

    /// <summary>
    /// 规则结论
    /// </summary>
    public class RuleConclusion
    {
        /// <summary>
        /// 结论ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 结论类型
        /// </summary>
        public ConclusionType Type { get; set; } = ConclusionType.AddFact;

        /// <summary>
        /// 要添加的事实（如果类型是AddFact）
        /// </summary>
        public ReasoningFact? NewFact { get; set; }

        /// <summary>
        /// 要执行的动作（如果类型是ExecuteAction）
        /// </summary>
        public string? Action { get; set; }

        /// <summary>
        /// 动作参数
        /// </summary>
        public Dictionary<string, object> ActionParameters { get; set; } = new();

        /// <summary>
        /// 结论置信度
        /// </summary>
        public double Confidence { get; set; } = 1.0;
    }

    /// <summary>
    /// 结论类型枚举
    /// </summary>
    public enum ConclusionType
    {
        /// <summary>
        /// 添加新事实
        /// </summary>
        AddFact,

        /// <summary>
        /// 删除事实
        /// </summary>
        RemoveFact,

        /// <summary>
        /// 更新事实
        /// </summary>
        UpdateFact,

        /// <summary>
        /// 执行动作
        /// </summary>
        ExecuteAction,

        /// <summary>
        /// 触发事件
        /// </summary>
        TriggerEvent
    }

    /// <summary>
    /// 事实查询条件
    /// </summary>
    public class FactQuery
    {
        /// <summary>
        /// 查询ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 主体查询条件
        /// </summary>
        public string? Subject { get; set; }

        /// <summary>
        /// 谓词查询条件
        /// </summary>
        public string? Predicate { get; set; }

        /// <summary>
        /// 客体查询条件
        /// </summary>
        public string? Object { get; set; }

        /// <summary>
        /// 事实类型过滤
        /// </summary>
        public FactType? Type { get; set; }

        /// <summary>
        /// 最小置信度
        /// </summary>
        public double? MinConfidence { get; set; }

        /// <summary>
        /// 最大置信度
        /// </summary>
        public double? MaxConfidence { get; set; }

        /// <summary>
        /// 时间范围开始
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 时间范围结束
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 标签过滤
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 是否包含过期事实
        /// </summary>
        public bool IncludeExpired { get; set; } = false;

        /// <summary>
        /// 最大结果数
        /// </summary>
        public int MaxResults { get; set; } = 100;

        /// <summary>
        /// 排序方式
        /// </summary>
        public FactSortOrder SortOrder { get; set; } = FactSortOrder.ByTimestamp;
    }

    /// <summary>
    /// 事实排序方式
    /// </summary>
    public enum FactSortOrder
    {
        ByTimestamp,
        ByConfidence,
        ByWeight,
        ByRelevance
    }

    /// <summary>
    /// 推理查询
    /// </summary>
    public class ReasoningQuery
    {
        /// <summary>
        /// 查询ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 查询目标
        /// </summary>
        public ReasoningFact Goal { get; set; } = new();

        /// <summary>
        /// 推理类型
        /// </summary>
        public ReasoningType Type { get; set; } = ReasoningType.Forward;

        /// <summary>
        /// 最大推理深度
        /// </summary>
        public int MaxDepth { get; set; } = 10;

        /// <summary>
        /// 最小置信度阈值
        /// </summary>
        public double MinConfidence { get; set; } = 0.5;

        /// <summary>
        /// 推理上下文
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// 是否返回推理链
        /// </summary>
        public bool IncludeReasoningChain { get; set; } = true;

        /// <summary>
        /// 超时时间（毫秒）
        /// </summary>
        public int TimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// 推理类型枚举
    /// </summary>
    public enum ReasoningType
    {
        /// <summary>
        /// 前向推理
        /// </summary>
        Forward,

        /// <summary>
        /// 后向推理
        /// </summary>
        Backward,

        /// <summary>
        /// 双向推理
        /// </summary>
        Bidirectional,

        /// <summary>
        /// 概率推理
        /// </summary>
        Probabilistic,

        /// <summary>
        /// 模糊推理
        /// </summary>
        Fuzzy,

        /// <summary>
        /// 类比推理
        /// </summary>
        Analogical,

        /// <summary>
        /// 因果推理
        /// </summary>
        Causal
    }

    /// <summary>
    /// 推理结果
    /// </summary>
    public class ReasoningResult
    {
        /// <summary>
        /// 结果ID
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 查询ID
        /// </summary>
        public string QueryId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = false;

        /// <summary>
        /// 推导出的事实
        /// </summary>
        public List<ReasoningFact> DerivedFacts { get; set; } = new();

        /// <summary>
        /// 推理链
        /// </summary>
        public ReasoningChain? ReasoningChain { get; set; }

        /// <summary>
        /// 总体置信度
        /// </summary>
        public double Confidence { get; set; } = 0.0;

        /// <summary>
        /// 推理时间（毫秒）
        /// </summary>
        public long ReasoningTimeMs { get; set; } = 0;

        /// <summary>
        /// 使用的规则数量
        /// </summary>
        public int RulesUsed { get; set; } = 0;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 推理统计信息
        /// </summary>
        public Dictionary<string, object> Statistics { get; set; } = new();

        /// <summary>
        /// 推理时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}

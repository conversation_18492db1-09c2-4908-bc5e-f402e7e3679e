<Window x:Class="DocumentCreationSystem.Views.RenameDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="重命名"
        Height="200"
        Width="400"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Style="{StaticResource MaterialDesignWindow}"
        Background="{DynamicResource MaterialDesignPaper}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="RenameBox" 
                                   VerticalAlignment="Center" 
                                   Margin="0,0,8,0"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            <TextBlock x:Name="TitleText" 
                     Text="重命名文件" 
                     FontSize="16" 
                     FontWeight="Medium"
                     VerticalAlignment="Center"/>
        </StackPanel>

        <!-- 输入区域 -->
        <StackPanel Grid.Row="1">
            <TextBlock Text="新名称:" Margin="0,0,0,8" FontWeight="Medium"/>
            <TextBox x:Name="NameTextBox"
                   materialDesign:HintAssist.Hint="请输入新的名称"
                   Style="{StaticResource MaterialDesignOutlinedTextBox}"
                   FontSize="14"
                   Padding="12,8"
                   KeyDown="NameTextBox_KeyDown"/>
        </StackPanel>

        <!-- 提示信息 -->
        <TextBlock Grid.Row="2" 
                 x:Name="HintText"
                 Text="提示：文件名不能包含以下字符：\ / : * ? &quot; &lt; &gt; |"
                 FontSize="12"
                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                 TextWrapping="Wrap"
                 Margin="0,10,0,0"
                 VerticalAlignment="Top"/>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3" 
                  Orientation="Horizontal" 
                  HorizontalAlignment="Right" 
                  Margin="0,20,0,0">
            <Button Content="取消" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Margin="0,0,10,0"
                  Padding="16,8"
                  Click="Cancel_Click"/>
            <Button x:Name="OkButton"
                  Content="确定" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Padding="16,8"
                  Click="Ok_Click"/>
        </StackPanel>
    </Grid>
</Window>

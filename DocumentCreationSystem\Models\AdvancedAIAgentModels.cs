using System.ComponentModel.DataAnnotations;

namespace DocumentCreationSystem.Models
{
    // ==================== 高级AI Agent相关模型 ====================

    /// <summary>
    /// Agent请求
    /// </summary>
    public class AgentRequest
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string UserId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public AgentRequestType RequestType { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public Dictionary<string, object> Context { get; set; } = new();
        public int Priority { get; set; } = 5;
        public TimeSpan? Timeout { get; set; }
        public List<string> RequiredCapabilities { get; set; } = new();
    }

    /// <summary>
    /// Agent请求类型
    /// </summary>
    public enum AgentRequestType
    {
        Information,    // 信息查询
        Task,          // 任务执行
        Creative,      // 创意协助
        Analysis,      // 分析处理
        Decision,      // 决策支持
        Learning,      // 学习指导
        Collaboration, // 协作支持
        General        // 通用请求
    }

    /// <summary>
    /// Agent响应
    /// </summary>
    public class AgentResponse
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string RequestId { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public AgentResponseType ResponseType { get; set; }
        public DateTime GeneratedAt { get; set; }
        public float Confidence { get; set; } = 1.0f;
        public List<string> Suggestions { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public List<AgentAction> RecommendedActions { get; set; } = new();
    }

    /// <summary>
    /// Agent响应类型
    /// </summary>
    public enum AgentResponseType
    {
        Success,
        Partial,
        Error,
        Redirect,
        RequiresInput,
        Processing
    }

    /// <summary>
    /// Agent动作
    /// </summary>
    public class AgentAction
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public ActionType Type { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public int Priority { get; set; } = 5;
        public bool RequiresConfirmation { get; set; } = false;
    }

    /// <summary>
    /// 动作类型
    /// </summary>
    public enum ActionType
    {
        CreateDocument,
        EditContent,
        SearchInformation,
        AnalyzeData,
        GenerateReport,
        SendNotification,
        ScheduleTask,
        UpdateSettings
    }

    /// <summary>
    /// Agent实例
    /// </summary>
    public class AgentInstance
    {
        public string Id { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime LastActiveAt { get; set; }
        public AgentMemory Memory { get; set; } = new();
        public PersonalizationProfile PersonalizationProfile { get; set; } = new();
        public Dictionary<string, object> CurrentContext { get; set; } = new();
        public List<ContextSnapshot> ContextHistory { get; set; } = new();
        public AgentStatus Status { get; set; } = AgentStatus.Active;
        public Dictionary<string, float> CapabilityScores { get; set; } = new();
    }

    /// <summary>
    /// Agent状态
    /// </summary>
    public enum AgentStatus
    {
        Active,
        Idle,
        Learning,
        Processing,
        Maintenance,
        Offline
    }

    /// <summary>
    /// 上下文快照
    /// </summary>
    public class ContextSnapshot
    {
        public DateTime Timestamp { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
        public AgentRequestType RequestType { get; set; }
        public string? Summary { get; set; }
    }

    /// <summary>
    /// Agent记忆
    /// </summary>
    public class AgentMemory
    {
        public string AgentId { get; set; } = string.Empty;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public List<MemoryFact> Facts { get; set; } = new();
        public List<MemoryExperience> Experiences { get; set; } = new();
        public Dictionary<string, object> Preferences { get; set; } = new();
        public List<MemoryPattern> Patterns { get; set; } = new();
        public Dictionary<string, float> ConceptWeights { get; set; } = new();
    }

    /// <summary>
    /// 记忆事实
    /// </summary>
    public class MemoryFact
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Content { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public float Importance { get; set; } = 0.5f;
        public float Confidence { get; set; } = 1.0f;
        public List<string> Tags { get; set; } = new();
        public string? Source { get; set; }
    }

    /// <summary>
    /// 记忆经验
    /// </summary>
    public class MemoryExperience
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public float Importance { get; set; } = 0.5f;
        public string? Outcome { get; set; }
        public List<string> LessonsLearned { get; set; } = new();
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 记忆模式
    /// </summary>
    public class MemoryPattern
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PatternType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public float Strength { get; set; } = 0.5f;
        public int Occurrences { get; set; } = 1;
        public DateTime FirstObserved { get; set; }
        public DateTime LastObserved { get; set; }
        public List<string> Examples { get; set; } = new();
    }

    /// <summary>
    /// 记忆更新
    /// </summary>
    public class MemoryUpdate
    {
        public MemoryUpdateType UpdateType { get; set; }
        public string Content { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public float Importance { get; set; } = 0.5f;
        public string Key { get; set; } = string.Empty;
        public object? Value { get; set; }
        public string? Outcome { get; set; }
        public List<string> LessonsLearned { get; set; } = new();
    }

    /// <summary>
    /// 记忆更新类型
    /// </summary>
    public enum MemoryUpdateType
    {
        AddFact,
        AddExperience,
        UpdatePreference,
        AddPattern,
        UpdateWeight
    }

    /// <summary>
    /// 交互数据
    /// </summary>
    public class InteractionData
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string AgentId { get; set; } = string.Empty;
        public AgentRequest Request { get; set; } = new();
        public AgentResponse Response { get; set; } = new();
        public DateTime Timestamp { get; set; }
        public TimeSpan Duration { get; set; }
        public float? UserSatisfaction { get; set; }
        public string? UserFeedback { get; set; }
        public Dictionary<string, object> Metrics { get; set; } = new();
    }

    /// <summary>
    /// 学习结果
    /// </summary>
    public class LearningResult
    {
        public string AgentId { get; set; } = string.Empty;
        public string InteractionId { get; set; } = string.Empty;
        public DateTime LearnedAt { get; set; } = DateTime.Now;
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
        public List<InteractionPattern> IdentifiedPatterns { get; set; } = new();
        public List<KnowledgeItem> ExtractedKnowledge { get; set; } = new();
        public List<CapabilityUpdate> CapabilityUpdates { get; set; } = new();
        public List<StrategyAdjustment> StrategyAdjustments { get; set; } = new();
        public float LearningScore { get; set; } = 0.5f;
    }

    /// <summary>
    /// 交互模式
    /// </summary>
    public class InteractionPattern
    {
        public string PatternType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public float Confidence { get; set; } = 0.5f;
        public int Frequency { get; set; } = 1;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 知识项
    /// </summary>
    public class KnowledgeItem
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Content { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public float Confidence { get; set; } = 0.5f;
        public string Source { get; set; } = string.Empty;
        public List<string> RelatedConcepts { get; set; } = new();
    }

    /// <summary>
    /// 能力更新
    /// </summary>
    public class CapabilityUpdate
    {
        public string CapabilityName { get; set; } = string.Empty;
        public float OldScore { get; set; }
        public float NewScore { get; set; }
        public string Reason { get; set; } = string.Empty;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 策略调整
    /// </summary>
    public class StrategyAdjustment
    {
        public string StrategyName { get; set; } = string.Empty;
        public string AdjustmentType { get; set; } = string.Empty;
        public Dictionary<string, object> OldParameters { get; set; } = new();
        public Dictionary<string, object> NewParameters { get; set; } = new();
        public string Reason { get; set; } = string.Empty;
        public DateTime AdjustedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 个性化档案
    /// </summary>
    public class PersonalizationProfile
    {
        public string UserId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
        public string CommunicationStyle { get; set; } = "professional";
        public string PreferredResponseLength { get; set; } = "medium";
        public Dictionary<string, float> PersonalityTraits { get; set; } = new();
        public List<string> PreferredTopics { get; set; } = new();
        public List<string> AvoidedTopics { get; set; } = new();
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }

    /// <summary>
    /// Agent能力
    /// </summary>
    public class AgentCapability
    {
        public string AgentId { get; set; } = string.Empty;
        public string TaskType { get; set; } = string.Empty;
        public DateTime AssessedAt { get; set; }
        public float PerformanceScore { get; set; } = 0.5f;
        public float KnowledgeScore { get; set; } = 0.5f;
        public float LearningScore { get; set; } = 0.5f;
        public float OverallScore { get; set; } = 0.5f;
        public List<string> CapabilityGaps { get; set; } = new();
        public List<string> ImprovementSuggestions { get; set; } = new();
        public Dictionary<string, float> DetailedScores { get; set; } = new();
    }

    /// <summary>
    /// Agent洞察
    /// </summary>
    public class AgentInsight
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public InsightType Type { get; set; }
        public float Importance { get; set; } = 0.5f;
        public float Confidence { get; set; } = 0.5f;
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public List<string> SupportingEvidence { get; set; } = new();
        public List<string> RecommendedActions { get; set; } = new();
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// 洞察类型
    /// </summary>
    public enum InsightType
    {
        Progress,       // 进度洞察
        Behavior,       // 行为洞察
        Quality,        // 质量洞察
        Efficiency,     // 效率洞察
        Collaboration,  // 协作洞察
        Trend,          // 趋势洞察
        Risk,           // 风险洞察
        Opportunity     // 机会洞察
    }

    /// <summary>
    /// Agent决策
    /// </summary>
    public class AgentDecision
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string AgentId { get; set; } = string.Empty;
        public DecisionContext Context { get; set; } = new();
        public DateTime DecisionTime { get; set; }
        public List<DecisionOption> AvailableOptions { get; set; } = new();
        public List<OptionEvaluation> OptionEvaluations { get; set; } = new();
        public DecisionOption? SelectedOption { get; set; }
        public float Confidence { get; set; } = 0.5f;
        public string Reasoning { get; set; } = string.Empty;
        public List<string> IdentifiedRisks { get; set; } = new();
        public ExecutionPlan? ExecutionPlan { get; set; }
        public bool Success { get; set; } = true;
        public string? ErrorMessage { get; set; }
    }



    /// <summary>
    /// 执行计划
    /// </summary>
    public class ExecutionPlan
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public List<ExecutionStep> Steps { get; set; } = new();
        public DateTime PlannedStartTime { get; set; }
        public DateTime PlannedEndTime { get; set; }
        public Dictionary<string, object> Resources { get; set; } = new();
        public List<string> Dependencies { get; set; } = new();
        public List<string> RiskMitigations { get; set; } = new();
    }

    /// <summary>
    /// 执行步骤
    /// </summary>
    public class ExecutionStep
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Order { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public List<string> Prerequisites { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public ExecutionStepStatus Status { get; set; } = ExecutionStepStatus.Pending;
    }

    /// <summary>
    /// 执行步骤状态
    /// </summary>
    public enum ExecutionStepStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Skipped
    }

    /// <summary>
    /// Agent建议
    /// </summary>
    public class AgentSuggestion
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public SuggestionType Type { get; set; }
        public int Priority { get; set; } = 5;
        public float Confidence { get; set; } = 0.5f;
        public DateTime GeneratedAt { get; set; } = DateTime.Now;
        public List<string> Benefits { get; set; } = new();
        public List<AgentAction> SuggestedActions { get; set; } = new();
        public Dictionary<string, object> Context { get; set; } = new();
        public bool IsProactive { get; set; } = true;
    }

    /// <summary>
    /// 建议类型
    /// </summary>
    public enum SuggestionType
    {
        Productivity,   // 生产力提升
        Quality,        // 质量改进
        Learning,       // 学习机会
        Collaboration,  // 协作建议
        Efficiency,     // 效率优化
        Innovation,     // 创新建议
        Risk,           // 风险提醒
        Opportunity     // 机会提醒
    }

    /// <summary>
    /// 请求意图
    /// </summary>
    public class RequestIntent
    {
        public string IntentType { get; set; } = string.Empty;
        public float Confidence { get; set; } = 0.5f;
        public string Description { get; set; } = string.Empty;
        public List<string> RequiredCapabilities { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 决策信息
    /// </summary>
    public class DecisionInformation
    {
        public Dictionary<string, object> FactualData { get; set; } = new();
        public List<string> RelevantExperiences { get; set; } = new();
        public Dictionary<string, float> RiskFactors { get; set; } = new();
        public List<string> Constraints { get; set; } = new();
        public Dictionary<string, object> EnvironmentalFactors { get; set; } = new();
    }

    /// <summary>
    /// 性能分析
    /// </summary>
    public class PerformanceAnalysis
    {
        public float AverageScore { get; set; } = 0.5f;
        public int TotalInteractions { get; set; } = 0;
        public Dictionary<string, float> TaskTypeScores { get; set; } = new();
        public List<string> StrengthAreas { get; set; } = new();
        public List<string> ImprovementAreas { get; set; } = new();
        public DateTime AnalyzedAt { get; set; } = DateTime.Now;
    }

    // ==================== 插件生态系统相关模型 ====================

    /// <summary>
    /// 插件信息
    /// </summary>
    public class PluginInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public PluginStatus Status { get; set; } = PluginStatus.Disabled;
        public DateTime InstallDate { get; set; }
        public DateTime LastUpdateDate { get; set; }
        public string InstallPath { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
        public PluginManifest? Manifest { get; set; }
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 插件状态
    /// </summary>
    public enum PluginStatus
    {
        Disabled,       // 已禁用
        Enabled,        // 已启用
        Installing,     // 安装中
        Uninstalling,   // 卸载中
        Error,          // 错误状态
        Updating        // 更新中
    }

    /// <summary>
    /// 插件清单
    /// </summary>
    public class PluginManifest
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Author { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string EntryPoint { get; set; } = string.Empty;
        public List<string> Permissions { get; set; } = new();
        public List<PluginDependencyInfo> Dependencies { get; set; } = new();
        public PluginCompatibility Compatibility { get; set; } = new();
        public Dictionary<string, object> Configuration { get; set; } = new();
        public List<PluginHookInfo> Hooks { get; set; } = new();
        public List<PluginActionInfo> Actions { get; set; } = new();
    }

    /// <summary>
    /// 插件依赖信息
    /// </summary>
    public class PluginDependencyInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public bool IsOptional { get; set; } = false;
        public string? Source { get; set; }
    }

    /// <summary>
    /// 插件兼容性
    /// </summary>
    public class PluginCompatibility
    {
        public string MinSystemVersion { get; set; } = string.Empty;
        public string MaxSystemVersion { get; set; } = string.Empty;
        public List<string> SupportedPlatforms { get; set; } = new();
        public Dictionary<string, string> RequiredFeatures { get; set; } = new();
    }

    /// <summary>
    /// 插件钩子信息
    /// </summary>
    public class PluginHookInfo
    {
        public string Id { get; set; } = string.Empty;
        public string HookPoint { get; set; } = string.Empty;
        public string Handler { get; set; } = string.Empty;
        public int Priority { get; set; } = 5;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 插件动作信息
    /// </summary>
    public class PluginActionInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Handler { get; set; } = string.Empty;
        public List<PluginParameterInfo> Parameters { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 插件参数信息
    /// </summary>
    public class PluginParameterInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public bool IsRequired { get; set; } = false;
        public object? DefaultValue { get; set; }
        public string? Description { get; set; }
        public Dictionary<string, object> Constraints { get; set; } = new();
    }

    /// <summary>
    /// 插件安装结果
    /// </summary>
    public class PluginInstallResult
    {
        public string PluginId { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public bool Success { get; set; } = false;
        public string? ErrorMessage { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public PluginInfo? InstalledPlugin { get; set; }
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> InstallationData { get; set; } = new();
    }

    /// <summary>
    /// 插件执行结果
    /// </summary>
    public class PluginExecutionResult
    {
        public bool Success { get; set; } = false;
        public object? Result { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public Dictionary<string, object> OutputData { get; set; } = new();
        public List<string> Logs { get; set; } = new();
    }

    /// <summary>
    /// 插件钩子
    /// </summary>
    public class PluginHook
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string PluginId { get; set; } = string.Empty;
        public string HookPoint { get; set; } = string.Empty;
        public string Handler { get; set; } = string.Empty;
        public int Priority { get; set; } = 5;
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public Func<Dictionary<string, object>, Task<object?>>? HandlerFunction { get; set; }
    }

    /// <summary>
    /// 插件配置
    /// </summary>
    public class PluginConfiguration
    {
        public string PluginId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
        public Dictionary<string, object> Settings { get; set; } = new();
        public Dictionary<string, object> UserPreferences { get; set; } = new();
        public bool IsEnabled { get; set; } = true;
        public Dictionary<string, object> AdvancedSettings { get; set; } = new();
    }

    /// <summary>
    /// 插件依赖
    /// </summary>
    public class PluginDependency
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string? InstalledVersion { get; set; }
        public bool IsSatisfied { get; set; } = false;
        public bool IsOptional { get; set; } = false;
        public string? Source { get; set; }
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 插件验证结果
    /// </summary>
    public class PluginValidationResult
    {
        public bool IsValid { get; set; } = false;
        public List<string> Errors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> ValidationData { get; set; } = new();
        public DateTime ValidatedAt { get; set; } = DateTime.Now;
    }

    // ==================== 性能优化与监控相关模型 ====================

    /// <summary>
    /// 系统性能指标
    /// </summary>
    public class SystemPerformanceMetrics
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public float CpuUsage { get; set; }
        public float MemoryUsage { get; set; }
        public float DiskUsage { get; set; }
        public float NetworkUsage { get; set; }
        public float GpuUsage { get; set; }
        public float GpuMemoryUsage { get; set; }
        public float PerformanceScore { get; set; }
        public Dictionary<string, object> ApplicationMetrics { get; set; } = new();
        public Dictionary<string, float> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 性能警报
    /// </summary>
    public class PerformanceAlert
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public AlertType Type { get; set; }
        public AlertSeverity Severity { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public Dictionary<string, object> Context { get; set; } = new();
        public bool IsAcknowledged { get; set; } = false;
        public DateTime? AcknowledgedAt { get; set; }
    }

    /// <summary>
    /// 警报类型
    /// </summary>
    public enum AlertType
    {
        HighCpuUsage,
        HighMemoryUsage,
        HighGpuUsage,
        LowDiskSpace,
        HighNetworkUsage,
        SystemOverload,
        ApplicationError,
        PerformanceDegradation
    }

    /// <summary>
    /// 警报严重程度
    /// </summary>
    public enum AlertSeverity
    {
        Info,
        Warning,
        Critical,
        Emergency
    }

    /// <summary>
    /// 优化建议
    /// </summary>
    public class OptimizationRecommendation
    {
        public DateTime GeneratedAt { get; set; }
        public List<OptimizationSuggestion> Recommendations { get; set; } = new();
        public Dictionary<string, float> EstimatedImpact { get; set; } = new();
        public float OverallPotentialImprovement { get; set; }
        public Dictionary<string, object> AnalysisData { get; set; } = new();
    }

    /// <summary>
    /// 优化建议
    /// </summary>
    public class OptimizationSuggestion
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public OptimizationPriority Priority { get; set; }
        public OptimizationType Type { get; set; }
        public string EstimatedImpact { get; set; } = string.Empty;
        public List<string> Steps { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
        public bool AutoApplicable { get; set; } = false;
    }

    /// <summary>
    /// 优化优先级
    /// </summary>
    public enum OptimizationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// 优化类型
    /// </summary>
    public enum OptimizationType
    {
        Performance,    // 性能优化
        Memory,         // 内存优化
        Quality,        // 质量优化
        Efficiency,     // 效率优化
        Security,       // 安全优化
        Usability,      // 可用性优化
        Maintenance     // 维护性优化
    }

    /// <summary>
    /// 资源使用报告
    /// </summary>
    public class ResourceUsageReport
    {
        public TimeSpan Period { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public float AverageCpuUsage { get; set; }
        public float AverageMemoryUsage { get; set; }
        public float AverageGpuUsage { get; set; }
        public float PeakCpuUsage { get; set; }
        public float PeakMemoryUsage { get; set; }
        public float PeakGpuUsage { get; set; }
        public Dictionary<string, object> UsagePatterns { get; set; } = new();
        public List<ResourceUsageEvent> Events { get; set; } = new();
        public Dictionary<string, float> Trends { get; set; } = new();
    }

    /// <summary>
    /// 资源使用事件
    /// </summary>
    public class ResourceUsageEvent
    {
        public DateTime Timestamp { get; set; }
        public string EventType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Dictionary<string, object> Data { get; set; } = new();
    }

    /// <summary>
    /// 调度任务
    /// </summary>
    public class ScheduledTask
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public TaskPriority Priority { get; set; } = TaskPriority.Medium;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime ScheduledExecutionTime { get; set; }
        public TaskStatus Status { get; set; } = TaskStatus.Pending;
        public DateTime? StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public Func<Task>? ExecuteAsync { get; set; }
        public CancellationTokenSource? CancellationToken { get; set; }
        public TimeSpan EstimatedDuration { get; set; }
        public List<string> Dependencies { get; set; } = new();
    }

    /// <summary>
    /// 任务优先级
    /// </summary>
    public enum TaskPriority
    {
        Low = 1,
        Medium = 2,
        High = 3,
        Critical = 4
    }

    /// <summary>
    /// 任务状态
    /// </summary>
    public enum TaskStatus
    {
        Pending,
        Scheduled,
        Running,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 任务调度结果
    /// </summary>
    public class TaskSchedulingResult
    {
        public string TaskId { get; set; } = string.Empty;
        public bool Success { get; set; } = false;
        public DateTime ScheduledAt { get; set; }
        public DateTime OptimalExecutionTime { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> SchedulingData { get; set; } = new();
    }

    /// <summary>
    /// 性能调优请求
    /// </summary>
    public class PerformanceTuningRequest
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public List<string> TuningStrategies { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public List<string> TargetMetrics { get; set; } = new();
        public float TargetImprovement { get; set; } = 0.1f;
        public TimeSpan MaxTuningTime { get; set; } = TimeSpan.FromMinutes(30);
    }

    /// <summary>
    /// 性能调优结果
    /// </summary>
    public class PerformanceTuningResult
    {
        public string RequestId { get; set; } = string.Empty;
        public bool Success { get; set; } = false;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<string> AppliedOptimizations { get; set; } = new();
        public PerformanceImprovement PerformanceImprovement { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> TuningData { get; set; } = new();
    }

    /// <summary>
    /// 性能改进
    /// </summary>
    public class PerformanceImprovement
    {
        public float CpuImprovement { get; set; }
        public float MemoryImprovement { get; set; }
        public float GpuImprovement { get; set; }
        public float OverallImprovement { get; set; }
        public Dictionary<string, float> CustomMetricImprovements { get; set; } = new();
    }

    /// <summary>
    /// 性能指标快照
    /// </summary>
    public class PerformanceMetricSnapshot
    {
        public DateTime Timestamp { get; set; }
        public float CpuUsage { get; set; }
        public float MemoryUsage { get; set; }
        public float GpuUsage { get; set; }
        public float DiskUsage { get; set; }
        public float NetworkUsage { get; set; }
        public Dictionary<string, float> CustomMetrics { get; set; } = new();
    }

    /// <summary>
    /// 性能趋势
    /// </summary>
    public class PerformanceTrends
    {
        public float CpuTrend { get; set; }
        public float MemoryTrend { get; set; }
        public float GpuTrend { get; set; }
        public float DiskTrend { get; set; }
        public float NetworkTrend { get; set; }
        public Dictionary<string, float> CustomTrends { get; set; } = new();
        public TrendDirection OverallTrend { get; set; }
    }

    /// <summary>
    /// 趋势方向
    /// </summary>
    public enum TrendDirection
    {
        Improving,
        Stable,
        Degrading,
        Unknown
    }

    /// <summary>
    /// 系统监控指标
    /// </summary>
    public class SystemMetrics
    {
        public float CpuUsage { get; set; }
        public float MemoryUsage { get; set; }
        public float DiskUsage { get; set; }
        public float NetworkUsage { get; set; }
        public float GpuUsage { get; set; }
        public float GpuMemoryUsage { get; set; }
        public long AvailableMemory { get; set; }
        public long TotalMemory { get; set; }
        public long AvailableDiskSpace { get; set; }
        public long TotalDiskSpace { get; set; }
        public int ProcessCount { get; set; }
        public int ThreadCount { get; set; }
        public float Temperature { get; set; }
        public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
    }


}

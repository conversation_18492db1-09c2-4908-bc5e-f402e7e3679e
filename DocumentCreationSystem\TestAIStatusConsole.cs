using System;
using System.Threading.Tasks;

namespace DocumentCreationSystem
{
    /// <summary>
    /// AI服务状态诊断控制台程序
    /// </summary>
    class TestAIStatusConsole
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("AI服务状态诊断工具");
            Console.WriteLine("==================");
            Console.WriteLine();

            try
            {
                await AIServiceStatusDiagnostic.RunDiagnosticAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"程序执行失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
